﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\comprehensiveviewwidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\configmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\databasemanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\geographicdatapanel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\geoinfowidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\localtilemanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\qgismapwidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\qtgeoinfowidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\qtlocationmapview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\signalanalysiswidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\spectrumplot_simple.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\tilemapview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\waterfallplot_simple.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\code\LiteAPPStar\include\comprehensiveviewwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\include\configmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\include\databasemanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\include\geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\include\mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\geoinfowidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\localtilemanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\qgismapwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\qtgeoinfowidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\qtlocationmapview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\signalanalysiswidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\spectrumplot_simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\tilemapview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\src\waterfallplot_simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar\build\CMakeFiles\6ced8156163e1d06b8bc9dabf4ea54b5\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\code\LiteAPPStar\build\CMakeFiles\a949e9403c7fc38777131ca2bcb1be2d\qrc_resources.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\code\LiteAPPStar\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\code\LiteAPPStar\src\comprehensive_view.ui" />
    <None Include="E:\code\LiteAPPStar\src\geographicdatapanel.ui" />
    <None Include="E:\code\LiteAPPStar\src\mainwindow.ui" />
    <None Include="E:\code\LiteAPPStar\src\signalanalysis.ui" />
    <None Include="E:\code\LiteAPPStar\resources\resources.qrc" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Debug.stamp" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Release.stamp" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_MinSizeRel.stamp" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{7320B4C8-02F9-38DC-A561-B5B3877688CE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{78A14667-4DF8-36EA-AC51-41AB89B534FF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{3AECDC13-BC76-3C53-ABEF-466753A2E9DE}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
