﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\comprehensiveviewwidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\configmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\databasemanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\geographicdatapanel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\geoinfowidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\localtilemanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\qgismapwidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\qtgeoinfowidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\qtlocationmapview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\signalanalysiswidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\spectrumplot_simple.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\tilemapview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\src\waterfallplot_simple.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\code\LiteAPPStar2\include\comprehensiveviewwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\include\configmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\include\databasemanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\include\geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\include\mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\geoinfowidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\localtilemanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\qgismapwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\qtgeoinfowidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\qtlocationmapview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\signalanalysiswidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\spectrumplot_simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\tilemapview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\src\waterfallplot_simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Debug\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Debug\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Debug\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Debug\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Release\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Release\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Release\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_Release\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_comprehensive_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_geographicdatapanel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_signalanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar2\build\CMakeFiles\9f4df36830bf5b6c9232ab610f73c87c\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\code\LiteAPPStar2\build\CMakeFiles\ead14719a397a823260d11ba753b83fc\qrc_resources.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\code\LiteAPPStar2\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\code\LiteAPPStar2\src\comprehensive_view.ui" />
    <None Include="E:\code\LiteAPPStar2\src\geographicdatapanel.ui" />
    <None Include="E:\code\LiteAPPStar2\src\mainwindow.ui" />
    <None Include="E:\code\LiteAPPStar2\src\signalanalysis.ui" />
    <None Include="E:\code\LiteAPPStar2\resources\resources.qrc" />
    <None Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\autouic_Debug.stamp" />
    <None Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\autouic_Release.stamp" />
    <None Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\autouic_MinSizeRel.stamp" />
    <None Include="E:\code\LiteAPPStar2\build\LiteAPPStar_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{EB33C064-2FCC-3ACA-8B93-B482D61B499D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{0B462042-E293-3210-8042-6BBDB3381FBE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{7A8E0C39-2A4F-38F5-A624-C4341C58FBBA}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
