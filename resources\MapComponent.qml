import QtQuick 2.12
import QtLocation 5.12
import QtPositioning 5.12

Rectangle {
    id: mapContainer
    width: 800
    height: 600
    color: "#f0f0f0"
    
    // 地图属性
    property alias center: map.center
    property alias zoomLevel: map.zoomLevel
    property alias mapType: map.activeMapType
    
    // 标记管理
    property var markers: []
    property int nextMarkerId: 1
    
    // 信号定义
    signal mapClicked(real latitude, real longitude)
    signal mapDoubleClicked(real latitude, real longitude)
    signal centerChanged(real latitude, real longitude)
    signal zoomChanged(real zoom)
    
    // 地图组件
    Map {
        id: map
        anchors.fill: parent
        
        // 默认插件和中心点
        plugin: Plugin {
            id: mapPlugin
            name: "osm" // OpenStreetMap
            
            // 插件参数
            PluginParameter {
                name: "osm.mapping.providersrepository.disabled"
                value: true
            }
            
            PluginParameter {
                name: "osm.mapping.offline.directory"
                value: "."
            }
        }
        
        // 默认中心点（北京天安门）
        center: QtPositioning.coordinate(39.9042, 116.4074)
        zoomLevel: 10
        
        // 地图手势
        gesture.enabled: true
        gesture.acceptedGestures: MapGestureArea.PinchGesture | MapGestureArea.PanGesture | MapGestureArea.FlickGesture
        
        // 地图点击事件
        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton | Qt.RightButton
            
            onClicked: {
                var coordinate = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                mapContainer.mapClicked(coordinate.latitude, coordinate.longitude)
                
                // 右键添加标记
                if (mouse.button === Qt.RightButton) {
                    addMarker(coordinate.latitude, coordinate.longitude, "标记 " + nextMarkerId, "右键添加的标记")
                }
            }
            
            onDoubleClicked: {
                var coordinate = map.toCoordinate(Qt.point(mouse.x, mouse.y))
                mapContainer.mapDoubleClicked(coordinate.latitude, coordinate.longitude)
            }
        }
        
        // 监听中心点变化
        onCenterChanged: {
            mapContainer.centerChanged(center.latitude, center.longitude)
        }
        
        // 监听缩放变化
        onZoomLevelChanged: {
            mapContainer.zoomChanged(zoomLevel)
        }
        
        // 地图项目组
        MapItemView {
            model: markersModel
            delegate: MapQuickItem {
                coordinate: QtPositioning.coordinate(model.latitude, model.longitude)
                anchorPoint.x: markerImage.width / 2
                anchorPoint.y: markerImage.height
                
                sourceItem: Column {
                    Image {
                        id: markerImage
                        source: "data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24'><path fill='red' d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/></svg>"
                        width: 32
                        height: 32
                    }
                    
                    Rectangle {
                        width: titleText.width + 10
                        height: titleText.height + 6
                        color: "white"
                        border.color: "black"
                        border.width: 1
                        radius: 3
                        visible: model.showLabel
                        
                        Text {
                            id: titleText
                            anchors.centerIn: parent
                            text: model.title
                            font.pixelSize: 12
                            color: "black"
                        }
                    }
                }
                
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("标记被点击:", model.title)
                        model.showLabel = !model.showLabel
                    }
                }
            }
        }
    }
    
    // 标记数据模型
    ListModel {
        id: markersModel
    }
    
    // 地图控制方法
    function setCenter(latitude, longitude) {
        map.center = QtPositioning.coordinate(latitude, longitude)
    }
    
    function setZoomLevel(zoom) {
        map.zoomLevel = zoom
    }
    
    function setMapType(typeName) {
        var supportedTypes = map.supportedMapTypes
        for (var i = 0; i < supportedTypes.length; i++) {
            if (supportedTypes[i].name.toLowerCase().indexOf(typeName.toLowerCase()) >= 0) {
                map.activeMapType = supportedTypes[i]
                break
            }
        }
    }
    
    // 标记管理方法
    function addMarker(latitude, longitude, title, description) {
        var markerId = nextMarkerId++
        markersModel.append({
            "id": markerId,
            "latitude": latitude,
            "longitude": longitude,
            "title": title || ("标记 " + markerId),
            "description": description || "",
            "showLabel": true
        })
        
        console.log("添加标记:", markerId, latitude, longitude, title)
        return markerId
    }
    
    function removeMarker(markerId) {
        for (var i = 0; i < markersModel.count; i++) {
            if (markersModel.get(i).id === markerId) {
                markersModel.remove(i)
                console.log("移除标记:", markerId)
                break
            }
        }
    }
    
    function clearMarkers() {
        markersModel.clear()
        console.log("清除所有标记")
    }
    
    function getMarkers() {
        var result = []
        for (var i = 0; i < markersModel.count; i++) {
            var marker = markersModel.get(i)
            result.push({
                id: marker.id,
                latitude: marker.latitude,
                longitude: marker.longitude,
                title: marker.title,
                description: marker.description
            })
        }
        return result
    }
    
    // 视图控制方法
    function fitViewportToMapItems() {
        if (markersModel.count === 0) {
            return
        }
        
        map.fitViewportToMapItems()
    }
    
    function zoomIn() {
        map.zoomLevel = Math.min(map.zoomLevel + 1, map.maximumZoomLevel)
    }
    
    function zoomOut() {
        map.zoomLevel = Math.max(map.zoomLevel - 1, map.minimumZoomLevel)
    }
    
    // 状态指示器
    Rectangle {
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 10
        width: statusText.width + 20
        height: statusText.height + 10
        color: "white"
        border.color: "gray"
        border.width: 1
        radius: 5
        opacity: 0.9
        
        Text {
            id: statusText
            anchors.centerIn: parent
            text: "中心: " + map.center.latitude.toFixed(6) + ", " + map.center.longitude.toFixed(6) + 
                  "\n缩放: " + map.zoomLevel.toFixed(1) + 
                  "\n标记: " + markersModel.count
            font.pixelSize: 10
            color: "black"
        }
    }
    
    // 加载指示器
    Rectangle {
        anchors.centerIn: parent
        width: 200
        height: 50
        color: "white"
        border.color: "gray"
        border.width: 1
        radius: 5
        visible: !map.mapReady
        
        Text {
            anchors.centerIn: parent
            text: "地图加载中..."
            font.pixelSize: 14
            color: "black"
        }
    }
    
    // 组件完成时的初始化
    Component.onCompleted: {
        console.log("地图组件初始化完成")
        console.log("支持的地图类型:", map.supportedMapTypes.length)
        
        // 添加一个默认标记
        addMarker(39.9042, 116.4074, "天安门", "北京市中心")
    }
}
