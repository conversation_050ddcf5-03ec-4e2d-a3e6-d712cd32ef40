#############################################################################
# Makefile for building: LiteAPPStar_debug
# Generated by qmake (3.1) (Qt 5.14.2)
# Project:  LiteAPPStar.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DDEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -Wall -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -Iinclude -Ibuild -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include\QtWidgets -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include\QtGui -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include\QtANGLE -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include\QtNetwork -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\include\QtCore -Ibuild\moc -Ibuild\ui -ID:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\libQt5Widgets.a D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\libQt5Gui.a D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\libQt5Network.a D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\libQt5Core.a E:\code\LiteAPPStar\build\obj\app_res.o  -lmingw32 D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-win32\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = E:\code\LiteAPPStar\build\obj\app_res.o
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = build\obj

####### Files

SOURCES       = src\main.cpp \
		src\mainwindow.cpp \
		src\signalanalysiswidget.cpp \
		src\qgismapwidget.cpp \
		src\tilemapview.cpp \
		src\localtilemanager.cpp \
		src\spectrumplot_simple.cpp \
		src\waterfallplot_simple.cpp build\rcc\qrc_resources.cpp \
		build\moc\moc_mainwindow.cpp \
		build\moc\moc_signalanalysiswidget.cpp \
		build\moc\moc_qgismapwidget.cpp \
		build\moc\moc_tilemapview.cpp \
		build\moc\moc_localtilemanager.cpp \
		build\moc\moc_spectrumplot_simple.cpp \
		build\moc\moc_waterfallplot_simple.cpp
OBJECTS       = build/obj/main.o \
		build/obj/mainwindow.o \
		build/obj/signalanalysiswidget.o \
		build/obj/qgismapwidget.o \
		build/obj/tilemapview.o \
		build/obj/localtilemanager.o \
		build/obj/spectrumplot_simple.o \
		build/obj/waterfallplot_simple.o \
		build/obj/qrc_resources.o \
		build/obj/moc_mainwindow.o \
		build/obj/moc_signalanalysiswidget.o \
		build/obj/moc_qgismapwidget.o \
		build/obj/moc_tilemapview.o \
		build/obj/moc_localtilemanager.o \
		build/obj/moc_spectrumplot_simple.o \
		build/obj/moc_waterfallplot_simple.o

DIST          =  include\mainwindow.h \
		src\signalanalysiswidget.h \
		src\qgismapwidget.h \
		src\tilemapview.h \
		src\localtilemanager.h \
		src\spectrumplot_simple.h \
		src\waterfallplot_simple.h src\main.cpp \
		src\mainwindow.cpp \
		src\signalanalysiswidget.cpp \
		src\qgismapwidget.cpp \
		src\tilemapview.cpp \
		src\localtilemanager.cpp \
		src\spectrumplot_simple.cpp \
		src\waterfallplot_simple.cpp
QMAKE_TARGET  = LiteAPPStar_debug
DESTDIR        = bin\ #avoid trailing-slash linebreak
TARGET         = LiteAPPStar_debug.exe
DESTDIR_TARGET = bin\LiteAPPStar_debug.exe

####### Build rules

first: all
all: Makefile.Debug  bin/LiteAPPStar_debug.exe

bin/LiteAPPStar_debug.exe: D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Widgets.a D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Gui.a D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Network.a D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Core.a D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libqtmain.a build/ui/ui_mainwindow.h build/ui/ui_signalanalysis.h $(OBJECTS) E:/code/LiteAPPStar/build/obj/app_res.o
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.LiteAPPStar_debug.Debug  $(LIBS)

E:/code/LiteAPPStar/build/obj/app_res.o: resources/app.rc
	windres -i resources\app.rc -o E:\code\LiteAPPStar\build\obj\app_res.o --include-dir=./resources $(DEFINES)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug LiteAPPStar.pro

qmake_all: FORCE

dist:
	$(ZIP) LiteAPPStar_debug.zip $(SOURCES) $(DIST) LiteAPPStar.pro D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\spec_pre.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\qdevice.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\device_config.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\sanitize.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\gcc-base.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\g++-base.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\angle.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\win32\windows_vulkan_sdk.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\windows-vulkan.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\g++-win32.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\common\windows-desktop.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\qconfig.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3danimation.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3danimation_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dcore.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dcore_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dextras.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dextras_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dinput.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dinput_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dlogic_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquick.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquick_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickanimation_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickextras_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3dquickscene2d_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3drender.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_3drender_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_accessibility_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axbase.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axbase_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axcontainer_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axserver.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_axserver_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_bluetooth_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_bodymovin_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_bootstrap_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_charts.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_charts_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_concurrent.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_concurrent_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_core.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_core_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_datavisualization_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_dbus.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_dbus_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_designer.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_designer_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_edid_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_egl_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_fb_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_fontdatabase_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_gamepad.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_gamepad_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_gui.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_gui_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_help.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_help_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_location.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_location_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_multimedia.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_multimedia_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_network.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_network_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_networkauth.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_networkauth_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_nfc.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_nfc_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_opengl.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_opengl_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_openglextensions_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_platformcompositor_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_positioning.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_positioning_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_positioningquick_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_printsupport.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_printsupport_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_purchasing.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_purchasing_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qml.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qml_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmldebug_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmlmodels.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmltest.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmltest_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3d.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3d_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3drender.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3drender_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3dutils.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quick_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickshapes_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_remoteobjects_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_repparser.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_repparser_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_script.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_script_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_scripttools.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_scripttools_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_scxml.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_scxml_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_sensors.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_sensors_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_serialbus.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_serialbus_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_serialport.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_serialport_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_sql.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_sql_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_svg.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_svg_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_testlib.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_testlib_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_texttospeech_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_theme_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_uiplugin.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_uitools.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_uitools_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_virtualkeyboard_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_webchannel.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_webchannel_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_websockets.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_websockets_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_widgets.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_widgets_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_winextras.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_winextras_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_xml.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_xml_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\modules\qt_lib_zlib_private.pri D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\qt_functions.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\qt_config.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\win32-g++\qmake.conf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\spec_post.prf .qmake.stash D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\exclusive_builds.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\toolchain.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\default_pre.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\win32\default_pre.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\resolve_config.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\exclusive_builds_post.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\default_post.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\build_pass.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\warn_on.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\precompile_header.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\qt.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\resources_functions.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\resources.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\moc.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\win32\opengl.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\uic.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\qmake_use.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\file_copies.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\win32\windows.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\testcase_targets.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\exceptions.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\yacc.prf D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\lex.prf LiteAPPStar.pro resources\resources.qrc D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\Qt5Widgets.prl D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\Qt5Gui.prl D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\Qt5Network.prl D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\Qt5Core.prl D:\Qt\Qt5.14.2\5.14.2\mingw73_32\lib\qtmain.prl   resources\resources.qrc D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\data\dummy.cpp include\mainwindow.h src\signalanalysiswidget.h src\qgismapwidget.h src\tilemapview.h src\localtilemanager.h src\spectrumplot_simple.h src\waterfallplot_simple.h  src\main.cpp src\mainwindow.cpp src\signalanalysiswidget.cpp src\qgismapwidget.cpp src\tilemapview.cpp src\localtilemanager.cpp src\spectrumplot_simple.cpp src\waterfallplot_simple.cpp src\mainwindow.ui src\signalanalysis.ui    

clean: compiler_clean 
	-$(DEL_FILE) build\obj\main.o build\obj\mainwindow.o build\obj\signalanalysiswidget.o build\obj\qgismapwidget.o build\obj\tilemapview.o build\obj\localtilemanager.o build\obj\spectrumplot_simple.o build\obj\waterfallplot_simple.o build\obj\qrc_resources.o build\obj\moc_mainwindow.o build\obj\moc_signalanalysiswidget.o build\obj\moc_qgismapwidget.o build\obj\moc_tilemapview.o build\obj\moc_localtilemanager.o build\obj\moc_spectrumplot_simple.o build\obj\moc_waterfallplot_simple.o
	-$(DEL_FILE) E:\code\LiteAPPStar\bin\LiteAPPStar_debug*
	-$(DEL_FILE) E:\code\LiteAPPStar\build\obj\app_res.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: build/rcc/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) build\rcc\qrc_resources.cpp
build/rcc/qrc_resources.cpp: resources/resources.qrc \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/rcc.exe \
		resources/MapComponent.qml \
		resources/images/logo.png \
		resources/images/splash.png \
		resources/styles/default.qss \
		resources/styles/dark.qss \
		resources/translations/liteappstar_zh_CN.qm \
		resources/translations/liteappstar_en_US.qm \
		resources/icons/save.png \
		resources/icons/open.png \
		resources/icons/cut.png \
		resources/icons/app_icon.png \
		resources/icons/copy.png \
		resources/icons/exit.png \
		resources/icons/satellite.svg \
		resources/icons/new.png \
		resources/icons/paste.png
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\rcc.exe -name resources resources\resources.qrc -o build\rcc\qrc_resources.cpp

compiler_moc_predefs_make_all: build/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build\moc\moc_predefs.h
build/moc/moc_predefs.h: D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -Wall -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o build\moc\moc_predefs.h D:\Qt\Qt5.14.2\5.14.2\mingw73_32\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: build/moc/moc_mainwindow.cpp build/moc/moc_signalanalysiswidget.cpp build/moc/moc_qgismapwidget.cpp build/moc/moc_tilemapview.cpp build/moc/moc_localtilemanager.cpp build/moc/moc_spectrumplot_simple.cpp build/moc/moc_waterfallplot_simple.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build\moc\moc_mainwindow.cpp build\moc\moc_signalanalysiswidget.cpp build\moc\moc_qgismapwidget.cpp build\moc\moc_tilemapview.cpp build\moc\moc_localtilemanager.cpp build\moc\moc_spectrumplot_simple.cpp build\moc\moc_waterfallplot_simple.cpp
build/moc/moc_mainwindow.cpp: include/mainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenuBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QToolBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include include\mainwindow.h -o build\moc\moc_mainwindow.cpp

build/moc/moc_signalanalysiswidget.cpp: src/signalanalysiswidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QFrame \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QShortcut \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QKeySequence \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		src/spectrumplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		src/waterfallplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\signalanalysiswidget.h -o build\moc\moc_signalanalysiswidget.cpp

build/moc/moc_qgismapwidget.cpp: src/qgismapwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLineEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTextEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTabWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QListWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTreeWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPair \
		src/tilemapview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsScene \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsPixmapItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QHash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPoint \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\qgismapwidget.h -o build\moc\moc_qgismapwidget.cpp

build/moc/moc_tilemapview.cpp: src/tilemapview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsScene \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsPixmapItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QHash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPoint \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\tilemapview.h -o build\moc\moc_tilemapview.cpp

build/moc/moc_localtilemanager.cpp: src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\localtilemanager.h -o build\moc\moc_localtilemanager.cpp

build/moc/moc_spectrumplot_simple.cpp: src/spectrumplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\spectrumplot_simple.h -o build\moc\moc_spectrumplot_simple.cpp

build/moc/moc_waterfallplot_simple.cpp: src/waterfallplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		build/moc/moc_predefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe $(DEFINES) --include E:/code/LiteAPPStar/build/moc/moc_predefs.h -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -IE:/code/LiteAPPStar -IE:/code/LiteAPPStar/include -IE:/code/LiteAPPStar/build -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork -ID:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include src\waterfallplot_simple.h -o build\moc\moc_waterfallplot_simple.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: build/ui/ui_mainwindow.h build/ui/ui_signalanalysis.h
compiler_uic_clean:
	-$(DEL_FILE) build\ui\ui_mainwindow.h build\ui\ui_signalanalysis.h
build/ui/ui_mainwindow.h: src/mainwindow.ui \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/uic.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\uic.exe src\mainwindow.ui -o build\ui\ui_mainwindow.h

build/ui/ui_signalanalysis.h: src/signalanalysis.ui \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/uic.exe
	D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\uic.exe src\signalanalysis.ui -o build\ui\ui_signalanalysis.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

build/obj/main.o: src/main.cpp D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStyleFactory \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFileInfo \
		include/mainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenuBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QToolBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\main.o src\main.cpp

build/obj/mainwindow.o: src/mainwindow.cpp include/mainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenuBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenubar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QToolBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtoolbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		build/ui/ui_mainwindow.h \
		src/signalanalysiswidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QFrame \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QShortcut \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QKeySequence \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		src/spectrumplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		src/waterfallplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		src/qgismapwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLineEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTextEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTabWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QListWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTreeWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPair \
		src/tilemapview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsScene \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsPixmapItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QHash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPoint \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		include/comprehensiveviewwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDateTimeEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdatetimeedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcalendar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QStandardItemModel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qstandarditemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSortFilterProxyModel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractproxymodel.h \
		include/databasemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariantMap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariantList \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStringList \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMutex \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QQueue \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QThread \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qthread.h \
		include/configmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonParseError \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFile \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDialog \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QDragEnterEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QDropEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMimeData \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmimedata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QCloseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStackedWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstackedwidget.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\mainwindow.o src\mainwindow.cpp

build/obj/signalanalysiswidget.o: src/signalanalysiswidget.cpp src/signalanalysiswidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QFrame \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QShortcut \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qshortcut.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QKeySequence \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		src/spectrumplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		src/waterfallplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		build/ui/ui_signalanalysis.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QtMath \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\signalanalysiswidget.o src\signalanalysiswidget.cpp

build/obj/qgismapwidget.o: src/qgismapwidget.cpp src/qgismapwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLineEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlineedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTextEdit \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtextedit.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextdocument.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTabWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStatusBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstatusbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QListWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlistview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qitemselectionmodel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTreeWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreeview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtablewidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtableview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTableWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHeaderView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qheaderview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPair \
		src/tilemapview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsScene \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsPixmapItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QHash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPoint \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		include/databasemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariantMap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariantList \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStringList \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMutex \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QQueue \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qqueue.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QThread \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qthread.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		include/configmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsonobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsonvalue.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonDocument \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qjsondocument.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QJsonParseError \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFile \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QCoreApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		include/geographicdatapanel.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPropertyAnimation \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpropertyanimation.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariantanimation.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeasingcurve.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractanimation.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsOpacityEffect \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicseffect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QFileDialog \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qfiledialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QTreeWidgetItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QtMath \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QInputDialog \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qinputdialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QLocale \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QResizeEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QFrame \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDialog \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QDoubleValidator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QScrollArea \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\qgismapwidget.o src\qgismapwidget.cpp

build/obj/tilemapview.o: src/tilemapview.cpp src/tilemapview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsView \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsScene \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGraphicsPixmapItem \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QHash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QPoint \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QScrollBar \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollbar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QtMath \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFile \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFileInfo \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\tilemapview.o src\tilemapview.cpp

build/obj/localtilemanager.o: src/localtilemanager.cpp src/localtilemanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkAccessManager \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkaccessmanager.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetworkglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtnetwork-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkrequest.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSharedDataPointer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QUrl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVariant \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslConfiguration \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslconfiguration.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qtcpsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qabstractsocket.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslerror.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslcertificate.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcryptographichash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qssl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFlags \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMetaType \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkReply \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/qnetworkreply.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QIODevice \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtNetwork/QNetworkRequest \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QStandardPaths \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstandardpaths.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QFileInfo
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\localtilemanager.o src\localtilemanager.cpp

build/obj/spectrumplot_simple.o: src/spectrumplot_simple.cpp src/spectrumplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QContextMenuEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\spectrumplot_simple.o src\spectrumplot_simple.cpp

build/obj/waterfallplot_simple.o: src/waterfallplot_simple.cpp src/waterfallplot_simple.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QMouseEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QWheelEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFont \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QFontMetrics \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMenu \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmenu.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qaction.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qactiongroup.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QAction \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDateTime \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatetime.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QContextMenuEvent \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\waterfallplot_simple.o src\waterfallplot_simple.cpp

build/obj/qrc_resources.o: build/rcc/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\qrc_resources.o build\rcc\qrc_resources.cpp

build/obj/moc_mainwindow.o: build/moc/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_mainwindow.o build\moc\moc_mainwindow.cpp

build/obj/moc_signalanalysiswidget.o: build/moc/moc_signalanalysiswidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_signalanalysiswidget.o build\moc\moc_signalanalysiswidget.cpp

build/obj/moc_qgismapwidget.o: build/moc/moc_qgismapwidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_qgismapwidget.o build\moc\moc_qgismapwidget.cpp

build/obj/moc_tilemapview.o: build/moc/moc_tilemapview.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_tilemapview.o build\moc\moc_tilemapview.cpp

build/obj/moc_localtilemanager.o: build/moc/moc_localtilemanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_localtilemanager.o build\moc\moc_localtilemanager.cpp

build/obj/moc_spectrumplot_simple.o: build/moc/moc_spectrumplot_simple.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_spectrumplot_simple.o build\moc\moc_spectrumplot_simple.cpp

build/obj/moc_waterfallplot_simple.o: build/moc/moc_waterfallplot_simple.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build\obj\moc_waterfallplot_simple.o build\moc\moc_waterfallplot_simple.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

