#define _USE_MATH_DEFINES
#include "tilemapview.h"
#include "localtilemanager.h"
#include <QApplication>
#include <QDebug>
#include <QScrollBar>
#include <QPainter>
#include <QtMath>
#include <QMessageBox>
#include <QStandardPaths>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QTimer>
#include <QDateTime>
#include <algorithm>
#include <cmath>

TileMapView::TileMapView(QWidget *parent)
    : QGraphicsView(parent), m_scene(nullptr), m_networkManager(nullptr), m_updateTimer(nullptr), m_tileManager(nullptr), m_centerLat(35.0), m_centerLng(104.0), m_zoomLevel(4), m_dragging(false)
{
    initializeMap();
}

TileMapView::~TileMapView()
{
    // 取消所有网络请求
    for (auto it = m_pendingTiles.begin(); it != m_pendingTiles.end(); ++it)
    {
        it.key()->abort();
        it.key()->deleteLater();
    }
    m_pendingTiles.clear();
}

void TileMapView::initializeMap()
{
    qDebug() << "TileMapView: 初始化地图";

    // 创建场景
    m_scene = new QGraphicsScene(this);
    setScene(m_scene);

    // 设置视图属性
    setDragMode(QGraphicsView::RubberBandDrag);           // 允许拖拽
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff); // 隐藏水平滚动条
    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);   // 隐藏垂直滚动条
    setRenderHint(QPainter::Antialiasing, true);
    setRenderHint(QPainter::SmoothPixmapTransform, true);

    // 设置视图自适应内容
    setResizeAnchor(QGraphicsView::AnchorViewCenter);
    setTransformationAnchor(QGraphicsView::AnchorViewCenter);

    // 启用鼠标跟踪
    setMouseTracking(true);

    // 设置背景色为浅灰色，便于调试
    setBackgroundBrush(QBrush(QColor(240, 240, 240)));

    // 创建网络管理器（暂时禁用网络功能）
    m_networkManager = nullptr; // new QNetworkAccessManager(this);

    // 创建本地瓦片管理器
    m_tileManager = new LocalTileManager(this);
    connect(m_tileManager, &LocalTileManager::tileDownloaded, this, &TileMapView::onLocalTileDownloaded);
    connect(m_tileManager, &LocalTileManager::downloadProgress, this, &TileMapView::onDownloadProgress);

    // 创建更新定时器
    m_updateTimer = new QTimer(this);
    m_updateTimer->setSingleShot(true);
    m_updateTimer->setInterval(100); // 减少延迟，提高响应性
    connect(m_updateTimer, &QTimer::timeout, this, &TileMapView::updateVisibleTiles);

    qDebug() << "TileMapView: 初始化完成，中心坐标:" << m_centerLat << m_centerLng << "缩放级别:" << m_zoomLevel;
    qDebug() << "TileMapView: 当前视图大小:" << size() << "场景矩形:" << m_scene->sceneRect();

    // 添加一个测试矩形，确保场景正常工作
    QGraphicsRectItem *testRect = m_scene->addRect(100, 100, 200, 200, QPen(Qt::red), QBrush(Qt::yellow));
    qDebug() << "TileMapView: 添加测试矩形到场景";

    // 初始化视图
    updateView();

    // 加载可见瓦片
    qDebug() << "TileMapView: 开始加载地图瓦片，中心:" << m_centerLat << m_centerLng << "缩放级别:" << m_zoomLevel;
    loadVisibleTiles();

    // 启动更新定时器
    m_updateTimer->start();
}

void TileMapView::setCenter(double lat, double lng)
{
    // 经度循环处理：确保经度在-180到180度范围内
    while (lng > 180.0)
        lng -= 360.0;
    while (lng < -180.0)
        lng += 360.0;

    // 纬度边界限制：Web Mercator投影的有效范围
    lat = qBound(-85.0511, lat, 85.0511);

    if (m_centerLat != lat || m_centerLng != lng)
    {
        m_centerLat = lat;
        m_centerLng = lng;
        updateView();
        m_updateTimer->start();
        emit centerChanged(lat, lng);
    }
}

void TileMapView::setZoomLevel(int zoom)
{
    zoom = qBound(MIN_ZOOM, zoom, MAX_ZOOM);
    if (m_zoomLevel != zoom)
    {
        m_zoomLevel = zoom;

        // 清除当前瓦片
        m_scene->clear();
        m_tiles.clear();

        updateView();
        m_updateTimer->start();
        emit zoomChanged(zoom);
    }
}

QPointF TileMapView::latLngToPixel(double lat, double lng) const
{
    // Web Mercator投影 - 使用高精度计算
    // 确保使用双精度浮点数进行所有计算
    const double DEGREES_TO_RADIANS = M_PI / 180.0;
    const double RADIANS_TO_DEGREES = 180.0 / M_PI;

    // 经度循环处理：确保经度在-180到180度范围内
    while (lng > 180.0)
        lng -= 360.0;
    while (lng < -180.0)
        lng += 360.0;

    // 经度转换为像素X坐标
    double x = (lng + 180.0) / 360.0 * (1LL << m_zoomLevel) * TILE_SIZE;

    // 纬度转换为像素Y坐标（Web Mercator投影）
    double latRad = lat * DEGREES_TO_RADIANS;

    // 处理极地情况，避免数值溢出
    if (lat >= 85.0511)
    {
        latRad = 85.0511 * DEGREES_TO_RADIANS;
    }
    else if (lat <= -85.0511)
    {
        latRad = -85.0511 * DEGREES_TO_RADIANS;
    }

    double y = (1.0 - log(tan(latRad) + 1.0 / cos(latRad)) / M_PI) / 2.0 * (1LL << m_zoomLevel) * TILE_SIZE;

    qDebug() << "TileMapView: latLngToPixel - 输入:" << lat << lng << "输出:" << x << y << "缩放:" << m_zoomLevel;
    return QPointF(x, y);
}

QPointF TileMapView::latLngToViewPixel(double lat, double lng) const
{
    // 获取目标点的场景坐标（全球像素坐标）
    QPointF scenePos = latLngToPixel(lat, lng);

    // 使用QGraphicsView的标准坐标转换，但确保精度
    QPointF viewPos = mapFromScene(scenePos);

    // 四舍五入到最近的像素，避免亚像素偏移
    viewPos.setX(qRound(viewPos.x()));
    viewPos.setY(qRound(viewPos.y()));

    qDebug() << "TileMapView: latLngToViewPixel - 地理坐标:" << lat << lng
             << "场景坐标:" << scenePos.x() << scenePos.y()
             << "视图坐标:" << viewPos.x() << viewPos.y();

    return viewPos;
}

bool TileMapView::isCoordinateVisible(double lat, double lng) const
{
    // 获取场景坐标
    QPointF scenePos = latLngToPixel(lat, lng);

    // 获取当前视图的场景矩形
    QRectF visibleRect = mapToScene(rect()).boundingRect();

    // 添加边距以避免边界问题
    double margin = 100.0; // 100像素边距
    visibleRect.adjust(-margin, -margin, margin, margin);

    // 检查场景坐标是否在可见矩形内
    bool visible = visibleRect.contains(scenePos);

    qDebug() << "TileMapView: isCoordinateVisible - 地理坐标:" << lat << lng
             << "场景坐标:" << scenePos.x() << scenePos.y()
             << "可见矩形:" << visibleRect
             << "可见:" << visible;

    return visible;
}

QPair<double, double> TileMapView::pixelToLatLng(const QPointF &pixel) const
{
    // Web Mercator逆投影
    double lng = pixel.x() / TILE_SIZE / (1 << m_zoomLevel) * 360.0 - 180.0;
    double n = M_PI - 2.0 * M_PI * pixel.y() / TILE_SIZE / (1 << m_zoomLevel);
    double lat = 180.0 / M_PI * atan(0.5 * (exp(n) - exp(-n)));
    return qMakePair(lat, lng);
}

QPair<double, double> TileMapView::getCenter() const
{
    return qMakePair(m_centerLat, m_centerLng);
}

QPoint TileMapView::latLngToTile(double lat, double lng, int zoom) const
{
    // 直接计算瓦片坐标，不依赖当前缩放级别
    double x = (lng + 180.0) / 360.0 * (1 << zoom);
    double latRad = lat * M_PI / 180.0;
    double y = (1.0 - log(tan(latRad) + 1.0 / cos(latRad)) / M_PI) / 2.0 * (1 << zoom);

    int tileX = static_cast<int>(floor(x));
    int tileY = static_cast<int>(floor(y));

    qDebug() << "TileMapView: latLngToTile - 输入:" << lat << lng << zoom << "输出:" << tileX << tileY;
    return QPoint(tileX, tileY);
}

QPair<double, double> TileMapView::tileToLatLng(int x, int y, int zoom) const
{
    QPointF pixel(x * TILE_SIZE, y * TILE_SIZE);
    return pixelToLatLng(pixel);
}

QString TileMapView::getTileUrl(int x, int y, int z) const
{
    // 使用OpenStreetMap瓦片服务
    QString url = QString("https://tile.openstreetmap.org/%1/%2/%3.png").arg(z).arg(x).arg(y);
    qDebug() << "TileMapView: 生成瓦片URL:" << url;
    return url;
}

void TileMapView::updateView()
{
    if (!m_scene)
    {
        qDebug() << "TileMapView: updateView - 场景未初始化";
        return;
    }

    // 计算中心点像素坐标
    QPointF centerPixel = latLngToPixel(m_centerLat, m_centerLng);
    qDebug() << "TileMapView: updateView - 中心坐标:" << m_centerLat << m_centerLng << "像素坐标:" << centerPixel;

    // 计算我们有瓦片的区域范围
    QPoint centerTile = latLngToTile(m_centerLat, m_centerLng, m_zoomLevel);

    // 根据视图大小动态计算需要的瓦片范围（与loadVisibleTiles保持一致）
    QSize viewSize = size();
    int tilesX = (viewSize.width() / TILE_SIZE) + 2;
    int tilesY = (viewSize.height() / TILE_SIZE) + 2;
    int radiusX = qMax(tilesX / 2, 3);
    int radiusY = qMax(tilesY / 2, 3);

    int minTileX = centerTile.x() - radiusX;
    int maxTileX = centerTile.x() + radiusX;
    int minTileY = centerTile.y() - radiusY;
    int maxTileY = centerTile.y() + radiusY;

    // 设置场景矩形为瓦片覆盖的区域
    double sceneLeft = minTileX * TILE_SIZE;
    double sceneTop = minTileY * TILE_SIZE;
    double sceneWidth = (maxTileX - minTileX + 1) * TILE_SIZE;
    double sceneHeight = (maxTileY - minTileY + 1) * TILE_SIZE;

    m_scene->setSceneRect(sceneLeft, sceneTop, sceneWidth, sceneHeight);

    qDebug() << "TileMapView: 瓦片范围:" << minTileX << "-" << maxTileX << "," << minTileY << "-" << maxTileY;
    qDebug() << "TileMapView: 场景矩形:" << m_scene->sceneRect();

    // 居中显示到北京坐标
    centerOn(centerPixel);

    qDebug() << "TileMapView: updateView - 场景矩形:" << m_scene->sceneRect() << "视图大小:" << size();
}

void TileMapView::updateVisibleTiles()
{
    if (!m_scene)
    {
        return;
    }

    // 使用loadVisibleTiles方法
    loadVisibleTiles();

    // 清理旧瓦片（减少频率）
    static int updateCount = 0;
    if (++updateCount % 5 == 0) // 每5次更新才清理一次
    {
        clearOldTiles();
    }

    // 预加载周围瓦片（更低频率）
    if (updateCount % 10 == 0) // 每10次更新才预加载一次
    {
        preloadTiles();
    }
}

void TileMapView::loadTile(int x, int y, int z)
{
    QString key = QString("%1_%2_%3").arg(x).arg(y).arg(z);

    // 检查瓦片坐标是否有效
    int maxTileIndex = (1 << z) - 1;
    if (x < 0 || x > maxTileIndex || y < 0 || y > maxTileIndex)
    {
        return; // 静默忽略无效坐标，减少日志输出
    }

    // 检查瓦片是否已在场景中或正在加载
    if (m_tiles.contains(key))
    {
        TileItem &tileItem = m_tiles[key];
        if (tileItem.item || tileItem.loading)
        {
            // 更新访问时间
            tileItem.lastAccessed = QDateTime::currentMSecsSinceEpoch();
            return; // 已存在或正在加载，避免重复
        }
    }

    // 标记瓦片为正在加载状态
    TileItem tileItem;
    tileItem.loading = true;
    tileItem.item = nullptr;
    tileItem.lastAccessed = QDateTime::currentMSecsSinceEpoch();
    m_tiles[key] = tileItem;

    // 直接创建测试瓦片，不依赖LocalTileManager
    QPixmap pixmap = createTestTile(x, y, z);

    if (!pixmap.isNull())
    {
        // 成功获取瓦片，添加到场景
        QGraphicsPixmapItem *item = m_scene->addPixmap(pixmap);
        item->setPos(x * TILE_SIZE, y * TILE_SIZE);

        // 更新瓦片项
        m_tiles[key].item = item;
        m_tiles[key].loading = false;
    }
    else
    {
        // 创建占位瓦片
        QPixmap placeholder(TILE_SIZE, TILE_SIZE);
        placeholder.fill(QColor(245, 245, 245));
        QPainter painter(&placeholder);
        painter.setPen(QPen(QColor(200, 200, 200), 1));
        painter.drawRect(0, 0, TILE_SIZE - 1, TILE_SIZE - 1);
        painter.setPen(QColor(150, 150, 150));
        painter.setFont(QFont("Arial", 8));
        painter.drawText(placeholder.rect(), Qt::AlignCenter, QString("%1,%2").arg(x).arg(y));
        painter.end();

        // 添加到场景
        QGraphicsPixmapItem *item = m_scene->addPixmap(placeholder);
        item->setPos(x * TILE_SIZE, y * TILE_SIZE);

        // 更新瓦片项
        m_tiles[key].item = item;
        m_tiles[key].loading = false;
    }
}

void TileMapView::loadVisibleTiles()
{
    // 简化的瓦片加载：加载中心点周围的瓦片
    QPoint centerTile = latLngToTile(m_centerLat, m_centerLng, m_zoomLevel);

    // 根据视图大小动态计算需要的瓦片范围
    QSize viewSize = size();
    int tilesX = (viewSize.width() / TILE_SIZE) + 4;  // 增加缓冲区
    int tilesY = (viewSize.height() / TILE_SIZE) + 4; // 增加缓冲区

    // 确保至少加载合理的网格大小
    int radiusX = qMax(tilesX / 2, 4);
    int radiusY = qMax(tilesY / 2, 4);

    // 预计算最大瓦片索引
    int maxTileIndex = (1 << m_zoomLevel) - 1;

    // 优化加载顺序：从中心向外加载
    for (int radius = 0; radius <= qMax(radiusX, radiusY); radius++)
    {
        for (int dx = -radius; dx <= radius; dx++)
        {
            for (int dy = -radius; dy <= radius; dy++)
            {
                // 只加载当前半径边界上的瓦片
                if (qAbs(dx) != radius && qAbs(dy) != radius)
                    continue;

                int tileX = centerTile.x() + dx;
                int tileY = centerTile.y() + dy;

                // 实现循环瓦片：X轴可以循环（经度），Y轴有边界（纬度）
                int originalTileX = tileX;
                // X轴循环包装（经度-180到180度循环）
                while (tileX < 0)
                    tileX += (1 << m_zoomLevel);
                while (tileX > maxTileIndex)
                    tileX -= (1 << m_zoomLevel);

                // 调试输出：显示循环包装
                if (originalTileX != tileX)
                {
                    qDebug() << "TileMapView: 瓦片X坐标循环包装:" << originalTileX << "->" << tileX;
                }

                // Y轴边界检查（纬度有物理边界）
                if (tileY >= 0 && tileY <= maxTileIndex &&
                    qAbs(dx) <= radiusX && qAbs(dy) <= radiusY)
                {
                    loadTile(tileX, tileY, m_zoomLevel);
                }
            }
        }
    }
}

void TileMapView::preloadTiles()
{
    // 预加载当前视图外围的瓦片，提高滚动性能
    QPoint centerTile = latLngToTile(m_centerLat, m_centerLng, m_zoomLevel);

    // 预加载范围比可见范围大一圈
    QSize viewSize = size();
    int tilesX = (viewSize.width() / TILE_SIZE) + 6; // 更大的预加载范围
    int tilesY = (viewSize.height() / TILE_SIZE) + 6;

    int radiusX = tilesX / 2;
    int radiusY = tilesY / 2;
    int maxTileIndex = (1 << m_zoomLevel) - 1;

    // 只预加载边缘瓦片
    for (int dx = -radiusX; dx <= radiusX; dx++)
    {
        for (int dy = -radiusY; dy <= radiusY; dy++)
        {
            // 只预加载外围一圈的瓦片
            if (qAbs(dx) < radiusX - 2 && qAbs(dy) < radiusY - 2)
                continue;

            int tileX = centerTile.x() + dx;
            int tileY = centerTile.y() + dy;

            // 实现循环瓦片：X轴可以循环（经度），Y轴有边界（纬度）
            // X轴循环包装（经度-180到180度循环）
            while (tileX < 0)
                tileX += (1 << m_zoomLevel);
            while (tileX > maxTileIndex)
                tileX -= (1 << m_zoomLevel);

            // Y轴边界检查（纬度有物理边界）
            if (tileY >= 0 && tileY <= maxTileIndex)
            {
                QString key = QString("%1_%2_%3").arg(tileX).arg(tileY).arg(m_zoomLevel);
                if (!m_tiles.contains(key))
                {
                    loadTile(tileX, tileY, m_zoomLevel);
                }
            }
        }
    }
}

QPixmap TileMapView::createTestTile(int x, int y, int z)
{
    // 构建本地瓦片文件路径
    QString tilePath = QString("resources/map/vector6/%1/%2/%3.png")
                           .arg(z)
                           .arg(x)
                           .arg(y);

    QPixmap pixmap;

    // 尝试加载本地瓦片文件
    if (QFile::exists(tilePath) && pixmap.load(tilePath))
    {
        qDebug() << "TileMapView: 成功加载本地瓦片:" << tilePath;
        return pixmap;
    }

    // 如果本地瓦片不存在，创建占位符瓦片
    qDebug() << "TileMapView: 本地瓦片不存在，创建占位符:" << tilePath;

    pixmap = QPixmap(TILE_SIZE, TILE_SIZE);
    pixmap.fill(QColor(240, 240, 240)); // 浅灰色背景

    QPainter painter(&pixmap);

    // 绘制边框
    painter.setPen(QPen(Qt::lightGray, 1));
    painter.drawRect(0, 0, TILE_SIZE - 1, TILE_SIZE - 1);

    // 绘制"无瓦片"信息
    painter.setPen(QPen(Qt::gray, 1));
    painter.setFont(QFont("Arial", 10));
    painter.drawText(pixmap.rect(), Qt::AlignCenter,
                     QString("No Tile\nZ:%1 X:%2 Y:%3").arg(z).arg(x).arg(y));

    painter.end();

    return pixmap;
}

void TileMapView::onTileDownloaded()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply *>(sender());
    if (!reply)
        return;

    TileKey tileKey = m_pendingTiles.take(reply);
    QString key = QString("%1_%2_%3").arg(tileKey.x).arg(tileKey.y).arg(tileKey.z);

    if (reply->error() == QNetworkReply::NoError)
    {
        QByteArray data = reply->readAll();
        qDebug() << "TileMapView: 瓦片下载成功" << key << "大小:" << data.size() << "字节";

        QPixmap pixmap;
        if (pixmap.loadFromData(data))
        {
            qDebug() << "TileMapView: 瓦片图像加载成功" << key << "尺寸:" << pixmap.size();

            // 创建图形项
            QGraphicsPixmapItem *item = m_scene->addPixmap(pixmap);
            item->setPos(tileKey.x * TILE_SIZE, tileKey.y * TILE_SIZE);

            qDebug() << "TileMapView: 瓦片添加到场景" << key << "位置:" << (tileKey.x * TILE_SIZE) << (tileKey.y * TILE_SIZE);

            // 更新瓦片项
            if (m_tiles.contains(key))
            {
                m_tiles[key].item = item;
                m_tiles[key].loading = false;
            }
        }
        else
        {
            qDebug() << "TileMapView: 瓦片图像加载失败" << key;
        }
    }
    else
    {
        qDebug() << "TileMapView: 瓦片下载失败" << key << "错误:" << reply->errorString();
        // 下载失败，移除瓦片项
        m_tiles.remove(key);
    }

    reply->deleteLater();
}

void TileMapView::checkAndDownloadTiles()
{
    // 检查是否有本地瓦片，如果没有则开始下载
    bool hasLocalTiles = false;

    // 检查中心点周围的瓦片是否存在
    QPoint centerTile = latLngToTile(m_centerLat, m_centerLng, m_zoomLevel);
    for (int dx = -1; dx <= 1; dx++)
    {
        for (int dy = -1; dy <= 1; dy++)
        {
            int tileX = centerTile.x() + dx;
            int tileY = centerTile.y() + dy;

            if (m_tileManager->tileExists(tileX, tileY, m_zoomLevel))
            {
                hasLocalTiles = true;
                break;
            }
        }
        if (hasLocalTiles)
            break;
    }

    if (!hasLocalTiles)
    {
        qDebug() << "TileMapView: 本地瓦片不存在，开始下载测试瓦片";

        // 显示下载提示
        int ret = QMessageBox::question(this, "瓦片下载",
                                        "本地瓦片不存在，是否下载中国区域地图瓦片(1-4级)？\n"
                                        "这可能需要几分钟时间。",
                                        QMessageBox::Yes | QMessageBox::No);

        if (ret == QMessageBox::Yes)
        {
            // 先测试下载几个瓦片
            qDebug() << "TileMapView: 用户确认下载，开始下载测试瓦片";
            qDebug() << "TileMapView: 下载1级瓦片 (0,0) 到 (1,1)";

            // 只下载1级的4个瓦片进行测试
            m_tileManager->downloadTiles(0, 1, 0, 1, 1, 1);
        }
        else
        {
            qDebug() << "TileMapView: 用户取消下载";
        }
    }
}

void TileMapView::onLocalTileDownloaded(int x, int y, int z, bool success)
{
    qDebug() << "TileMapView: 瓦片下载完成" << x << y << z << "成功:" << success;

    if (success)
    {
        qDebug() << "TileMapView: 瓦片下载成功，当前缩放级别:" << m_zoomLevel;

        if (z == m_zoomLevel)
        {
            // 如果下载的瓦片是当前缩放级别的，尝试加载到场景
            qDebug() << "TileMapView: 加载新下载的瓦片到场景";
            loadTile(x, y, z);
        }
    }
    else
    {
        qDebug() << "TileMapView: 瓦片下载失败" << x << y << z;
    }
}

void TileMapView::onDownloadProgress(int current, int total)
{
    qDebug() << "TileMapView: 下载进度" << current << "/" << total;

    // 可以在这里更新进度条或状态栏
    if (current == total)
    {
        qDebug() << "TileMapView: 瓦片下载完成，重新加载可见瓦片";
        loadVisibleTiles();
    }
}

void TileMapView::createTestTiles()
{
    qDebug() << "TileMapView: 创建测试瓦片";

    // 获取中心瓦片坐标
    QPoint centerTile = latLngToTile(m_centerLat, m_centerLng, m_zoomLevel);

    // 创建几个测试瓦片
    for (int dx = -1; dx <= 1; dx++)
    {
        for (int dy = -1; dy <= 1; dy++)
        {
            int tileX = centerTile.x() + dx;
            int tileY = centerTile.y() + dy;

            // 检查坐标是否有效
            int maxTileIndex = (1 << m_zoomLevel) - 1;
            if (tileX >= 0 && tileX <= maxTileIndex && tileY >= 0 && tileY <= maxTileIndex)
            {
                // 创建测试瓦片图像
                QPixmap testPixmap(TILE_SIZE, TILE_SIZE);
                testPixmap.fill(QColor(200, 220, 240)); // 浅蓝色背景

                QPainter painter(&testPixmap);
                painter.setPen(QPen(Qt::darkBlue, 2));
                painter.drawRect(0, 0, TILE_SIZE - 1, TILE_SIZE - 1);

                // 绘制瓦片信息
                painter.setPen(Qt::black);
                painter.setFont(QFont("Arial", 12));
                painter.drawText(testPixmap.rect(), Qt::AlignCenter,
                                 QString("Tile\n%1,%2\nZ:%3").arg(tileX).arg(tileY).arg(m_zoomLevel));
                painter.end();

                // 直接添加到场景
                QGraphicsPixmapItem *item = m_scene->addPixmap(testPixmap);
                item->setPos(tileX * TILE_SIZE, tileY * TILE_SIZE);

                // 更新瓦片项
                QString key = QString("%1_%2_%3").arg(tileX).arg(tileY).arg(m_zoomLevel);
                TileItem tileItem;
                tileItem.item = item;
                tileItem.loading = false;
                m_tiles[key] = tileItem;

                qDebug() << "TileMapView: 创建测试瓦片" << key << "位置:" << (tileX * TILE_SIZE) << (tileY * TILE_SIZE);
            }
        }
    }
}

void TileMapView::clearOldTiles()
{
    if (m_tiles.size() <= MAX_CACHED_TILES)
        return;

    // LRU缓存策略：移除最久未访问的瓦片
    QList<QPair<qint64, QString>> tilesByAge;

    // 收集所有瓦片及其访问时间
    for (auto it = m_tiles.begin(); it != m_tiles.end(); ++it)
    {
        tilesByAge.append(qMakePair(it->lastAccessed, it.key()));
    }

    // 按访问时间排序（最旧的在前）
    std::sort(tilesByAge.begin(), tilesByAge.end());

    // 移除最旧的瓦片，直到数量合理
    int removeCount = m_tiles.size() - MAX_CACHED_TILES + 20; // 多移除一些，避免频繁清理

    for (int i = 0; i < removeCount && i < tilesByAge.size(); ++i)
    {
        const QString &key = tilesByAge[i].second;
        auto it = m_tiles.find(key);
        if (it != m_tiles.end())
        {
            if (it->item)
            {
                m_scene->removeItem(it->item);
                delete it->item;
            }
            m_tiles.erase(it);
        }
    }
}

void TileMapView::wheelEvent(QWheelEvent *event)
{
    // 瓦片级别缩放功能
    int delta = event->angleDelta().y();

    if (delta > 0)
    {
        // 放大：增加缩放级别
        setZoomLevel(m_zoomLevel + 1);
    }
    else if (delta < 0)
    {
        // 缩小：减少缩放级别
        setZoomLevel(m_zoomLevel - 1);
    }
    event->accept();
}

void TileMapView::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        m_dragging = false; // 初始时不是拖拽状态
        m_lastPanPoint = event->pos();
        m_clickStartPoint = event->pos(); // 记录点击起始位置
        setCursor(Qt::ClosedHandCursor);
    }
    QGraphicsView::mousePressEvent(event);
}

void TileMapView::mouseDoubleClickEvent(QMouseEvent *event)
{
    // 双击放大并居中
    QPointF scenePos = mapToScene(event->pos());
    auto latLng = pixelToLatLng(scenePos);

    setZoomLevel(m_zoomLevel + 1);
    setCenter(latLng.first, latLng.second);

    event->accept();
}

void TileMapView::mouseMoveEvent(QMouseEvent *event)
{
    // 限制坐标更新频率
    static QPoint lastMousePos;
    if ((event->pos() - lastMousePos).manhattanLength() > 5) // 只有移动超过5像素才更新
    {
        QPointF scenePos = mapToScene(event->pos());
        auto latLng = pixelToLatLng(scenePos);
        emit mouseCoordinateChanged(latLng.first, latLng.second);
        lastMousePos = event->pos();
    }

    // 检查是否开始拖拽（鼠标移动超过阈值）
    if (!m_dragging && (event->buttons() & Qt::LeftButton))
    {
        QPoint delta = event->pos() - m_clickStartPoint;
        if (delta.manhattanLength() > 5) // 移动超过5像素才认为是拖拽
        {
            m_dragging = true;
        }
    }

    if (m_dragging && (event->buttons() & Qt::LeftButton))
    {
        // 平移地图
        QPoint delta = event->pos() - m_lastPanPoint;

        // 只有移动距离足够大时才更新
        if (delta.manhattanLength() > 2)
        {
            // 转换为场景坐标的偏移
            QPointF sceneDelta = mapToScene(delta) - mapToScene(QPoint(0, 0));

            // 计算新的中心坐标
            QPointF currentCenterPixel = latLngToPixel(m_centerLat, m_centerLng);
            QPointF newCenterPixel = currentCenterPixel - sceneDelta;
            auto newCenter = pixelToLatLng(newCenterPixel);

            setCenter(newCenter.first, newCenter.second);
            m_lastPanPoint = event->pos();
        }
    }
    QGraphicsView::mouseMoveEvent(event);
}

void TileMapView::resizeEvent(QResizeEvent *event)
{
    QGraphicsView::resizeEvent(event);
    updateView();
    m_updateTimer->start();
}

void TileMapView::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton)
    {
        // 如果没有发生拖拽，则认为是点击事件
        if (!m_dragging)
        {
            // 发射点击坐标信号
            QPointF scenePos = mapToScene(event->pos());
            auto latLng = pixelToLatLng(scenePos);
            emit coordinateClicked(latLng.first, latLng.second);
        }

        // 重置拖拽状态
        m_dragging = false;
        setCursor(Qt::ArrowCursor);
    }
    QGraphicsView::mouseReleaseEvent(event);
}

void TileMapView::testSingleTileDownload()
{
    qDebug() << "TileMapView: 开始测试单个瓦片创建";

    // 目标文件路径
    QString cacheDir = QStandardPaths::writableLocation(QStandardPaths::AppLocalDataLocation) + "/resources/map/tiles";
    QString filePath = cacheDir + "/1/0/0.png";

    qDebug() << "TileMapView: 目标文件路径:" << filePath;

    // 确保目录存在
    QDir dir;
    QString dirPath = cacheDir + "/1/0";
    if (!dir.exists(dirPath))
    {
        bool success = dir.mkpath(dirPath);
        qDebug() << "TileMapView: 创建目录" << dirPath << "成功:" << success;
    }

    // 直接创建测试瓦片
    QPixmap pixmap(256, 256);
    pixmap.fill(QColor(200, 220, 240)); // 浅蓝色背景

    QPainter painter(&pixmap);
    painter.setPen(QPen(Qt::darkBlue, 2));
    painter.drawRect(0, 0, 255, 255);

    // 绘制瓦片信息
    painter.setPen(Qt::black);
    painter.setFont(QFont("Arial", 16));
    painter.drawText(pixmap.rect(), Qt::AlignCenter,
                     QString("Test Tile\n0,0\nZ:1"));
    painter.end();

    // 保存文件
    bool success = pixmap.save(filePath, "PNG");

    if (success)
    {
        qDebug() << "TileMapView: 测试瓦片保存成功:" << filePath;

        // 验证文件是否存在
        if (QFile::exists(filePath))
        {
            QFileInfo fileInfo(filePath);
            qDebug() << "TileMapView: 文件验证成功！";
            qDebug() << "TileMapView: 文件大小:" << fileInfo.size() << "字节";
            qDebug() << "TileMapView: 文件路径:" << fileInfo.absoluteFilePath();
        }
        else
        {
            qDebug() << "TileMapView: 错误 - 文件保存后不存在";
        }
    }
    else
    {
        qDebug() << "TileMapView: 测试瓦片保存失败:" << filePath;
    }
}
