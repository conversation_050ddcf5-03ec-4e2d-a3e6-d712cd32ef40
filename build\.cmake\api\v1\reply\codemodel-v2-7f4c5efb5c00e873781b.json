{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-72be8cd7a9b9e3bc1dd0.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "LiteAPPStar", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-9fad7f61f237e179c97b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "LiteAPPStar::@6890427a1f51a3e7e1df", "jsonFile": "target-LiteAPPStar-Debug-cb79d6b14e7dd7ce85ba.json", "name": "LiteAPPStar", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a11cd21fe9336cf32697.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-0143fdbee5155ec72905.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "LiteAPPStar", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-9fad7f61f237e179c97b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "LiteAPPStar::@6890427a1f51a3e7e1df", "jsonFile": "target-LiteAPPStar-Release-e2f1b5a3c15a6e73b77c.json", "name": "LiteAPPStar", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a11cd21fe9336cf32697.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-bc7387b0c99a13d7d2ac.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "LiteAPPStar", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-9fad7f61f237e179c97b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "LiteAPPStar::@6890427a1f51a3e7e1df", "jsonFile": "target-LiteAPPStar-MinSizeRel-6dc47001dfe90037f0bf.json", "name": "LiteAPPStar", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a11cd21fe9336cf32697.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-4232b7d91a2ff489536a.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "LiteAPPStar", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9fad7f61f237e179c97b.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "LiteAPPStar::@6890427a1f51a3e7e1df", "jsonFile": "target-LiteAPPStar-RelWithDebInfo-49e8350bd5da0dd8d798.json", "name": "LiteAPPStar", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a11cd21fe9336cf32697.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/code/LiteAPPStar/build", "source": "E:/code/LiteAPPStar"}, "version": {"major": 2, "minor": 6}}