/****************************************************************************
** Meta object code from reading C++ file 'qgismapwidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/qgismapwidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qgismapwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QgisMapWidget_t {
    QByteArrayData data[50];
    char stringdata0[740];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QgisMapWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QgisMapWidget_t qt_meta_stringdata_QgisMapWidget = {
    {
QT_MOC_LITERAL(0, 0, 13), // "QgisMapWidget"
QT_MOC_LITERAL(1, 14, 10), // "mapClicked"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 1), // "x"
QT_MOC_LITERAL(4, 28, 1), // "y"
QT_MOC_LITERAL(5, 30, 16), // "mapExtentChanged"
QT_MOC_LITERAL(6, 47, 10), // "layerAdded"
QT_MOC_LITERAL(7, 58, 7), // "layerId"
QT_MOC_LITERAL(8, 66, 4), // "name"
QT_MOC_LITERAL(9, 71, 12), // "layerRemoved"
QT_MOC_LITERAL(10, 84, 10), // "crsChanged"
QT_MOC_LITERAL(11, 95, 7), // "crsCode"
QT_MOC_LITERAL(12, 103, 16), // "zoomToFullExtent"
QT_MOC_LITERAL(13, 120, 11), // "zoomToLayer"
QT_MOC_LITERAL(14, 132, 10), // "refreshMap"
QT_MOC_LITERAL(15, 143, 9), // "exportMap"
QT_MOC_LITERAL(16, 153, 8), // "filePath"
QT_MOC_LITERAL(17, 162, 6), // "format"
QT_MOC_LITERAL(18, 169, 21), // "toggleLayerVisibility"
QT_MOC_LITERAL(19, 191, 7), // "visible"
QT_MOC_LITERAL(20, 199, 15), // "setLayerOpacity"
QT_MOC_LITERAL(21, 215, 7), // "opacity"
QT_MOC_LITERAL(22, 223, 11), // "moveLayerUp"
QT_MOC_LITERAL(23, 235, 13), // "moveLayerDown"
QT_MOC_LITERAL(24, 249, 21), // "refreshGeographicData"
QT_MOC_LITERAL(25, 271, 23), // "showGeographicDataPanel"
QT_MOC_LITERAL(26, 295, 23), // "hideGeographicDataPanel"
QT_MOC_LITERAL(27, 319, 25), // "toggleGeographicDataPanel"
QT_MOC_LITERAL(28, 345, 18), // "onMapCanvasClicked"
QT_MOC_LITERAL(29, 364, 15), // "onExtentChanged"
QT_MOC_LITERAL(30, 380, 18), // "onLayerTreeChanged"
QT_MOC_LITERAL(31, 399, 23), // "onAddVectorLayerClicked"
QT_MOC_LITERAL(32, 423, 23), // "onAddRasterLayerClicked"
QT_MOC_LITERAL(33, 447, 20), // "onRemoveLayerClicked"
QT_MOC_LITERAL(34, 468, 24), // "onLayerPropertiesClicked"
QT_MOC_LITERAL(35, 493, 18), // "onExportMapClicked"
QT_MOC_LITERAL(36, 512, 20), // "onLayerToggleClicked"
QT_MOC_LITERAL(37, 533, 16), // "onMeasureClicked"
QT_MOC_LITERAL(38, 550, 12), // "onMapClicked"
QT_MOC_LITERAL(39, 563, 3), // "lat"
QT_MOC_LITERAL(40, 567, 3), // "lng"
QT_MOC_LITERAL(41, 571, 27), // "onGeographicDataPanelClosed"
QT_MOC_LITERAL(42, 599, 32), // "onGeographicDataRefreshRequested"
QT_MOC_LITERAL(43, 632, 27), // "onGeographicDataRowSelected"
QT_MOC_LITERAL(44, 660, 3), // "row"
QT_MOC_LITERAL(45, 664, 4), // "data"
QT_MOC_LITERAL(46, 669, 24), // "onDataPanelToggleClicked"
QT_MOC_LITERAL(47, 694, 17), // "onLocationClicked"
QT_MOC_LITERAL(48, 712, 14), // "getTileMapView"
QT_MOC_LITERAL(49, 727, 12) // "TileMapView*"

    },
    "QgisMapWidget\0mapClicked\0\0x\0y\0"
    "mapExtentChanged\0layerAdded\0layerId\0"
    "name\0layerRemoved\0crsChanged\0crsCode\0"
    "zoomToFullExtent\0zoomToLayer\0refreshMap\0"
    "exportMap\0filePath\0format\0"
    "toggleLayerVisibility\0visible\0"
    "setLayerOpacity\0opacity\0moveLayerUp\0"
    "moveLayerDown\0refreshGeographicData\0"
    "showGeographicDataPanel\0hideGeographicDataPanel\0"
    "toggleGeographicDataPanel\0onMapCanvasClicked\0"
    "onExtentChanged\0onLayerTreeChanged\0"
    "onAddVectorLayerClicked\0onAddRasterLayerClicked\0"
    "onRemoveLayerClicked\0onLayerPropertiesClicked\0"
    "onExportMapClicked\0onLayerToggleClicked\0"
    "onMeasureClicked\0onMapClicked\0lat\0lng\0"
    "onGeographicDataPanelClosed\0"
    "onGeographicDataRefreshRequested\0"
    "onGeographicDataRowSelected\0row\0data\0"
    "onDataPanelToggleClicked\0onLocationClicked\0"
    "getTileMapView\0TileMapView*"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QgisMapWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      35,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,  189,    2, 0x06 /* Public */,
       5,    0,  194,    2, 0x06 /* Public */,
       6,    2,  195,    2, 0x06 /* Public */,
       9,    1,  200,    2, 0x06 /* Public */,
      10,    1,  203,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    0,  206,    2, 0x0a /* Public */,
      13,    1,  207,    2, 0x0a /* Public */,
      14,    0,  210,    2, 0x0a /* Public */,
      15,    2,  211,    2, 0x0a /* Public */,
      15,    1,  216,    2, 0x2a /* Public | MethodCloned */,
      18,    2,  219,    2, 0x0a /* Public */,
      20,    2,  224,    2, 0x0a /* Public */,
      22,    1,  229,    2, 0x0a /* Public */,
      23,    1,  232,    2, 0x0a /* Public */,
      24,    0,  235,    2, 0x0a /* Public */,
      25,    0,  236,    2, 0x0a /* Public */,
      26,    0,  237,    2, 0x0a /* Public */,
      27,    0,  238,    2, 0x0a /* Public */,
      28,    0,  239,    2, 0x08 /* Private */,
      29,    0,  240,    2, 0x08 /* Private */,
      30,    0,  241,    2, 0x08 /* Private */,
      31,    0,  242,    2, 0x08 /* Private */,
      32,    0,  243,    2, 0x08 /* Private */,
      33,    0,  244,    2, 0x08 /* Private */,
      34,    0,  245,    2, 0x08 /* Private */,
      35,    0,  246,    2, 0x08 /* Private */,
      36,    0,  247,    2, 0x08 /* Private */,
      37,    0,  248,    2, 0x08 /* Private */,
      38,    2,  249,    2, 0x08 /* Private */,
      41,    0,  254,    2, 0x08 /* Private */,
      42,    0,  255,    2, 0x08 /* Private */,
      43,    2,  256,    2, 0x08 /* Private */,
      46,    0,  261,    2, 0x08 /* Private */,
      47,    0,  262,    2, 0x08 /* Private */,
      48,    0,  263,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    7,    8,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,   11,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   16,   17,
    QMetaType::Void, QMetaType::QString,   16,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    7,   19,
    QMetaType::Void, QMetaType::QString, QMetaType::Double,    7,   21,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,   39,   40,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QStringList,   44,   45,
    QMetaType::Void,
    QMetaType::Void,
    0x80000000 | 49,

       0        // eod
};

void QgisMapWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QgisMapWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->mapClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->mapExtentChanged(); break;
        case 2: _t->layerAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->layerRemoved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->crsChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->zoomToFullExtent(); break;
        case 6: _t->zoomToLayer((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->refreshMap(); break;
        case 8: _t->exportMap((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 9: _t->exportMap((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 10: _t->toggleLayerVisibility((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 11: _t->setLayerOpacity((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 12: _t->moveLayerUp((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 13: _t->moveLayerDown((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 14: _t->refreshGeographicData(); break;
        case 15: _t->showGeographicDataPanel(); break;
        case 16: _t->hideGeographicDataPanel(); break;
        case 17: _t->toggleGeographicDataPanel(); break;
        case 18: _t->onMapCanvasClicked(); break;
        case 19: _t->onExtentChanged(); break;
        case 20: _t->onLayerTreeChanged(); break;
        case 21: _t->onAddVectorLayerClicked(); break;
        case 22: _t->onAddRasterLayerClicked(); break;
        case 23: _t->onRemoveLayerClicked(); break;
        case 24: _t->onLayerPropertiesClicked(); break;
        case 25: _t->onExportMapClicked(); break;
        case 26: _t->onLayerToggleClicked(); break;
        case 27: _t->onMeasureClicked(); break;
        case 28: _t->onMapClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 29: _t->onGeographicDataPanelClosed(); break;
        case 30: _t->onGeographicDataRefreshRequested(); break;
        case 31: _t->onGeographicDataRowSelected((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QStringList(*)>(_a[2]))); break;
        case 32: _t->onDataPanelToggleClicked(); break;
        case 33: _t->onLocationClicked(); break;
        case 34: { TileMapView* _r = _t->getTileMapView();
            if (_a[0]) *reinterpret_cast< TileMapView**>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QgisMapWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QgisMapWidget::mapClicked)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QgisMapWidget::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QgisMapWidget::mapExtentChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QgisMapWidget::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QgisMapWidget::layerAdded)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QgisMapWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QgisMapWidget::layerRemoved)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (QgisMapWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QgisMapWidget::crsChanged)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QgisMapWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_QgisMapWidget.data,
    qt_meta_data_QgisMapWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QgisMapWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QgisMapWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QgisMapWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int QgisMapWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 35)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 35;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 35)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 35;
    }
    return _id;
}

// SIGNAL 0
void QgisMapWidget::mapClicked(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QgisMapWidget::mapExtentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void QgisMapWidget::layerAdded(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QgisMapWidget::layerRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QgisMapWidget::crsChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
