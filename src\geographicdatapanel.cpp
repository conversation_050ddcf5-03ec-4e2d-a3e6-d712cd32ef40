#include "geographicdatapanel.h"
#include "ui/ui_geographicdatapanel.h"
#include <QApplication>
#include <QPainter>
#include <QMouseEvent>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QHeaderView>
#include <QDebug>
#include <QtMath>

GeographicDataPanel::GeographicDataPanel(QWidget *parent)
    : QWidget(parent), ui(new Ui::GeographicDataPanel), m_position(Bottom), m_dragging(false), m_isVisible(false), m_showAnimation(nullptr), m_hideAnimation(nullptr), m_opacityEffect(nullptr), m_totalRecords(0), m_loadProgress(0), m_refreshTimer(nullptr)
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    setupAnimations();
    setupTableColumns();

    // 设置默认大小和位置
    resize(DEFAULT_WIDTH, DEFAULT_HEIGHT);
    setPosition(Bottom);

    // 加载示例数据
    loadSampleData();

    qDebug() << "GeographicDataPanel: 初始化完成";
}

GeographicDataPanel::~GeographicDataPanel()
{
    delete ui;
}

void GeographicDataPanel::setupUI()
{
    // 设置窗口属性
    setWindowFlags(Qt::Widget);
    setAttribute(Qt::WA_TranslucentBackground);

    // 设置透明度效果
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    m_opacityEffect->setOpacity(0.95);
    setGraphicsEffect(m_opacityEffect);

    // 初始隐藏
    hide();

    qDebug() << "GeographicDataPanel: UI设置完成";
}

void GeographicDataPanel::setupConnections()
{
    // 连接表格信号
    connect(ui->userInfoTable, &QTableWidget::cellClicked, this, &GeographicDataPanel::onTableRowClicked);

    // 连接Tab切换信号
    connect(ui->dataTabWidget, &QTabWidget::currentChanged, this, &GeographicDataPanel::onTabChanged);

    qDebug() << "GeographicDataPanel: 信号连接完成";
}

void GeographicDataPanel::setupAnimations()
{
    // 显示动画
    m_showAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_showAnimation->setDuration(ANIMATION_DURATION);
    m_showAnimation->setStartValue(0.0);
    m_showAnimation->setEndValue(0.95);
    connect(m_showAnimation, &QPropertyAnimation::finished, this, &GeographicDataPanel::onShowAnimationFinished);

    // 隐藏动画
    m_hideAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_hideAnimation->setDuration(ANIMATION_DURATION);
    m_hideAnimation->setStartValue(0.95);
    m_hideAnimation->setEndValue(0.0);
    connect(m_hideAnimation, &QPropertyAnimation::finished, this, &GeographicDataPanel::onHideAnimationFinished);

    qDebug() << "GeographicDataPanel: 动画设置完成";
}

void GeographicDataPanel::setupTableColumns()
{
    // 设置表格自适应列宽
    QHeaderView *header = ui->userInfoTable->horizontalHeader();

    // 设置列的拉伸模式
    header->setSectionResizeMode(0, QHeaderView::Fixed);            // ID - 固定宽度
    header->setSectionResizeMode(1, QHeaderView::Stretch);          // 系统类型 - 拉伸
    header->setSectionResizeMode(2, QHeaderView::ResizeToContents); // UE类别 - 内容适应
    header->setSectionResizeMode(3, QHeaderView::ResizeToContents); // 重要级别 - 内容适应
    header->setSectionResizeMode(4, QHeaderView::Stretch);          // 开始时间 - 拉伸
    header->setSectionResizeMode(5, QHeaderView::Fixed);            // 经度 - 固定宽度
    header->setSectionResizeMode(6, QHeaderView::Fixed);            // 纬度 - 固定宽度
    header->setSectionResizeMode(7, QHeaderView::Stretch);          // 创建时间 - 拉伸

    // 设置固定列的宽度
    ui->userInfoTable->setColumnWidth(0, 50); // ID - 更紧凑
    ui->userInfoTable->setColumnWidth(5, 90); // 经度
    ui->userInfoTable->setColumnWidth(6, 90); // 纬度

    // 隐藏垂直表头
    ui->userInfoTable->verticalHeader()->setVisible(false);

    // 设置表格属性
    ui->userInfoTable->setAlternatingRowColors(true);
    ui->userInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->userInfoTable->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->userInfoTable->setSortingEnabled(true);

    // 设置表格样式
    ui->userInfoTable->setStyleSheet(
        "QTableWidget { "
        "gridline-color: rgba(200, 200, 200, 100); "
        "background-color: rgba(255, 255, 255, 250); "
        "alternate-background-color: rgba(240, 248, 255, 200); "
        "selection-background-color: rgba(52, 152, 219, 150); "
        "} "
        "QTableWidget::item { "
        "padding: 8px 4px; "
        "border: none; "
        "} "
        "QTableWidget::item:selected { "
        "background-color: rgba(52, 152, 219, 180); "
        "color: white; "
        "} "
        "QHeaderView::section { "
        "background-color: rgba(70, 130, 180, 200); "
        "color: white; "
        "padding: 8px 4px; "
        "border: 1px solid rgba(100, 149, 237, 150); "
        "font-weight: bold; "
        "}");

    qDebug() << "GeographicDataPanel: 自适应表格列设置完成";
}

void GeographicDataPanel::setPosition(Position position)
{
    m_position = position;
    updatePosition();
}

void GeographicDataPanel::setCustomPosition(const QPoint &pos)
{
    m_customPosition = pos;
    m_position = Custom;
    updatePosition();
}

void GeographicDataPanel::updatePosition()
{
    if (!parentWidget())
    {
        return;
    }

    // 自适应宽度
    adjustToParentWidth();

    QPoint newPos = calculatePosition(m_position);
    move(newPos);

    qDebug() << "GeographicDataPanel: 位置更新到" << newPos;
}

void GeographicDataPanel::adjustToParentWidth()
{
    if (!parentWidget())
    {
        return;
    }

    // 获取父窗口宽度，留出更大边距以适应右侧控制面板
    int parentWidth = parentWidget()->width();
    int leftMargin = 20;  // 左边距
    int rightMargin = 80; // 右边距（为右上角控制面板留空间）
    int newWidth = parentWidth - leftMargin - rightMargin;

    // 设置最小和最大宽度限制，更加灵活
    int minWidth = qMin(500, static_cast<int>(parentWidth * 0.4)); // 最小宽度为窗口40%或500px
    int maxWidth = static_cast<int>(parentWidth * 0.85);           // 最大宽度为窗口85%
    newWidth = qMax(minWidth, qMin(newWidth, maxWidth));

    // 保持高度不变，但确保有合理的高度
    int currentHeight = height();
    if (currentHeight < 300)
    {
        currentHeight = 350; // 默认高度
    }

    resize(newWidth, currentHeight);

    // 更新表格布局
    updateTableLayout();

    qDebug() << "GeographicDataPanel: 宽度调整为" << newWidth << "高度:" << currentHeight;
}

void GeographicDataPanel::updateTableLayout()
{
    if (!ui->userInfoTable)
    {
        return;
    }

    // 强制更新表格布局
    ui->userInfoTable->resizeColumnsToContents();

    // 重新应用拉伸设置
    QHeaderView *header = ui->userInfoTable->horizontalHeader();
    header->setSectionResizeMode(1, QHeaderView::Stretch); // 系统类型
    header->setSectionResizeMode(4, QHeaderView::Stretch); // 开始时间
    header->setSectionResizeMode(7, QHeaderView::Stretch); // 创建时间

    qDebug() << "GeographicDataPanel: 表格布局已更新";
}

QPoint GeographicDataPanel::calculatePosition(Position position)
{
    if (!parentWidget())
    {
        return QPoint(0, 0);
    }

    QSize parentSize = parentWidget()->size();
    QSize panelSize = size();

    switch (position)
    {
    case Bottom:
        return QPoint((parentSize.width() - panelSize.width()) / 2,
                      parentSize.height() - panelSize.height() - MARGIN);
    case Right:
        return QPoint(parentSize.width() - panelSize.width() - MARGIN,
                      (parentSize.height() - panelSize.height()) / 2);
    case Custom:
        return m_customPosition;
    default:
        return QPoint(MARGIN, parentSize.height() - panelSize.height() - MARGIN);
    }
}

void GeographicDataPanel::showPanel()
{
    if (m_isVisible)
    {
        return;
    }

    m_isVisible = true;
    show();
    raise();

    if (m_hideAnimation->state() == QAbstractAnimation::Running)
    {
        m_hideAnimation->stop();
    }

    m_showAnimation->start();

    qDebug() << "GeographicDataPanel: 显示面板";
}

void GeographicDataPanel::hidePanel()
{
    if (!m_isVisible)
    {
        return;
    }

    m_isVisible = false;

    if (m_showAnimation->state() == QAbstractAnimation::Running)
    {
        m_showAnimation->stop();
    }

    m_hideAnimation->start();

    qDebug() << "GeographicDataPanel: 隐藏面板";
}

void GeographicDataPanel::togglePanel()
{
    if (m_isVisible)
    {
        hidePanel();
    }
    else
    {
        showPanel();
    }
}

void GeographicDataPanel::setOpacity(qreal opacity)
{
    if (m_opacityEffect)
    {
        m_opacityEffect->setOpacity(opacity);
    }
}

void GeographicDataPanel::loadSampleData()
{
    // 加载示例数据
    QList<QStringList> sampleData;
    sampleData << (QStringList() << "1" << "测试系统" << "UE001" << "1"
                                 << "2024-01-15 10:30:00" << "116.404000" << "39.915000" << "2024-01-15 10:30:00");
    sampleData << (QStringList() << "2" << "测试系统" << "UE002" << "2"
                                 << "2024-01-15 11:00:00" << "121.473000" << "31.230000" << "2024-01-15 11:00:00");
    sampleData << (QStringList() << "3" << "测试系统" << "UE003" << "1"
                                 << "2024-01-15 11:30:00" << "113.264000" << "23.129000" << "2024-01-15 11:30:00");

    setTableData(sampleData);

    // 更新统计信息
    updateStatistics(3, 100,
                     "华北地区: 1个点 (33.3%)\n华东地区: 1个点 (33.3%)\n华南地区: 1个点 (33.3%)",
                     "纬度: 23.129000° ~ 39.915000°\n经度: 113.264000° ~ 121.473000°\n覆盖范围: 16.786° × 8.209°",
                     "数据点总数: 3\n最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));

    qDebug() << "GeographicDataPanel: 示例数据加载完成";
}

void GeographicDataPanel::setTableData(const QList<QStringList> &data)
{
    m_tableData = data;
    updateTableData();
}

void GeographicDataPanel::updateTableData()
{
    ui->userInfoTable->setRowCount(0);
    m_coordinates.clear();

    for (int i = 0; i < m_tableData.size(); ++i)
    {
        const QStringList &row = m_tableData[i];
        if (row.size() >= 8)
        {
            ui->userInfoTable->insertRow(i);

            for (int j = 0; j < row.size() && j < 8; ++j)
            {
                ui->userInfoTable->setItem(i, j, new QTableWidgetItem(row[j]));
            }

            // 收集坐标数据
            bool latOk, lngOk;
            double latitude = row[6].toDouble(&latOk);
            double longitude = row[5].toDouble(&lngOk);
            if (latOk && lngOk)
            {
                m_coordinates.append(qMakePair(latitude, longitude));
            }
        }
    }

    // 更新状态标签
    ui->statusLabel->setText(QString("共加载 %1 条记录").arg(m_tableData.size()));
    ui->statusLabel->setStyleSheet("color: #27ae60; font-size: 11px; padding: 3px;");

    qDebug() << "GeographicDataPanel: 表格数据更新完成，共" << m_tableData.size() << "条记录";
}

void GeographicDataPanel::updateStatistics(int totalRecords, int progress,
                                           const QString &regionDistribution,
                                           const QString &coordinateRange,
                                           const QString &timeDistribution)
{
    m_totalRecords = totalRecords;
    m_loadProgress = progress;
    m_regionDistribution = regionDistribution;
    m_coordinateRange = coordinateRange;
    m_timeDistribution = timeDistribution;

    // 更新UI显示
    ui->totalRecordsLabel->setText(QString("总记录数: %1").arg(totalRecords));
    ui->dataLoadProgress->setValue(progress);
    ui->dataLoadProgress->setFormat(QString("已加载: %1 条记录 (%2%)").arg(totalRecords).arg(progress));
    ui->regionDistributionLabel->setText("地理区域分布:\n" + regionDistribution);
    ui->coordinateRangeLabel->setText("坐标范围:\n" + coordinateRange);
    ui->timeDistributionLabel->setText("时间分布统计:\n" + timeDistribution);

    qDebug() << "GeographicDataPanel: 统计信息更新完成";
}

void GeographicDataPanel::refreshData()
{
    emit refreshRequested();
    loadSampleData(); // 临时使用示例数据
}

// 鼠标事件处理已移除（无标题栏拖拽）

void GeographicDataPanel::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制半透明背景
    painter.setBrush(QColor(255, 255, 255, 240));
    painter.setPen(QPen(QColor(70, 130, 180, 200), 2));
    painter.drawRoundedRect(rect(), 8, 8);
}

void GeographicDataPanel::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updatePosition();
}

// 槽函数实现

void GeographicDataPanel::onTableRowClicked(int row, int column)
{
    Q_UNUSED(column)

    if (row >= 0 && row < m_tableData.size())
    {
        emit rowSelected(row, m_tableData[row]);
        qDebug() << "GeographicDataPanel: 选中行" << row;
    }
}

void GeographicDataPanel::onTabChanged(int index)
{
    qDebug() << "GeographicDataPanel: Tab切换到" << index;
}

void GeographicDataPanel::onShowAnimationFinished()
{
    qDebug() << "GeographicDataPanel: 显示动画完成";
}

void GeographicDataPanel::onHideAnimationFinished()
{
    hide();
    qDebug() << "GeographicDataPanel: 隐藏动画完成";
}

// 辅助方法
QMap<QString, int> GeographicDataPanel::calculateRegionDistribution(const QList<QPair<double, double>> &coordinates)
{
    QMap<QString, int> regionStats;

    for (const auto &coord : coordinates)
    {
        double lat = coord.first;
        double lng = coord.second;

        QString region = "未知区域";

        // 简化的地理区域划分
        if (lat >= 18.0 && lat <= 54.0 && lng >= 73.0 && lng <= 135.0)
        {
            if (lat >= 39.0 && lat <= 41.0 && lng >= 115.0 && lng <= 118.0)
            {
                region = "华北地区";
            }
            else if (lat >= 30.0 && lat <= 32.0 && lng >= 118.0 && lng <= 122.0)
            {
                region = "华东地区";
            }
            else if (lat >= 22.0 && lat <= 25.0 && lng >= 112.0 && lng <= 115.0)
            {
                region = "华南地区";
            }
            else if (lat >= 29.0 && lat <= 32.0 && lng >= 106.0 && lng <= 110.0)
            {
                region = "华中地区";
            }
            else if (lat >= 43.0 && lat <= 48.0 && lng >= 123.0 && lng <= 135.0)
            {
                region = "东北地区";
            }
            else if (lat >= 32.0 && lat <= 38.0 && lng >= 103.0 && lng <= 111.0)
            {
                region = "西北地区";
            }
            else if (lat >= 26.0 && lat <= 30.0 && lng >= 97.0 && lng <= 105.0)
            {
                region = "西南地区";
            }
            else
            {
                region = "中国其他地区";
            }
        }
        else
        {
            region = "国外地区";
        }

        regionStats[region]++;
    }

    return regionStats;
}

QString GeographicDataPanel::formatCoordinateRange(const QList<QPair<double, double>> &coordinates)
{
    if (coordinates.isEmpty())
    {
        return "暂无坐标数据";
    }

    double minLat = coordinates.first().first;
    double maxLat = coordinates.first().first;
    double minLng = coordinates.first().second;
    double maxLng = coordinates.first().second;

    for (const auto &coord : coordinates)
    {
        minLat = qMin(minLat, coord.first);
        maxLat = qMax(maxLat, coord.first);
        minLng = qMin(minLng, coord.second);
        maxLng = qMax(maxLng, coord.second);
    }

    return QString("纬度: %1° ~ %2°\n经度: %3° ~ %4°\n覆盖范围: %.2f° × %.2f°")
        .arg(minLat, 0, 'f', 6)
        .arg(maxLat, 0, 'f', 6)
        .arg(minLng, 0, 'f', 6)
        .arg(maxLng, 0, 'f', 6)
        .arg(maxLat - minLat, 0, 'f', 6)
        .arg(maxLng - minLng, 0, 'f', 6);
}
