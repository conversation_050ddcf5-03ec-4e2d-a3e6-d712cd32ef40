@echo off
setlocal enabledelayedexpansion

REM LiteAPPStar Windows 构建脚本

set "SCRIPT_NAME=%~nx0"
set "USE_QMAKE=0"
set "RELEASE_BUILD=0"
set "CLEAN_ONLY=0"
set "INSTALL=0"
set "PACKAGE=0"
set "JOBS=%NUMBER_OF_PROCESSORS%"

REM 显示帮助信息
:show_help
echo LiteAPPStar Windows 构建脚本
echo.
echo 用法: %SCRIPT_NAME% [选项]
echo.
echo 选项:
echo   -h, --help          显示此帮助信息
echo   -c, --clean         清理构建文件
echo   -r, --release       发布版本构建 (默认为调试版本)
echo   -q, --qmake         使用qmake构建 (默认使用CMake)
echo   -j, --jobs N        并行构建任务数 (默认为CPU核心数)
echo   --install           构建后安装
echo   --package           创建安装包
echo.
echo 示例:
echo   %SCRIPT_NAME%                  # 使用CMake进行调试构建
echo   %SCRIPT_NAME% -r               # 使用CMake进行发布构建
echo   %SCRIPT_NAME% -q -r            # 使用qmake进行发布构建
echo   %SCRIPT_NAME% -c               # 清理构建文件
echo   %SCRIPT_NAME% -r --package     # 发布构建并创建安装包
goto :eof

REM 打印信息
:print_info
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM 检查依赖
:check_dependencies
call :print_info "检查构建依赖..."

REM 检查Qt5
qmake -v >nul 2>&1
if errorlevel 1 (
    call :print_error "未找到qmake，请安装Qt5开发环境并添加到PATH"
    exit /b 1
)

REM 检查CMake (如果使用CMake构建)
if "%USE_QMAKE%" neq "1" (
    cmake --version >nul 2>&1
    if errorlevel 1 (
        call :print_error "未找到cmake，请安装CMake并添加到PATH"
        exit /b 1
    )
)

REM 检查编译器
cl >nul 2>&1
if errorlevel 1 (
    g++ --version >nul 2>&1
    if errorlevel 1 (
        call :print_error "未找到C++编译器，请安装Visual Studio或MinGW"
        exit /b 1
    )
)

call :print_success "依赖检查完成"
goto :eof

REM 清理构建文件
:clean_build
call :print_info "清理构建文件..."

if exist build rmdir /s /q build
if exist bin rmdir /s /q bin
if exist Makefile del /q Makefile
if exist .qmake.stash del /q .qmake.stash

for /r %%f in (moc_*.cpp moc_*.h qrc_*.cpp ui_*.h *.o *.obj) do (
    if exist "%%f" del /q "%%f"
)

call :print_success "清理完成"
goto :eof

REM CMake构建
:cmake_build
call :print_info "使用CMake构建..."

set "BUILD_TYPE=Debug"
if "%RELEASE_BUILD%" == "1" set "BUILD_TYPE=Release"

call :print_info "构建类型: %BUILD_TYPE%"

REM 配置
cmake -B build -S . -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
if errorlevel 1 (
    call :print_error "CMake配置失败"
    exit /b 1
)

REM 构建
cmake --build build --config %BUILD_TYPE% -j %JOBS%
if errorlevel 1 (
    call :print_error "CMake构建失败"
    exit /b 1
)

call :print_success "CMake构建完成"

if "%INSTALL%" == "1" (
    call :print_info "安装应用程序..."
    cmake --install build
    if errorlevel 1 (
        call :print_error "安装失败"
        exit /b 1
    )
    call :print_success "安装完成"
)

if "%PACKAGE%" == "1" (
    call :print_info "创建安装包..."
    cd build
    cpack
    if errorlevel 1 (
        call :print_error "创建安装包失败"
        cd ..
        exit /b 1
    )
    cd ..
    call :print_success "安装包创建完成"
)

goto :eof

REM qmake构建
:qmake_build
call :print_info "使用qmake构建..."

set "CONFIG=debug"
if "%RELEASE_BUILD%" == "1" set "CONFIG=release"

call :print_info "构建类型: %CONFIG%"

REM 生成Makefile
qmake LiteAPPStar.pro CONFIG+=%CONFIG%
if errorlevel 1 (
    call :print_error "qmake配置失败"
    exit /b 1
)

REM 构建
if exist "C:\msys64\mingw64\bin\mingw32-make.exe" (
    C:\msys64\mingw64\bin\mingw32-make.exe -j %JOBS%
) else if exist "mingw32-make.exe" (
    mingw32-make.exe -j %JOBS%
) else (
    nmake
)

if errorlevel 1 (
    call :print_error "qmake构建失败"
    exit /b 1
)

call :print_success "qmake构建完成"
goto :eof

REM 解析命令行参数
:parse_args
if "%~1" == "" goto :args_done
if "%~1" == "-h" goto :show_help
if "%~1" == "--help" goto :show_help
if "%~1" == "-c" (
    set "CLEAN_ONLY=1"
    shift
    goto :parse_args
)
if "%~1" == "--clean" (
    set "CLEAN_ONLY=1"
    shift
    goto :parse_args
)
if "%~1" == "-r" (
    set "RELEASE_BUILD=1"
    shift
    goto :parse_args
)
if "%~1" == "--release" (
    set "RELEASE_BUILD=1"
    shift
    goto :parse_args
)
if "%~1" == "-q" (
    set "USE_QMAKE=1"
    shift
    goto :parse_args
)
if "%~1" == "--qmake" (
    set "USE_QMAKE=1"
    shift
    goto :parse_args
)
if "%~1" == "-j" (
    set "JOBS=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1" == "--jobs" (
    set "JOBS=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1" == "--install" (
    set "INSTALL=1"
    shift
    goto :parse_args
)
if "%~1" == "--package" (
    set "PACKAGE=1"
    shift
    goto :parse_args
)

call :print_error "未知选项: %~1"
goto :show_help

:args_done

REM 主程序
:main
call :print_info "LiteAPPStar Windows 构建脚本启动"

REM 解析命令行参数
call :parse_args %*

REM 如果只是清理，执行清理后退出
if "%CLEAN_ONLY%" == "1" (
    call :clean_build
    exit /b 0
)

REM 检查依赖
call :check_dependencies
if errorlevel 1 exit /b 1

REM 构建
if "%USE_QMAKE%" == "1" (
    call :qmake_build
) else (
    call :cmake_build
)

if errorlevel 1 exit /b 1

call :print_success "构建完成！"

REM 显示可执行文件位置
if "%USE_QMAKE%" == "1" (
    call :print_info "可执行文件位置: .\bin\LiteAPPStar.exe"
) else (
    call :print_info "可执行文件位置: .\build\bin\LiteAPPStar.exe"
)

goto :eof

REM 运行主程序
call :main %*
