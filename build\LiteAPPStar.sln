﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}"
	ProjectSection(ProjectDependencies) = postProject
		{7887CFA9-C773-33FF-8513-E01335E4B69D} = {7887CFA9-C773-33FF-8513-E01335E4B69D}
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E} = {A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{109BFA8A-BD33-3F53-9F51-DDE5C75EB555}"
	ProjectSection(ProjectDependencies) = postProject
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7} = {2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E} = {A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "LiteAPPStar", "LiteAPPStar.vcxproj", "{7887CFA9-C773-33FF-8513-E01335E4B69D}"
	ProjectSection(ProjectDependencies) = postProject
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E} = {A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{8F02FC2D-C605-3ACC-8E97-388207153973}"
	ProjectSection(ProjectDependencies) = postProject
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7} = {2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E} = {A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.Debug|x64.ActiveCfg = Debug|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.Debug|x64.Build.0 = Debug|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.Release|x64.ActiveCfg = Release|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.Release|x64.Build.0 = Release|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{109BFA8A-BD33-3F53-9F51-DDE5C75EB555}.Debug|x64.ActiveCfg = Debug|x64
		{109BFA8A-BD33-3F53-9F51-DDE5C75EB555}.Release|x64.ActiveCfg = Release|x64
		{109BFA8A-BD33-3F53-9F51-DDE5C75EB555}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{109BFA8A-BD33-3F53-9F51-DDE5C75EB555}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.Debug|x64.ActiveCfg = Debug|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.Debug|x64.Build.0 = Debug|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.Release|x64.ActiveCfg = Release|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.Release|x64.Build.0 = Release|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7887CFA9-C773-33FF-8513-E01335E4B69D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8F02FC2D-C605-3ACC-8E97-388207153973}.Debug|x64.ActiveCfg = Debug|x64
		{8F02FC2D-C605-3ACC-8E97-388207153973}.Release|x64.ActiveCfg = Release|x64
		{8F02FC2D-C605-3ACC-8E97-388207153973}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8F02FC2D-C605-3ACC-8E97-388207153973}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.Debug|x64.ActiveCfg = Debug|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.Debug|x64.Build.0 = Debug|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.Release|x64.ActiveCfg = Release|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.Release|x64.Build.0 = Release|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {161D0B50-D84B-3C40-9ACF-D904657FB631}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
