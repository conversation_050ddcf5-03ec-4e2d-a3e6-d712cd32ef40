﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{382C0630-6785-35D1-896D-94AF1BD79F76}"
	ProjectSection(ProjectDependencies) = postProject
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA} = {8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2} = {2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{7E1E06A6-3790-33C2-8FDB-153D537E12EA}"
	ProjectSection(ProjectDependencies) = postProject
		{382C0630-6785-35D1-896D-94AF1BD79F76} = {382C0630-6785-35D1-896D-94AF1BD79F76}
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2} = {2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "LiteAPPStar", "LiteAPPStar.vcxproj", "{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}"
	ProjectSection(ProjectDependencies) = postProject
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2} = {2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{4983C009-C7A9-3575-8FEF-59D53FE1CB6A}"
	ProjectSection(ProjectDependencies) = postProject
		{382C0630-6785-35D1-896D-94AF1BD79F76} = {382C0630-6785-35D1-896D-94AF1BD79F76}
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2} = {2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{382C0630-6785-35D1-896D-94AF1BD79F76}.Debug|x64.ActiveCfg = Debug|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.Debug|x64.Build.0 = Debug|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.Release|x64.ActiveCfg = Release|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.Release|x64.Build.0 = Release|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{382C0630-6785-35D1-896D-94AF1BD79F76}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7E1E06A6-3790-33C2-8FDB-153D537E12EA}.Debug|x64.ActiveCfg = Debug|x64
		{7E1E06A6-3790-33C2-8FDB-153D537E12EA}.Release|x64.ActiveCfg = Release|x64
		{7E1E06A6-3790-33C2-8FDB-153D537E12EA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7E1E06A6-3790-33C2-8FDB-153D537E12EA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.Debug|x64.ActiveCfg = Debug|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.Debug|x64.Build.0 = Debug|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.Release|x64.ActiveCfg = Release|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.Release|x64.Build.0 = Release|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4983C009-C7A9-3575-8FEF-59D53FE1CB6A}.Debug|x64.ActiveCfg = Debug|x64
		{4983C009-C7A9-3575-8FEF-59D53FE1CB6A}.Release|x64.ActiveCfg = Release|x64
		{4983C009-C7A9-3575-8FEF-59D53FE1CB6A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4983C009-C7A9-3575-8FEF-59D53FE1CB6A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.Debug|x64.ActiveCfg = Debug|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.Debug|x64.Build.0 = Debug|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.Release|x64.ActiveCfg = Release|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.Release|x64.Build.0 = Release|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1D413228-D439-39C9-92E4-84AED89DF4AF}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
