{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "D:/Program Files/cmake-3.28.1-windows-x86_64/bin/cmake.exe", "cpack": "D:/Program Files/cmake-3.28.1-windows-x86_64/bin/cpack.exe", "ctest": "D:/Program Files/cmake-3.28.1-windows-x86_64/bin/ctest.exe", "root": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 1, "string": "3.28.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-7f4c5efb5c00e873781b.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-1314ce34afd6a113d671.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-3c26879b94e1d45e11c0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-0e27a47c91923f5b5386.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-1314ce34afd6a113d671.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-7f4c5efb5c00e873781b.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-0e27a47c91923f5b5386.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-3c26879b94e1d45e11c0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}