#ifndef GEOINFOWIDGET_H
#define GEOINFOWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QGroupBox>
#include <QSplitter>
#include <QTimer>
#include <QMouseEvent>
#include <QTextEdit>
#include "tilemapview.h"
#include <QScrollArea>
#include <QResizeEvent>
#include <QEvent>
#include <QWheelEvent>

/**
 * @brief 地理信息显示组件
 *
 * 集成地图显示、位置信息、控制面板等功能
 */
class GeoInfoWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit GeoInfoWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~GeoInfoWidget();

protected:
    /**
     * @brief 窗口大小变化事件
     */
    void resizeEvent(QResizeEvent *event) override;

    // 事件过滤器已移除，TileMapView自己处理事件

signals:
    /**
     * @brief 坐标改变信号
     */
    void coordinateChanged(double lat, double lng);

    /**
     * @brief 鼠标坐标变化信号
     */
    void mouseCoordinateChanged(double lat, double lng);

    /**
     * @brief 缩放级别改变信号
     */
    void zoomLevelChanged(int zoom);

    /**
     * @brief 状态改变信号
     */
    void statusChanged(const QString &status);

public slots:
    /**
     * @brief 跳转到指定坐标
     * @param latitude 纬度
     * @param longitude 经度
     */
    void jumpToCoordinate(double latitude, double longitude);

    /**
     * @brief 获取当前位置
     */
    void getCurrentLocation();

    /**
     * @brief 切换地图图层
     * @param layerType 图层类型
     */
    void switchMapLayer(const QString &layerType);

    /**
     * @brief 设置地图缩放级别
     * @param zoomLevel 缩放级别
     */
    void setZoomLevel(int zoomLevel);

private slots:
    /**
     * @brief 坐标输入框回车事件
     */
    void onCoordinateInputEnter();

    /**
     * @brief 定位按钮点击事件
     */
    void onLocationButtonClicked();

    /**
     * @brief 图层切换事件
     */
    void onLayerChanged();

    /**
     * @brief 更新鼠标位置信息
     */
    void updateMousePosition();

    /**
     * @brief 地图坐标点击事件
     */
    void onMapCoordinateClicked(double lat, double lng);

    /**
     * @brief 地图中心改变事件
     */
    void onMapCenterChanged(double lat, double lng);

    /**
     * @brief 地图缩放级别改变事件
     */
    void onMapZoomChanged(int zoom);

    /**
     * @brief 鼠标坐标改变事件
     */
    void onMouseCoordinateChanged(double lat, double lng);

private:
    /**
     * @brief 初始化UI组件
     */
    void initializeUI();

    /**
     * @brief 创建地图显示区域
     */
    void createMapArea();

    /**
     * @brief 创建控制面板
     */
    void createControlPanel();

    /**
     * @brief 创建信息显示面板
     */
    void createInfoPanel();

    /**
     * @brief 创建地图控制组件
     */
    void createMapControls();

    /**
     * @brief 创建地图信息面板
     */
    void createMapInfoPanel();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 初始化地图
     */
    void initializeMap();

    /**
     * @brief 加载地图HTML
     */
    void loadMapHTML();

    /**
     * @brief 更新坐标显示
     */
    void updateCoordinateDisplay(double lat, double lng);

    /**
     * @brief 更新缩放级别显示
     */
    void updateZoomDisplay(int zoom);

    /**
     * @brief 更新比例尺显示
     */
    void updateScaleDisplay(int zoom);

    // 地图事件处理方法已移除，现在由TileMapView直接处理

    /**
     * @brief 检查网络连接状态
     */
    bool checkNetworkConnection();

    /**
     * @brief 切换到离线模式
     */
    void switchToOfflineMode();

    /**
     * @brief 切换到在线模式
     */
    void switchToOnlineMode();

    /**
     * @brief 显示错误信息
     */
    void showError(const QString &error);

    /**
     * @brief 更新状态显示
     */
    void updateStatus(const QString &status);

private:
    // UI组件
    QVBoxLayout *m_mainLayout; ///< 主布局
    // QSplitter *m_splitter;     ///< 分割器（已移除）

    // 地图区域
    QWidget *m_mapContainer; ///< 地图容器
    TileMapView *m_mapView;  ///< 瓦片地图显示组件

    // 控制面板
    QWidget *m_controlPanel;    ///< 控制面板
    QPushButton *m_locationBtn; ///< 定位按钮
    QComboBox *m_layerCombo;    ///< 图层选择
    QLineEdit *m_latInput;      ///< 纬度输入
    QLineEdit *m_lngInput;      ///< 经度输入
    QPushButton *m_jumpBtn;     ///< 跳转按钮

    // 信息显示面板
    QWidget *m_infoPanel;        ///< 信息面板
    QLabel *m_coordSystemLabel;  ///< 坐标系标签
    QLabel *m_currentCoordLabel; ///< 当前坐标标签
    QLabel *m_zoomLevelLabel;    ///< 缩放级别标签
    QLabel *m_scaleLabel;        ///< 比例尺标签
    QLabel *m_mouseCoordLabel;   ///< 鼠标坐标标签

    // 数据成员
    QTimer *m_updateTimer;  ///< 更新定时器
    double m_currentLat;    ///< 当前纬度
    double m_currentLng;    ///< 当前经度
    int m_currentZoom;      ///< 当前缩放级别
    QString m_currentLayer; ///< 当前图层类型

    // 地图控制面板
    QWidget *m_mapControlPanel; ///< 地图控制面板
    QWidget *m_mapInfoPanel;    ///< 地图信息面板

    // 错误处理和状态
    bool m_isOnlineMode;          ///< 是否在线模式
    QString m_lastError;          ///< 最后的错误信息
    QLabel *m_statusLabel;        ///< 状态标签
    QWidget *m_zoomControlWidget; ///< 缩放控制按钮容器

    // 常量
    static const double DEFAULT_LAT; ///< 默认纬度
    static const double DEFAULT_LNG; ///< 默认经度
    static const int DEFAULT_ZOOM;   ///< 默认缩放级别
};

#endif // GEOINFOWIDGET_H
