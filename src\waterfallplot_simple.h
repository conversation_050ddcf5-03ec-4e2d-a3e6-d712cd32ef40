#ifndef WATERFALLPLOT_SIMPLE_H
#define WATERFALLPLOT_SIMPLE_H

#include <QWidget>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QVector>
#include <QColor>
#include <QFont>
#include <QFontMetrics>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QRandomGenerator>
#include <QDateTime>
#include <cmath>

/**
 * @brief 简化版瀑布图显示组件
 * 
 * 基于QPainter实现的轻量级时频域瀑布图显示组件
 * 采用现代紫绿色渐变配色方案
 */
class WaterfallPlotSimple : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 颜色映射方案枚举
     */
    enum ColorScheme {
        PurpleGreen = 0,    ///< 紫绿色渐变（默认）
        BlueRed = 1,        ///< 蓝红色渐变
        Grayscale = 2       ///< 灰度渐变
    };

    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit WaterfallPlotSimple(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~WaterfallPlotSimple();

    /**
     * @brief 设置频率范围
     * @param startFreq 起始频率 (Hz)
     * @param endFreq 结束频率 (Hz)
     */
    void setFrequencyRange(double startFreq, double endFreq);

    /**
     * @brief 设置时间窗口
     * @param timeWindow 时间窗口长度 (秒)
     */
    void setTimeWindow(double timeWindow);

    /**
     * @brief 设置幅度范围
     * @param minAmplitude 最小幅度 (dBm)
     * @param maxAmplitude 最大幅度 (dBm)
     */
    void setAmplitudeRange(double minAmplitude, double maxAmplitude);

    /**
     * @brief 添加新的频谱数据行
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     */
    void addSpectrumData(const QVector<double> &frequencies, const QVector<double> &amplitudes);

    /**
     * @brief 设置颜色映射方案
     * @param scheme 颜色方案
     */
    void setColorScheme(ColorScheme scheme);

    /**
     * @brief 清除所有历史数据
     */
    void clearHistory();

    /**
     * @brief 获取当前频率范围
     * @return 频率范围 (起始频率, 结束频率)
     */
    QPair<double, double> frequencyRange() const;

    /**
     * @brief 获取当前时间窗口
     * @return 时间窗口长度 (秒)
     */
    double timeWindow() const { return m_timeWindow; }

    /**
     * @brief 获取当前幅度范围
     * @return 幅度范围 (最小幅度, 最大幅度)
     */
    QPair<double, double> amplitudeRange() const;

signals:
    /**
     * @brief 频率范围改变信号
     * @param startFreq 起始频率
     * @param endFreq 结束频率
     */
    void frequencyRangeChanged(double startFreq, double endFreq);

    /**
     * @brief 时间位置点击信号
     * @param frequency 点击的频率
     * @param time 点击的时间
     * @param amplitude 对应的幅度
     */
    void timeFrequencyClicked(double frequency, double time, double amplitude);

    /**
     * @brief 数据更新信号
     */
    void dataUpdated();

protected:
    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;

    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标滚轮事件
     */
    void wheelEvent(QWheelEvent *event) override;

    /**
     * @brief 右键菜单事件
     */
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    /**
     * @brief 清除历史数据槽函数
     */
    void clearData();

    /**
     * @brief 设置紫绿色方案槽函数
     */
    void setPurpleGreenScheme();

    /**
     * @brief 设置蓝红色方案槽函数
     */
    void setBlueRedScheme();

    /**
     * @brief 设置灰度方案槽函数
     */
    void setGrayscaleScheme();

private:
    /**
     * @brief 绘制背景和坐标轴
     * @param painter 绘制器
     */
    void drawBackground(QPainter &painter);

    /**
     * @brief 绘制瀑布图数据
     * @param painter 绘制器
     */
    void drawWaterfall(QPainter &painter);

    /**
     * @brief 绘制颜色标尺
     * @param painter 绘制器
     */
    void drawColorScale(QPainter &painter);

    /**
     * @brief 根据幅度值获取颜色
     * @param amplitude 幅度值
     * @return 对应的颜色
     */
    QColor amplitudeToColor(double amplitude) const;

    /**
     * @brief 频率转换为X坐标
     * @param frequency 频率
     * @return X坐标
     */
    int frequencyToX(double frequency) const;

    /**
     * @brief 时间转换为Y坐标
     * @param time 时间
     * @return Y坐标
     */
    int timeToY(double time) const;

    /**
     * @brief X坐标转换为频率
     * @param x X坐标
     * @return 频率
     */
    double xToFrequency(int x) const;

    /**
     * @brief Y坐标转换为时间
     * @param y Y坐标
     * @return 时间
     */
    double yToTime(int y) const;

    /**
     * @brief 格式化频率显示
     * @param frequency 频率值
     * @return 格式化的频率字符串
     */
    QString formatFrequency(double frequency) const;

    /**
     * @brief 格式化时间显示
     * @param time 时间值
     * @return 格式化的时间字符串
     */
    QString formatTime(double time) const;

private:
    // 数据存储
    QVector<double> m_frequencies;           ///< 频率数据
    QVector<QVector<double>> m_historyData;  ///< 历史幅度数据
    QVector<double> m_timeStamps;            ///< 时间戳数据

    // 显示范围
    double m_startFreq;              ///< 起始频率
    double m_endFreq;                ///< 结束频率
    double m_timeWindow;             ///< 时间窗口长度
    double m_minAmplitude;           ///< 最小幅度
    double m_maxAmplitude;           ///< 最大幅度

    // 配置参数
    ColorScheme m_colorScheme;       ///< 颜色映射方案
    int m_maxHistorySize;            ///< 最大历史数据大小

    // 绘图区域
    QRect m_plotRect;                ///< 绘图区域
    QRect m_colorScaleRect;          ///< 颜色标尺区域
    int m_marginLeft;                ///< 左边距
    int m_marginRight;               ///< 右边距
    int m_marginTop;                 ///< 上边距
    int m_marginBottom;              ///< 下边距

    // 右键菜单
    QMenu *m_contextMenu;            ///< 右键菜单
    QAction *m_clearDataAction;      ///< 清除数据动作
    QMenu *m_colorSchemeMenu;        ///< 颜色方案菜单
    QAction *m_purpleGreenAction;    ///< 紫绿色方案动作
    QAction *m_blueRedAction;        ///< 蓝红色方案动作
    QAction *m_grayscaleAction;      ///< 灰度方案动作

    // 样式配置
    static const QColor BACKGROUND_COLOR;   ///< 背景颜色
    static const QColor AXIS_COLOR;         ///< 坐标轴颜色
    static const QColor TEXT_COLOR;         ///< 文本颜色
};

#endif // WATERFALLPLOT_SIMPLE_H
