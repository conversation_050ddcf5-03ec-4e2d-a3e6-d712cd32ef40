#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlRecord>
#include <QVariant>
#include <QVariantMap>
#include <QVariantList>
#include <QString>
#include <QStringList>
#include <QMutex>
#include <QTimer>
#include <QQueue>
#include <QThread>
#include <QDebug>
#include <QDateTime>
#include <memory>

#include "configmanager.h"

// 前向声明
class DatabaseConnection;
class ConnectionPool;
class QueryResult;

/**
 * @brief 查询结果类
 */
class QueryResult
{
public:
    QueryResult();
    QueryResult(const QSqlQuery &query);
    ~QueryResult();

    // 禁用拷贝构造函数和拷贝赋值操作符
    QueryResult(const QueryResult &) = delete;
    QueryResult &operator=(const QueryResult &) = delete;

    // 移动构造函数和移动赋值操作符
    QueryResult(QueryResult &&other) noexcept;
    QueryResult &operator=(QueryResult &&other) noexcept;

    // 结果访问
    bool isValid() const;
    bool next();
    bool previous();
    bool first();
    bool last();
    void beforeFirst();
    void afterLast();

    // 数据访问
    QVariant value(int index) const;
    QVariant value(const QString &name) const;
    QVariantMap record() const;
    QVariantList recordList() const;

    // 结果信息
    int size() const;
    int numRowsAffected() const;
    QString lastError() const;
    bool hasError() const;

    // 字段信息
    QStringList fieldNames() const;
    int fieldIndex(const QString &name) const;

private:
    class QueryResultPrivate;
    std::unique_ptr<QueryResultPrivate> d;
};

/**
 * @brief 数据库连接类
 */
class DatabaseConnection
{
public:
    explicit DatabaseConnection(const QString &connectionName, const DatabaseConfig &config);
    ~DatabaseConnection();

    // 连接管理
    bool open();
    void close();
    bool isOpen() const;
    bool isValid() const;

    // 查询执行
    QueryResult executeQuery(const QString &sql, const QVariantList &params = QVariantList());
    bool executeNonQuery(const QString &sql, const QVariantList &params = QVariantList());

    // 事务管理
    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    bool isInTransaction() const;

    // 连接信息
    QString connectionName() const;
    QString lastError() const;
    QDateTime lastUsed() const;
    void updateLastUsed();

    // 连接测试
    bool testConnection();

private:
    class DatabaseConnectionPrivate;
    std::unique_ptr<DatabaseConnectionPrivate> d;
};

/**
 * @brief 连接池类
 */
class ConnectionPool : public QObject
{
    Q_OBJECT

public:
    explicit ConnectionPool(const EnvironmentConfig &config, QObject *parent = nullptr);
    ~ConnectionPool();

    // 连接管理
    std::shared_ptr<DatabaseConnection> getConnection();
    void returnConnection(std::shared_ptr<DatabaseConnection> connection);

    // 池管理
    void initialize();
    void cleanup();
    void resize(int minConnections, int maxConnections);

    // 池状态
    int activeConnections() const;
    int idleConnections() const;
    int totalConnections() const;
    bool isHealthy() const;

    // 配置更新
    void updateConfig(const EnvironmentConfig &config);

signals:
    void connectionCreated(const QString &connectionName);
    void connectionDestroyed(const QString &connectionName);
    void poolError(const QString &error);

private slots:
    void cleanupIdleConnections();
    void checkConnectionHealth();

private:
    void createConnection();
    bool createConnectionWithTimeout();
    void createConnectionInternal();
    void removeConnection(std::shared_ptr<DatabaseConnection> connection);
    QString generateConnectionName();
    void resizeInternal(int minConnections, int maxConnections);

private:
    class ConnectionPoolPrivate;
    std::unique_ptr<ConnectionPoolPrivate> d;
};

/**
 * @brief 数据库管理器类
 *
 * 提供统一的数据库访问接口，包括连接池管理、自动重连、
 * CRUD操作、事务管理和错误处理
 */
class DatabaseManager : public QObject
{
    Q_OBJECT

public:
    // 单例模式
    static DatabaseManager *instance();
    static void destroyInstance();

    // 初始化和配置
    bool initialize(const QString &configPath = "");
    bool initialize(const EnvironmentConfig &config);
    void shutdown();

    // 环境管理
    bool switchEnvironment(const QString &environment);
    QString currentEnvironment() const;
    QStringList availableEnvironments() const;

    // 连接管理
    bool isConnected() const;
    bool testConnection();
    void reconnect();

    // 查询操作
    QueryResult select(const QString &table,
                       const QStringList &columns = QStringList(),
                       const QString &where = QString(),
                       const QVariantList &params = QVariantList(),
                       const QString &orderBy = QString(),
                       int limit = -1,
                       int offset = 0);

    QueryResult executeQuery(const QString &sql, const QVariantList &params = QVariantList());

    // 数据操作
    bool insert(const QString &table, const QVariantMap &data);
    bool update(const QString &table, const QVariantMap &data, const QString &where, const QVariantList &params = QVariantList());
    bool remove(const QString &table, const QString &where, const QVariantList &params = QVariantList());

    // 批量操作
    bool batchInsert(const QString &table, const QVariantList &dataList);
    bool batchUpdate(const QString &table, const QVariantList &dataList, const QStringList &whereColumns);

    // 事务管理
    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    bool isInTransaction() const;

    // 表操作
    bool tableExists(const QString &tableName);
    bool createTable(const QString &tableName, const QString &schema);
    bool dropTable(const QString &tableName);
    QStringList tableNames();

    // 统计信息
    int count(const QString &table, const QString &where = QString(), const QVariantList &params = QVariantList());

    // 错误处理
    QString lastError() const;
    bool hasError() const;
    void clearError();

    // 配置信息
    EnvironmentConfig currentConfig() const;
    ConnectionPool *connectionPool() const;

signals:
    void connected();
    void disconnected();
    void error(const QString &error);
    void environmentChanged(const QString &environment);
    void queryExecuted(const QString &sql, int executionTime);

private slots:
    void onConfigChanged(const QString &environment);
    void onConnectionPoolError(const QString &error);

private:
    explicit DatabaseManager(QObject *parent = nullptr);
    ~DatabaseManager();

    // 禁用拷贝构造和赋值
    DatabaseManager(const DatabaseManager &) = delete;
    DatabaseManager &operator=(const DatabaseManager &) = delete;

    // 内部方法
    bool initializeConnectionPool();
    void setupAutoReconnect();
    QString buildSelectSql(const QString &table, const QStringList &columns,
                           const QString &where, const QString &orderBy,
                           int limit, int offset) const;
    QString buildInsertSql(const QString &table, const QVariantMap &data) const;
    QString buildUpdateSql(const QString &table, const QVariantMap &data, const QString &where) const;
    QString buildDeleteSql(const QString &table, const QString &where) const;

    void logQuery(const QString &sql, const QVariantList &params, int executionTime) const;
    void setLastError(const QString &error);

private:
    static DatabaseManager *s_instance;
    static QMutex s_mutex;

    class DatabaseManagerPrivate;
    std::unique_ptr<DatabaseManagerPrivate> d;
};

// 便利宏定义
#define DB_MANAGER DatabaseManager::instance()

#endif // DATABASEMANAGER_H
