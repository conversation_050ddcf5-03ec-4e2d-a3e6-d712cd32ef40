{"artifacts": [{"path": "bin/MinSizeRel/LiteAPPStar.exe"}, {"path": "bin/MinSizeRel/LiteAPPStar.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_property", "_populate_Quick_target_properties", "find_package", "include", "add_definitions", "include_directories"], "files": ["CMakeLists.txt", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 118, "parent": 0}, {"command": 1, "file": 0, "line": 185, "parent": 0}, {"command": 2, "file": 0, "line": 126, "parent": 0}, {"command": 5, "file": 0, "line": 39, "parent": 0}, {"file": 2, "parent": 4}, {"command": 5, "file": 2, "line": 28, "parent": 5}, {"file": 1, "parent": 6}, {"command": 4, "file": 1, "line": 185, "parent": 7}, {"command": 3, "file": 1, "line": 45, "parent": 8}, {"command": 5, "file": 2, "line": 28, "parent": 5}, {"file": 4, "parent": 10}, {"command": 6, "file": 4, "line": 216, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 110, "parent": 13}, {"command": 7, "file": 0, "line": 24, "parent": 0}, {"command": 7, "file": 0, "line": 23, "parent": 0}, {"command": 8, "file": 0, "line": 91, "parent": 0}, {"command": 8, "file": 0, "line": 92, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /utf-8 /FS /W3 /wd4251 /wd4275 /wd4996 /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}], "defines": [{"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_NETWORK_LIB"}, {"backtrace": 3, "define": "QT_NO_DEBUG"}, {"backtrace": 3, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 3, "define": "QT_QML_LIB"}, {"backtrace": 3, "define": "QT_QUICKWIDGETS_LIB"}, {"backtrace": 3, "define": "QT_QUICK_LIB"}, {"backtrace": 3, "define": "QT_SQL_LIB"}, {"backtrace": 3, "define": "QT_SVG_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}, {"backtrace": 15, "define": "_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS"}, {"backtrace": 16, "define": "_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING"}], "includes": [{"backtrace": 0, "path": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include_MinSizeRel"}, {"backtrace": 17, "path": "E:/code/LiteAPPStar/include"}, {"backtrace": 18, "path": "E:/code/LiteAPPStar/build"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 40]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "LiteAPPStar::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "E:/code/LiteAPPStar/install"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /utf-8 /FS /W3 /wd4251 /wd4275 /wd4996 /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:windows", "role": "flags"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5QuickWidgets.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Svg.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Quick.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5QmlModels.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Qml.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Network.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\qtmain.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "LiteAPPStar", "nameOnDisk": "LiteAPPStar.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 40]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 35, 36, 37, 38]}, {"name": "", "sourceIndexes": [30, 31, 32, 33, 34, 39]}, {"name": "CMake Rules", "sourceIndexes": [41, 42]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/comprehensiveviewwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/configmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/databasemanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/geographicdatapanel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/geoinfowidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/localtilemanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/qgismapwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/qtgeoinfowidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/qtlocationmapview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/signalanalysiswidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/spectrumplot_simple.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/tilemapview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/waterfallplot_simple.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/comprehensiveviewwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/configmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/databasemanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/geographicdatapanel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/geoinfowidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/localtilemanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/qgismapwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/qtgeoinfowidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/qtlocationmapview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/signalanalysiswidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/spectrumplot_simple.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/tilemapview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/waterfallplot_simple.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/comprehensive_view.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/geographicdatapanel.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/signalanalysis.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "resources/resources.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/include_MinSizeRel/src/ui_comprehensive_view.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/include_MinSizeRel/src/ui_geographicdatapanel.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/include_MinSizeRel/src/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/include_MinSizeRel/src/ui_signalanalysis.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/autouic_MinSizeRel.stamp", "sourceGroupIndex": 2}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/LiteAPPStar_autogen/3YJK5W5UP7/qrc_resources.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/6ced8156163e1d06b8bc9dabf4ea54b5/autouic_(CONFIG).stamp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/a949e9403c7fc38777131ca2bcb1be2d/qrc_resources.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}