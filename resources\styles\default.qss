/* LiteAPPStar 默认样式表 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f0f0f0;
    color: #333333;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #d0d0d0;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #e0e0e0;
}

QMenuBar::item:pressed {
    background-color: #d0d0d0;
}

/* 菜单样式 */
QMenu {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 3px;
    padding: 2px;
}

QMenu::item {
    padding: 6px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #e0e0e0;
}

QMenu::separator {
    height: 1px;
    background-color: #d0d0d0;
    margin: 2px 0px;
}

/* 工具栏样式 */
QToolBar {
    background-color: #f8f8f8;
    border: 1px solid #d0d0d0;
    padding: 2px;
    spacing: 2px;
}

QToolBar::handle {
    background-color: #d0d0d0;
    width: 8px;
    margin: 2px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px;
    margin: 1px;
}

QToolButton:hover {
    background-color: #e0e0e0;
    border-color: #c0c0c0;
}

QToolButton:pressed {
    background-color: #d0d0d0;
    border-color: #a0a0a0;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #f8f8f8;
    border-top: 1px solid #d0d0d0;
    padding: 2px;
}

/* 文本编辑器样式 */
QTextEdit {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 3px;
    padding: 4px;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 10pt;
    line-height: 1.4;
}

QTextEdit:focus {
    border-color: #4a90e2;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #d0d0d0;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

QSplitter::handle:pressed {
    background-color: #4a90e2;
}

/* 按钮样式 */
QPushButton {
    background-color: #ffffff;
    border: 1px solid #d0d0d0;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 9pt;
}

QPushButton:hover {
    background-color: #e0e0e0;
    border-color: #c0c0c0;
}

QPushButton:pressed {
    background-color: #d0d0d0;
    border-color: #a0a0a0;
}

QPushButton:default {
    border-color: #4a90e2;
    font-weight: bold;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}
