/**
 * LiteAPPStar 地图边界限制功能使用示例
 * 
 * 本文件展示如何使用新实现的地图边界限制和自动矫正功能
 */

#include "qgismapwidget.h"
#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QLabel>
#include <QDebug>

class BoundaryTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    BoundaryTestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        connectSignals();
    }

private slots:
    void testNormalCoordinates()
    {
        qDebug() << "=== 测试正常坐标 ===";
        
        // 测试北京坐标
        m_mapWidget->setCenter(39.9042, 116.4074);
        
        // 测试上海坐标
        m_mapWidget->setCenter(31.2304, 121.4737);
        
        // 测试纽约坐标
        m_mapWidget->setCenter(40.7128, -74.0060);
    }
    
    void testBoundaryCoordinates()
    {
        qDebug() << "=== 测试边界坐标 ===";
        
        // 测试北极点
        m_mapWidget->setCenter(90.0, 0.0);
        
        // 测试南极点
        m_mapWidget->setCenter(-90.0, 0.0);
        
        // 测试东边界
        m_mapWidget->setCenter(0.0, 180.0);
        
        // 测试西边界
        m_mapWidget->setCenter(0.0, -180.0);
    }
    
    void testInvalidCoordinates()
    {
        qDebug() << "=== 测试无效坐标 ===";
        
        // 测试超出北边界
        m_mapWidget->setCenter(95.0, 0.0);
        
        // 测试超出南边界
        m_mapWidget->setCenter(-95.0, 0.0);
        
        // 测试超出东边界
        m_mapWidget->setCenter(0.0, 185.0);
        
        // 测试超出西边界
        m_mapWidget->setCenter(0.0, -185.0);
    }
    
    void testLongitudeWrapping()
    {
        qDebug() << "=== 测试经度循环 ===";
        
        // 测试经度+360度（应该循环到原位置）
        m_mapWidget->setCenter(39.9042, 116.4074 + 360.0);
        
        // 测试经度-360度（应该循环到原位置）
        m_mapWidget->setCenter(39.9042, 116.4074 - 360.0);
        
        // 测试多次循环
        m_mapWidget->setCenter(39.9042, 116.4074 + 720.0);
        m_mapWidget->setCenter(39.9042, 116.4074 - 720.0);
    }
    
    void testLocationFunction()
    {
        qDebug() << "=== 测试定位功能 ===";
        
        // 测试正常定位
        m_mapWidget->locateToCoordinate(39.9042, 116.4074);
        
        // 测试边界定位
        m_mapWidget->locateToCoordinate(90.0, 180.0);
        
        // 测试无效定位（应该显示错误对话框）
        m_mapWidget->locateToCoordinate(95.0, 185.0);
    }
    
    void runFullTest()
    {
        qDebug() << "=== 运行完整边界约束测试 ===";
        m_mapWidget->testBoundaryConstraints();
    }
    
    void testCustomCoordinate()
    {
        bool ok1, ok2;
        double lat = m_latEdit->text().toDouble(&ok1);
        double lng = m_lngEdit->text().toDouble(&ok2);
        
        if (ok1 && ok2)
        {
            qDebug() << "=== 测试自定义坐标 ===" << lat << "," << lng;
            m_mapWidget->setCenter(lat, lng);
        }
        else
        {
            qDebug() << "无效的坐标输入";
        }
    }

private:
    void setupUI()
    {
        auto *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        auto *layout = new QVBoxLayout(centralWidget);
        
        // 创建地图组件
        m_mapWidget = new QgisMapWidget(this);
        layout->addWidget(m_mapWidget);
        
        // 创建测试按钮
        auto *buttonLayout = new QHBoxLayout();
        
        auto *normalBtn = new QPushButton("测试正常坐标", this);
        auto *boundaryBtn = new QPushButton("测试边界坐标", this);
        auto *invalidBtn = new QPushButton("测试无效坐标", this);
        auto *wrapBtn = new QPushButton("测试经度循环", this);
        auto *locateBtn = new QPushButton("测试定位功能", this);
        auto *fullTestBtn = new QPushButton("完整测试", this);
        
        buttonLayout->addWidget(normalBtn);
        buttonLayout->addWidget(boundaryBtn);
        buttonLayout->addWidget(invalidBtn);
        buttonLayout->addWidget(wrapBtn);
        buttonLayout->addWidget(locateBtn);
        buttonLayout->addWidget(fullTestBtn);
        
        layout->addLayout(buttonLayout);
        
        // 创建自定义坐标输入
        auto *customLayout = new QHBoxLayout();
        customLayout->addWidget(new QLabel("纬度:", this));
        m_latEdit = new QLineEdit("39.9042", this);
        customLayout->addWidget(m_latEdit);
        
        customLayout->addWidget(new QLabel("经度:", this));
        m_lngEdit = new QLineEdit("116.4074", this);
        customLayout->addWidget(m_lngEdit);
        
        auto *customBtn = new QPushButton("测试自定义坐标", this);
        customLayout->addWidget(customBtn);
        
        layout->addLayout(customLayout);
        
        // 连接信号
        connect(normalBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testNormalCoordinates);
        connect(boundaryBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testBoundaryCoordinates);
        connect(invalidBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testInvalidCoordinates);
        connect(wrapBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testLongitudeWrapping);
        connect(locateBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testLocationFunction);
        connect(fullTestBtn, &QPushButton::clicked, this, &BoundaryTestWindow::runFullTest);
        connect(customBtn, &QPushButton::clicked, this, &BoundaryTestWindow::testCustomCoordinate);
        
        setWindowTitle("LiteAPPStar 地图边界限制测试");
        resize(1200, 800);
    }
    
    void connectSignals()
    {
        // 可以在这里连接地图组件的信号来监控边界矫正
        // 例如：监控地图中心变更
    }

private:
    QgisMapWidget *m_mapWidget;
    QLineEdit *m_latEdit;
    QLineEdit *m_lngEdit;
};

// 主函数示例
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    BoundaryTestWindow window;
    window.show();
    
    return app.exec();
}

#include "BOUNDARY_USAGE_EXAMPLE.moc"

/**
 * 编译和运行说明：
 * 
 * 1. 将此文件添加到项目中
 * 2. 在CMakeLists.txt中添加：
 *    add_executable(boundary_test BOUNDARY_USAGE_EXAMPLE.cpp)
 *    target_link_libraries(boundary_test ${QT_LIBRARIES})
 * 
 * 3. 编译运行：
 *    mkdir build && cd build
 *    cmake ..
 *    make boundary_test
 *    ./boundary_test
 * 
 * 4. 观察控制台输出，查看边界约束的工作情况
 * 
 * 预期行为：
 * - 正常坐标：直接设置成功
 * - 边界坐标：设置到边界值
 * - 无效坐标：自动矫正到有效范围
 * - 经度循环：自动循环到-180到+180范围内
 * - 定位功能：无效坐标显示错误对话框
 */
