# LiteAPPStar VSCode 调试说明

## 编译状态
✅ **编译成功** - 项目已成功编译，可以进行调试

## 快速开始

### 1. 编译项目
运行以下任一脚本来编译项目：
```powershell
# PowerShell脚本（推荐）
.\quick-build.ps1

# 或者直接使用CMake命令
cmake --build build --config Debug
```

### 2. VSCode调试
1. 在VSCode中打开项目
2. 按 **F5** 或者点击"运行和调试"
3. 选择 **"Debug LiteAPPStar (CMake - VS2022)"** 配置
4. 程序将自动编译并启动调试

### 3. 测试运行
如果只想运行程序而不调试：
```powershell
.\test-run.ps1
```

## 调试配置详情

### 当前可用的调试配置：
1. **Debug LiteAPPStar (CMake - VS2022)** - 推荐使用
   - 使用Visual Studio 2022编译器
   - 自动设置Qt环境变量
   - 支持断点调试

2. **Debug LiteAPPStar (CMake - MinGW)** - 备用选项
   - 使用MinGW编译器
   - 适用于没有Visual Studio的环境

3. **Debug LiteAPPStar (qmake)** - 传统方式
   - 使用qmake构建系统

### 环境要求
- ✅ Qt5.14.2 (MSVC 2017 64-bit)
- ✅ Visual Studio 2022
- ✅ CMake 3.20+
- ✅ VSCode C++ 扩展

## 文件结构
```
LiteAPPStar2/
├── build/                  # CMake构建目录
│   └── bin/Debug/          # 调试版本可执行文件
├── src/                    # 源代码
├── include/                # 头文件
├── resources/              # 资源文件
├── .vscode/                # VSCode配置
│   ├── launch.json         # 调试配置
│   ├── tasks.json          # 构建任务
│   └── settings.json       # 项目设置
├── CMakeLists.txt          # CMake配置
├── quick-build.ps1         # 快速构建脚本
└── test-run.ps1           # 测试运行脚本
```

## 常见问题

### Q: F5调试时提示找不到可执行文件
**A:** 运行 `.\quick-build.ps1` 重新编译项目

### Q: 程序启动时提示缺少Qt DLL
**A:** 检查Qt安装路径是否正确：`D:\Qt\Qt5.14.2\5.14.2\msvc2017_64`

### Q: 编译失败
**A:** 
1. 检查Visual Studio 2022是否正确安装
2. 检查Qt5.14.2是否正确安装
3. 运行 `cmake --build build --config Debug` 查看详细错误信息

## 调试技巧
1. 在代码中设置断点（点击行号左侧）
2. 使用F10单步执行，F11进入函数
3. 在调试控制台中查看变量值
4. 使用监视窗口监控特定变量

## 支持的平台
- ✅ Windows 10/11 (主要支持)
- ⚠️ Linux (需要调整Qt路径)
- ⚠️ macOS (需要调整Qt路径)

---
**注意：** 确保在调试前项目已成功编译，可执行文件位于 `build/bin/Debug/LiteAPPStar.exe`
