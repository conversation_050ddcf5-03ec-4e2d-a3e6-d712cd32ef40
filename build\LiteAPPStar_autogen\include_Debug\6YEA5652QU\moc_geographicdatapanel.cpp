/****************************************************************************
** Meta object code from reading C++ file 'geographicdatapanel.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../include/geographicdatapanel.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'geographicdatapanel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_GeographicDataPanel_t {
    QByteArrayData data[13];
    char stringdata0[163];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_GeographicDataPanel_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_GeographicDataPanel_t qt_meta_stringdata_GeographicDataPanel = {
    {
QT_MOC_LITERAL(0, 0, 19), // "GeographicDataPanel"
QT_MOC_LITERAL(1, 20, 11), // "panelClosed"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 16), // "refreshRequested"
QT_MOC_LITERAL(4, 50, 11), // "rowSelected"
QT_MOC_LITERAL(5, 62, 3), // "row"
QT_MOC_LITERAL(6, 66, 4), // "data"
QT_MOC_LITERAL(7, 71, 17), // "onTableRowClicked"
QT_MOC_LITERAL(8, 89, 6), // "column"
QT_MOC_LITERAL(9, 96, 12), // "onTabChanged"
QT_MOC_LITERAL(10, 109, 5), // "index"
QT_MOC_LITERAL(11, 115, 23), // "onShowAnimationFinished"
QT_MOC_LITERAL(12, 139, 23) // "onHideAnimationFinished"

    },
    "GeographicDataPanel\0panelClosed\0\0"
    "refreshRequested\0rowSelected\0row\0data\0"
    "onTableRowClicked\0column\0onTabChanged\0"
    "index\0onShowAnimationFinished\0"
    "onHideAnimationFinished"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_GeographicDataPanel[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x06 /* Public */,
       3,    0,   50,    2, 0x06 /* Public */,
       4,    2,   51,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    2,   56,    2, 0x08 /* Private */,
       9,    1,   61,    2, 0x08 /* Private */,
      11,    0,   64,    2, 0x08 /* Private */,
      12,    0,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QStringList,    5,    6,

 // slots: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    5,    8,
    QMetaType::Void, QMetaType::Int,   10,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void GeographicDataPanel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<GeographicDataPanel *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->panelClosed(); break;
        case 1: _t->refreshRequested(); break;
        case 2: _t->rowSelected((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QStringList(*)>(_a[2]))); break;
        case 3: _t->onTableRowClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->onTabChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->onShowAnimationFinished(); break;
        case 6: _t->onHideAnimationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (GeographicDataPanel::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeographicDataPanel::panelClosed)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (GeographicDataPanel::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeographicDataPanel::refreshRequested)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (GeographicDataPanel::*)(int , const QStringList & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeographicDataPanel::rowSelected)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject GeographicDataPanel::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_GeographicDataPanel.data,
    qt_meta_data_GeographicDataPanel,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *GeographicDataPanel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *GeographicDataPanel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_GeographicDataPanel.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int GeographicDataPanel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void GeographicDataPanel::panelClosed()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void GeographicDataPanel::refreshRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void GeographicDataPanel::rowSelected(int _t1, const QStringList & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
