@echo off
echo 快速构建 LiteAPPStar...

REM 设置Qt环境变量
set QT_DIR=D:\Qt\Qt5.14.2\5.14.2\msvc2017_64
set PATH=%QT_DIR%\bin;%PATH%

REM 检查Qt是否存在
if not exist "%QT_DIR%\bin\qmake.exe" (
    echo Qt5 not found at %QT_DIR%
    echo Please check Qt installation path in CMakeLists.txt
    pause
    exit /b 1
)

REM 创建构建目录
if not exist build mkdir build

REM 配置CMake项目
echo 配置CMake项目...
cmake -B build -G "Visual Studio 17 2022" -A x64
if %ERRORLEVEL% neq 0 (
    echo CMake配置失败!
    pause
    exit /b 1
)

REM 编译项目
echo 编译项目...
cmake --build build --config Debug
if %ERRORLEVEL% neq 0 (
    echo 编译失败!
    pause
    exit /b 1
)

echo 编译成功完成!
echo 可执行文件位置: build\bin\Debug\LiteAPPStar.exe

REM 检查可执行文件是否存在
if exist "build\bin\Debug\LiteAPPStar.exe" (
    echo 构建验证: 成功
    echo 现在可以在VSCode中使用F5进行调试了。
) else (
    echo 构建验证: 失败 - 找不到可执行文件
    pause
    exit /b 1
)

pause
