{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.1/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.1/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QuickWidgets/Qt5QuickWidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QuickWidgets/Qt5QuickWidgetsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Svg/Qt5SvgConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Svg/Qt5SvgConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CPack.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CPackComponent.cmake"}, {"isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Templates/CPackConfig.cmake.in"}, {"isExternal": true, "path": "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Templates/CPackConfig.cmake.in"}, {"path": "resources/resources.qrc"}], "kind": "cmakeFiles", "paths": {"build": "E:/code/LiteAPPStar/build", "source": "E:/code/LiteAPPStar"}, "version": {"major": 1, "minor": 0}}