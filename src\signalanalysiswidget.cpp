#include "signalanalysiswidget.h"
#include "ui/ui_signalanalysis.h"
#include <QApplication>
#include <QRandomGenerator>
#include <QDateTime>
#include <QtMath>
#include <cmath>

// 静态常量定义
const double SignalAnalysisWidget::DEFAULT_CENTER_FREQ = 100e6; // 100 MHz
const double SignalAnalysisWidget::DEFAULT_BANDWIDTH = 10e6;    // 10 MHz
const double SignalAnalysisWidget::MIN_AMPLITUDE = -120.0;      // -120 dBm
const double SignalAnalysisWidget::MAX_AMPLITUDE = 0.0;         // 0 dBm

SignalAnalysisWidget::SignalAnalysisWidget(QWidget *parent)
    : QWidget(parent),
      ui(new Ui::SignalAnalysisWidget),
      m_spectrumPlot(nullptr),
      m_waterfallPlot(nullptr),
      m_updateTimer(new QTimer(this)),
      m_centerFrequency(DEFAULT_CENTER_FREQ),
      m_bandwidth(DEFAULT_BANDWIDTH),
      m_analysisRunning(false),
      m_synchronizing(false),
      m_testMode(false),           // 默认关闭测试模式，使用动态数据生成
      m_currentTestType(0),        // 默认综合测试模式
      m_dynamicDataEnabled(false), // 默认关闭动态数据生成，等待用户启动
      m_timeCounter(0.0),
      m_smoothingFactor(0.15),   // 15%的平滑因子，保证连续性
      m_spectralResolution(0.0), // 将在initializeUI中计算
      m_fftSize(1024),           // 默认FFT大小
      m_lastBurstTime(0.0),      // 突发信号时间
      m_fadingPhase(0.0)         // 衰落相位
{
    ui->setupUi(this);
    initializeUI();
    setupTableProperties();
    connectSignals();

    // 设置初始频率范围
    updateFrequencyRangeDisplay();

    // 初始化动态数据生成参数
    initializeDynamicDataParameters();

    // 生成初始静态数据（不启动定时器）
    if (m_testMode)
    {
        generateTestSignalData(m_currentFrequencies, m_currentAmplitudes, m_currentTestType);
    }
    else
    {
        // 默认生成静态数据，等待用户启动动态数据生成
        generateSpectrumData(m_currentFrequencies, m_currentAmplitudes);
    }
    updateDataTable();

    // 注意：不在构造函数中启动定时器，等待用户点击动态数据按钮
}

SignalAnalysisWidget::~SignalAnalysisWidget()
{
    if (m_updateTimer)
    {
        m_updateTimer->stop();
    }
    delete ui;
}

void SignalAnalysisWidget::initializeUI()
{
    // 创建频谱图组件
    m_spectrumPlot = new SpectrumPlotSimple(this);

    // 创建瀑布图组件
    m_waterfallPlot = new WaterfallPlotSimple(this);

    // 将组件添加到UI布局中
    if (ui->spectrumDisplay)
    {
        QVBoxLayout *spectrumLayout = new QVBoxLayout(ui->spectrumDisplay);
        spectrumLayout->setContentsMargins(0, 0, 0, 0);
        spectrumLayout->addWidget(m_spectrumPlot);
        ui->spectrumDisplay->setLayout(spectrumLayout);
    }

    if (ui->waterfallDisplay)
    {
        QVBoxLayout *waterfallLayout = new QVBoxLayout(ui->waterfallDisplay);
        waterfallLayout->setContentsMargins(0, 0, 0, 0);
        waterfallLayout->addWidget(m_waterfallPlot);
        ui->waterfallDisplay->setLayout(waterfallLayout);
    }

    // 设置初始频率参数
    if (ui->centerFreqLineEdit && ui->centerFreqUnitCombo)
    {
        ui->centerFreqUnitCombo->setCurrentText("MHz");
        ui->centerFreqLineEdit->setText(QString::number(m_centerFrequency / 1e6, 'f', 3));
    }

    if (ui->spanLineEdit && ui->spanUnitCombo)
    {
        ui->spanUnitCombo->setCurrentText("MHz");
        ui->spanLineEdit->setText(QString::number(m_bandwidth / 1e6, 'f', 3));
    }

    // 配置定时器
    m_updateTimer->setSingleShot(false);
    m_updateTimer->setInterval(UPDATE_INTERVAL_MS);

    // 设置图表的初始频率范围和幅度范围
    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;

    m_spectrumPlot->setFrequencyRange(startFreq, endFreq);
    m_spectrumPlot->setAmplitudeRange(MIN_AMPLITUDE, MAX_AMPLITUDE);

    m_waterfallPlot->setFrequencyRange(startFreq, endFreq);
    m_waterfallPlot->setAmplitudeRange(MIN_AMPLITUDE, MAX_AMPLITUDE);
    m_waterfallPlot->setTimeWindow(10.0); // 10秒时间窗口

    // 设置QSplitter的初始分割比例，三个组件：频谱图、瀑布图、信号列表
    if (ui->mainSplitter)
    {
        // 延迟设置大小，确保组件已完全初始化
        QTimer::singleShot(200, this, [this]()
                           {
            if (ui->mainSplitter) {
                // 获取总高度并按比例分配
                int totalHeight = ui->mainSplitter->height();
                if (totalHeight > 0) {
                    QList<int> sizes;
                    sizes << totalHeight * 4 / 10;  // 频谱图 40%
                    sizes << totalHeight * 3 / 10;  // 瀑布图 30%
                    sizes << totalHeight * 3 / 10;  // 信号列表 30%
                    ui->mainSplitter->setSizes(sizes);
                }

                // 设置拉伸因子
                ui->mainSplitter->setStretchFactor(0, 4); // 频谱图拉伸因子4
                ui->mainSplitter->setStretchFactor(1, 3); // 瀑布图拉伸因子3
                ui->mainSplitter->setStretchFactor(2, 3); // 信号列表拉伸因子3
            } });

        // 设置分割器样式，使拖拉手柄更明显
        ui->mainSplitter->setStyleSheet(
            "QSplitter::handle {"
            "    background-color: rgb(100, 100, 120);"
            "    border: 1px solid rgb(80, 80, 100);"
            "    margin: 1px;"
            "}"
            "QSplitter::handle:hover {"
            "    background-color: rgb(120, 120, 140);"
            "}"
            "QSplitter::handle:pressed {"
            "    background-color: rgb(140, 140, 160);"
            "}");

        // 设置分割器手柄宽度
        ui->mainSplitter->setHandleWidth(6);

        // 允许组件完全收缩，实现完全覆盖效果
        ui->mainSplitter->setChildrenCollapsible(true);

        // 移除各组件的最小高度限制，允许完全拖拽
        if (ui->spectrumGroup)
        {
            ui->spectrumGroup->setMinimumHeight(0);
        }
        if (ui->waterfallGroup)
        {
            ui->waterfallGroup->setMinimumHeight(0);
        }
        if (ui->dataGroup)
        {
            ui->dataGroup->setMinimumHeight(0);
        }
    }
}

void SignalAnalysisWidget::setupTableProperties()
{
    if (!ui->dataTable)
    {
        return;
    }

    // 设置表格基本属性
    ui->dataTable->setRowCount(15);   // 增加行数以显示更多信号
    ui->dataTable->setColumnCount(8); // 8列：序号、中心频率、带宽、调制方式、速率、信噪比、频偏、通讯体制

    // 设置表头
    QStringList headers;
    headers << "序号" << "中心频率(KHz)" << "带宽(KHz)" << "调制方式" << "速率(kbps)" << "信噪比(dB)" << "频偏(Hz)" << "通讯体制";
    ui->dataTable->setHorizontalHeaderLabels(headers);

    // 优化的列宽分配策略 - 考虑整个表格宽度和字段数量
    // 使用比例分配，确保所有列都能合理显示

    // 设置最小列宽，确保可读性
    ui->dataTable->horizontalHeader()->setMinimumSectionSize(60); // 所有列最小60px

    // 使用Interactive模式，允许程序和用户都可以调整列宽
    ui->dataTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed);       // 序号：固定宽度
    ui->dataTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Interactive); // 中心频率：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Interactive); // 带宽：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Interactive); // 调制方式：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(4, QHeaderView::Interactive); // 速率：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(5, QHeaderView::Interactive); // 信噪比：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(6, QHeaderView::Interactive); // 频偏：可交互调整
    ui->dataTable->horizontalHeader()->setSectionResizeMode(7, QHeaderView::Interactive); // 通讯体制：可交互调整

    // 设置固定列宽（序号列）
    ui->dataTable->setColumnWidth(0, 50); // 序号列固定50px

    // 关闭最后一列单独拉伸，使用我们的智能分配
    ui->dataTable->horizontalHeader()->setStretchLastSection(false);

    // 设置行高
    ui->dataTable->verticalHeader()->setDefaultSectionSize(30);
    ui->dataTable->setAlternatingRowColors(true);                        // 交替行颜色
    ui->dataTable->setSelectionBehavior(QAbstractItemView::SelectRows);  // 选择整行
    ui->dataTable->setSelectionMode(QAbstractItemView::SingleSelection); // 单选模式
    ui->dataTable->verticalHeader()->setVisible(false);                  // 隐藏行号

    // 连接表格大小改变信号，实现动态列宽调整
    connect(ui->dataTable->horizontalHeader(), &QHeaderView::sectionResized,
            this, &SignalAnalysisWidget::onTableColumnResized);

    // 安装事件过滤器，监听表格大小变化
    ui->dataTable->installEventFilter(this);

    // 延迟调整表格列宽，确保表格已完全初始化
    QTimer::singleShot(100, this, &SignalAnalysisWidget::adjustTableColumnWidths);
}

void SignalAnalysisWidget::connectSignals()
{
    // 连接定时器信号
    connect(m_updateTimer, &QTimer::timeout, this, &SignalAnalysisWidget::updateData);

    // 连接频率控制信号
    if (ui->centerFreqLineEdit)
    {
        connect(ui->centerFreqLineEdit, &QLineEdit::textChanged,
                this, &SignalAnalysisWidget::onCenterFrequencyTextChanged);
    }

    if (ui->centerFreqUnitCombo)
    {
        connect(ui->centerFreqUnitCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &SignalAnalysisWidget::onCenterFrequencyUnitChanged);
    }

    if (ui->spanLineEdit)
    {
        connect(ui->spanLineEdit, &QLineEdit::textChanged,
                this, &SignalAnalysisWidget::onSpanTextChanged);
    }

    if (ui->spanUnitCombo)
    {
        connect(ui->spanUnitCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &SignalAnalysisWidget::onSpanUnitChanged);
    }

    // 连接预设按钮信号
    if (ui->presetISMBtn)
    {
        connect(ui->presetISMBtn, &QPushButton::clicked, this, &SignalAnalysisWidget::onPresetISM);
    }

    if (ui->presetWiFiBtn)
    {
        connect(ui->presetWiFiBtn, &QPushButton::clicked, this, &SignalAnalysisWidget::onPresetWiFi);
    }

    if (ui->presetFMBtn)
    {
        connect(ui->presetFMBtn, &QPushButton::clicked, this, &SignalAnalysisWidget::onPresetFM);
    }

    // 连接动态数据控制信号
    if (ui->dynamicDataBtn)
    {
        // 设置按钮为可切换状态
        ui->dynamicDataBtn->setCheckable(true);
        ui->dynamicDataBtn->setChecked(false);

        connect(ui->dynamicDataBtn, &QPushButton::toggled, this, &SignalAnalysisWidget::onDynamicDataToggled);
        // 设置初始按钮文本
        ui->dynamicDataBtn->setText("启动动态数据");
    }

    // 连接图表信号
    if (m_spectrumPlot)
    {
        connect(m_spectrumPlot, &SpectrumPlotSimple::frequencyRangeChanged,
                this, &SignalAnalysisWidget::onSpectrumFrequencyRangeChanged);
    }

    if (m_waterfallPlot)
    {
        connect(m_waterfallPlot, &WaterfallPlotSimple::frequencyRangeChanged,
                this, &SignalAnalysisWidget::onWaterfallFrequencyRangeChanged);
    }
}

void SignalAnalysisWidget::startAnalysis()
{
    if (!m_analysisRunning)
    {
        m_analysisRunning = true;
        m_updateTimer->start();
        emit analysisStateChanged(true);
    }
}

void SignalAnalysisWidget::stopAnalysis()
{
    if (m_analysisRunning)
    {
        m_analysisRunning = false;
        m_updateTimer->stop();
        emit analysisStateChanged(false);
    }
}

bool SignalAnalysisWidget::isAnalysisRunning() const
{
    return m_analysisRunning;
}

void SignalAnalysisWidget::setCenterFrequency(double frequency)
{
    if (qAbs(m_centerFrequency - frequency) > 1.0)
    { // 1Hz精度
        m_centerFrequency = frequency;
        updateFrequencyInputs();
        synchronizeFrequencyAxes();
        updateFrequencyRangeDisplay();
        emit frequencyRangeChanged(m_centerFrequency, m_bandwidth);
    }
}

void SignalAnalysisWidget::setBandwidth(double bandwidth)
{
    if (qAbs(m_bandwidth - bandwidth) > 1.0)
    { // 1Hz精度
        m_bandwidth = bandwidth;
        updateFrequencyInputs();
        synchronizeFrequencyAxes();
        updateFrequencyRangeDisplay();
        emit frequencyRangeChanged(m_centerFrequency, m_bandwidth);
    }
}

double SignalAnalysisWidget::centerFrequency() const
{
    return m_centerFrequency;
}

double SignalAnalysisWidget::bandwidth() const
{
    return m_bandwidth;
}

void SignalAnalysisWidget::clearAllData()
{
    if (m_spectrumPlot)
    {
        m_spectrumPlot->clearHoldData();
    }

    if (m_waterfallPlot)
    {
        m_waterfallPlot->clearHistory();
    }

    if (ui->dataTable)
    {
        ui->dataTable->clearContents();
    }

    m_currentFrequencies.clear();
    m_currentAmplitudes.clear();
}

void SignalAnalysisWidget::setDynamicDataEnabled(bool enabled)
{
    if (m_dynamicDataEnabled != enabled)
    {
        m_dynamicDataEnabled = enabled;

        if (enabled)
        {
            // 启用动态数据生成
            initializeDynamicDataParameters();

            // 启动定时器开始连续生成数据
            if (m_updateTimer && !m_updateTimer->isActive())
            {
                m_updateTimer->start();
                m_analysisRunning = true;
            }

            // 立即生成一次数据
            if (!m_testMode)
            {
                updateData();
            }
        }
        else
        {
            // 停止动态数据生成
            if (m_updateTimer && m_updateTimer->isActive())
            {
                m_updateTimer->stop();
                m_analysisRunning = false;
            }
        }

        // 发射状态变化信号
        emit analysisStateChanged(m_analysisRunning);
    }
}

bool SignalAnalysisWidget::isDynamicDataEnabled() const
{
    return m_dynamicDataEnabled;
}

void SignalAnalysisWidget::setUpdateInterval(int intervalMs)
{
    if (m_updateTimer && intervalMs > 0)
    {
        m_updateTimer->setInterval(intervalMs);
    }
}

int SignalAnalysisWidget::updateInterval() const
{
    return m_updateTimer ? m_updateTimer->interval() : 0;
}

void SignalAnalysisWidget::updateData()
{
    // 更新时间计数器
    m_timeCounter += m_updateTimer->interval() / 1000.0; // 转换为秒

    // 根据模式生成数据
    if (m_testMode)
    {
        // 每10秒切换一次测试类型，用于全面验证文字显示
        static int updateCount = 0;
        updateCount++;
        if (updateCount % 100 == 0)
        { // 10秒切换一次 (100 * 100ms = 10s)
            m_currentTestType = (m_currentTestType + 1) % 4;
        }
        generateTestSignalData(m_currentFrequencies, m_currentAmplitudes, m_currentTestType);
    }
    else if (m_dynamicDataEnabled)
    {
        // 生成FFT频谱数据
        generateFFTSpectrumData(m_currentFrequencies, m_currentAmplitudes);
    }
    else
    {
        // 生成静态频谱数据
        generateSpectrumData(m_currentFrequencies, m_currentAmplitudes);
    }

    // 确保频率数组一致性
    if (m_currentFrequencies.isEmpty() || m_currentAmplitudes.isEmpty())
    {
        return;
    }

    // 同时更新频谱图和瀑布图，确保数据同步
    if (m_spectrumPlot)
    {
        m_spectrumPlot->updateSpectrumData(m_currentFrequencies, m_currentAmplitudes);
    }

    if (m_waterfallPlot)
    {
        // 使用相同的频率和幅度数据，确保完全同步
        m_waterfallPlot->addSpectrumData(m_currentFrequencies, m_currentAmplitudes);
    }

    // 更新数据表格
    updateDataTable();

    emit dataUpdated();
}

void SignalAnalysisWidget::onCenterFrequencyTextChanged()
{
    if (!ui->centerFreqLineEdit || !ui->centerFreqUnitCombo)
        return;

    QString text = ui->centerFreqLineEdit->text();
    QString unit = ui->centerFreqUnitCombo->currentText();
    double frequency = parseFrequencyText(text, unit);

    if (frequency > 0)
    {
        setCenterFrequency(frequency);
    }
}

void SignalAnalysisWidget::onCenterFrequencyUnitChanged()
{
    onCenterFrequencyTextChanged();
}

void SignalAnalysisWidget::onSpanTextChanged()
{
    if (!ui->spanLineEdit || !ui->spanUnitCombo)
        return;

    QString text = ui->spanLineEdit->text();
    QString unit = ui->spanUnitCombo->currentText();
    double span = parseFrequencyText(text, unit);

    if (span > 0)
    {
        setBandwidth(span);
    }
}

void SignalAnalysisWidget::onSpanUnitChanged()
{
    onSpanTextChanged();
}

void SignalAnalysisWidget::onSpectrumFrequencyRangeChanged(double startFreq, double endFreq)
{
    if (m_synchronizing)
        return;

    m_synchronizing = true;

    // 根据频谱图的频率范围更新参数
    double newCenterFreq = (startFreq + endFreq) / 2.0;
    double newBandwidth = endFreq - startFreq;

    setCenterFrequency(newCenterFreq);
    setBandwidth(newBandwidth);

    // 同步瀑布图
    if (m_waterfallPlot)
    {
        m_waterfallPlot->setFrequencyRange(startFreq, endFreq);
    }

    m_synchronizing = false;
}

void SignalAnalysisWidget::onWaterfallFrequencyRangeChanged(double startFreq, double endFreq)
{
    if (m_synchronizing)
        return;

    m_synchronizing = true;

    // 根据瀑布图的频率范围更新参数
    double newCenterFreq = (startFreq + endFreq) / 2.0;
    double newBandwidth = endFreq - startFreq;

    setCenterFrequency(newCenterFreq);
    setBandwidth(newBandwidth);

    // 同步频谱图
    if (m_spectrumPlot)
    {
        m_spectrumPlot->setFrequencyRange(startFreq, endFreq);
    }

    m_synchronizing = false;
}

void SignalAnalysisWidget::onPresetISM()
{
    // ISM 2.4GHz频段: 2.4-2.5GHz
    setCenterFrequency(2.45e9); // 2.45 GHz
    setBandwidth(100e6);        // 100 MHz
}

void SignalAnalysisWidget::onPresetWiFi()
{
    // WiFi 5GHz频段: 5.15-5.85GHz
    setCenterFrequency(5.5e9); // 5.5 GHz
    setBandwidth(700e6);       // 700 MHz
}

void SignalAnalysisWidget::onPresetFM()
{
    // FM广播频段: 88-108MHz
    setCenterFrequency(98e6); // 98 MHz
    setBandwidth(20e6);       // 20 MHz
}

void SignalAnalysisWidget::onDynamicDataToggled(bool enabled)
{
    // 更新按钮文本
    if (ui->dynamicDataBtn)
    {
        if (enabled)
        {
            ui->dynamicDataBtn->setText("停止动态数据");
        }
        else
        {
            ui->dynamicDataBtn->setText("启动动态数据");
        }
    }

    // 调用实际的设置函数
    setDynamicDataEnabled(enabled);
}

void SignalAnalysisWidget::onTableColumnResized(int logicalIndex, int oldSize, int newSize)
{
    Q_UNUSED(logicalIndex)
    Q_UNUSED(oldSize)
    Q_UNUSED(newSize)

    // 当用户手动调整列宽时，可以在这里添加自定义逻辑
    // 例如：保存用户的列宽偏好设置
    // 目前暂时不需要特殊处理，因为我们使用了智能的调整模式
}

void SignalAnalysisWidget::adjustTableColumnWidths()
{
    if (!ui->dataTable)
        return;

    // 获取表格可用宽度（减去滚动条和边距）
    int availableWidth = ui->dataTable->viewport()->width();
    if (availableWidth <= 0)
        return;

    // 定义各列的权重比例（总和为100）
    // 根据内容重要性和显示需求分配
    QVector<int> columnWeights = {
        0,  // 序号：固定宽度，不参与比例分配
        18, // 中心频率：18%
        15, // 带宽：15%
        12, // 调制方式：12%
        15, // 速率：15%
        12, // 信噪比：12%
        13, // 频偏：13%
        15  // 通讯体制：15%
    };

    // 计算可分配宽度（减去固定列宽）
    int fixedWidth = 50;                                     // 序号列固定宽度
    int distributedWidth = availableWidth - fixedWidth - 20; // 减去一些边距

    if (distributedWidth > 0)
    {
        // 根据权重分配列宽
        for (int i = 1; i < ui->dataTable->columnCount() && i < columnWeights.size(); ++i)
        {
            int columnWidth = (distributedWidth * columnWeights[i]) / 100;
            // 确保最小宽度
            columnWidth = qMax(columnWidth, 60);
            ui->dataTable->setColumnWidth(i, columnWidth);
        }
    }
}

bool SignalAnalysisWidget::eventFilter(QObject *watched, QEvent *event)
{
    // 监听表格的大小变化事件
    if (watched == ui->dataTable && event->type() == QEvent::Resize)
    {
        // 延迟调整列宽，避免频繁调整
        QTimer::singleShot(50, this, &SignalAnalysisWidget::adjustTableColumnWidths);
    }

    return QWidget::eventFilter(watched, event);
}

void SignalAnalysisWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 重新启用自动调整，确保布局比例正确
    QTimer::singleShot(100, this, &SignalAnalysisWidget::resetLayoutProportions);
}

void SignalAnalysisWidget::resetLayoutProportions()
{
    if (!ui->mainSplitter)
        return;

    // 获取当前窗口高度
    int totalHeight = ui->mainSplitter->height();
    if (totalHeight <= 0)
        return;

    // 计算各组件的理想高度 - 调整比例让瀑布图更大
    int spectrumHeight = totalHeight * 4 / 10;  // 40% 的空间给频谱图
    int waterfallHeight = totalHeight * 4 / 10; // 40% 的空间给瀑布图
    int dataHeight = totalHeight * 2 / 10;      // 20% 的空间给信号列表

    // 移除最小高度限制，允许完全拖拽覆盖
    // spectrumHeight = qMax(spectrumHeight, 250);
    // waterfallHeight = qMax(waterfallHeight, 250);
    // dataHeight = qMax(dataHeight, 150);

    // 设置新的大小
    QList<int> sizes;
    sizes << spectrumHeight << waterfallHeight << dataHeight;
    ui->mainSplitter->setSizes(sizes);

    // 重新设置拉伸因子
    ui->mainSplitter->setStretchFactor(0, 4); // 频谱图
    ui->mainSplitter->setStretchFactor(1, 4); // 瀑布图
    ui->mainSplitter->setStretchFactor(2, 2); // 信号列表
}

void SignalAnalysisWidget::generateSpectrumData(QVector<double> &frequencies, QVector<double> &amplitudes)
{
    frequencies.clear();
    amplitudes.clear();

    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;
    double freqStep = m_bandwidth / (SPECTRUM_POINTS - 1);

    // 生成频率数组
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        double freq = startFreq + i * freqStep;
        frequencies.append(freq);
    }

    // 生成模拟的频谱数据
    amplitudes.resize(SPECTRUM_POINTS);

    // 获取当前时间用于动态信号生成
    static double timeCounter = 0.0;
    timeCounter += 0.1; // 100ms间隔

    // 1. 基础高斯白噪声底噪 (-110到-120dBm)
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        // 使用Box-Muller变换生成高斯噪声
        static bool hasSpare = false;
        static double spare;

        if (hasSpare)
        {
            amplitudes[i] = -115.0 + spare * 3.0;
            hasSpare = false;
        }
        else
        {
            double u = QRandomGenerator::global()->generateDouble();
            double v = QRandomGenerator::global()->generateDouble();
            double mag = 3.0 * sqrt(-2.0 * log(u));
            spare = mag * cos(2.0 * M_PI * v);
            amplitudes[i] = -115.0 + mag * sin(2.0 * M_PI * v);
            hasSpare = true;
        }
    }

    // 2. 主信号源 (-20到-40dBm) - 移动载波信号
    double mainCarrierFreq = m_centerFrequency + 0.3 * m_bandwidth * sin(timeCounter * 0.1);
    int mainCarrierIndex = (mainCarrierFreq - startFreq) / freqStep;
    if (mainCarrierIndex >= 0 && mainCarrierIndex < SPECTRUM_POINTS)
    {
        double mainAmplitude = -30.0 + 5.0 * sin(timeCounter * 0.05);
        // 窄带载波信号
        for (int i = qMax(0, mainCarrierIndex - 3); i <= qMin(SPECTRUM_POINTS - 1, mainCarrierIndex + 3); ++i)
        {
            double distance = qAbs(i - mainCarrierIndex);
            double carrierShape = exp(-distance * distance / 2.0);
            amplitudes[i] = qMax(amplitudes[i], mainAmplitude * carrierShape);
        }
    }

    // 3. 次要信号源 (-50到-70dBm) - FM调制信号
    double fmCenterFreq = m_centerFrequency - 0.2 * m_bandwidth;
    int fmCenterIndex = (fmCenterFreq - startFreq) / freqStep;
    if (fmCenterIndex >= 10 && fmCenterIndex < SPECTRUM_POINTS - 10)
    {
        double fmAmplitude = -60.0 + 8.0 * cos(timeCounter * 0.08);
        double fmDeviation = 8; // 频偏范围

        // FM信号的频谱展宽
        for (int i = fmCenterIndex - fmDeviation; i <= fmCenterIndex + fmDeviation; ++i)
        {
            if (i >= 0 && i < SPECTRUM_POINTS)
            {
                double distance = qAbs(i - fmCenterIndex);
                double fmShape = exp(-distance * distance / (2.0 * fmDeviation * fmDeviation / 4.0));
                amplitudes[i] = qMax(amplitudes[i], fmAmplitude * fmShape);
            }
        }
    }

    // 4. 微弱信号 (-80到-100dBm) - 扫频信号
    static double sweepPhase = 0.0;
    sweepPhase += 0.02;
    double sweepFreq = m_centerFrequency + 0.4 * m_bandwidth * sin(sweepPhase);
    int sweepIndex = (sweepFreq - startFreq) / freqStep;
    if (sweepIndex >= 0 && sweepIndex < SPECTRUM_POINTS)
    {
        double sweepAmplitude = -90.0 + 5.0 * sin(timeCounter * 0.12);
        for (int i = qMax(0, sweepIndex - 2); i <= qMin(SPECTRUM_POINTS - 1, sweepIndex + 2); ++i)
        {
            double distance = qAbs(i - sweepIndex);
            double sweepShape = exp(-distance * distance);
            amplitudes[i] = qMax(amplitudes[i], sweepAmplitude * sweepShape);
        }
    }

    // 5. 间歇性脉冲信号
    static double pulseTimer = 0.0;
    pulseTimer += 0.1;
    if (fmod(pulseTimer, 3.0) < 0.5)
    { // 每3秒出现0.5秒
        double pulseFreq = m_centerFrequency + 0.1 * m_bandwidth;
        int pulseIndex = (pulseFreq - startFreq) / freqStep;
        if (pulseIndex >= 5 && pulseIndex < SPECTRUM_POINTS - 5)
        {
            double pulseAmplitude = -45.0;
            // 宽带脉冲信号
            for (int i = pulseIndex - 5; i <= pulseIndex + 5; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - pulseIndex);
                    double pulseShape = exp(-distance / 3.0);
                    amplitudes[i] = qMax(amplitudes[i], pulseAmplitude * pulseShape);
                }
            }
        }
    }

    // 6. 添加轻微的随机变化以模拟真实环境
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] += (QRandomGenerator::global()->generateDouble() - 0.5) * 1.0;
        amplitudes[i] = qBound(MIN_AMPLITUDE, amplitudes[i], MAX_AMPLITUDE);
    }
}

void SignalAnalysisWidget::generateTestSignalData(QVector<double> &frequencies, QVector<double> &amplitudes, int testMode)
{
    frequencies.clear();
    amplitudes.clear();

    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;
    double freqStep = m_bandwidth / (SPECTRUM_POINTS - 1);

    // 生成频率数组
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        double freq = startFreq + i * freqStep;
        frequencies.append(freq);
    }

    // 初始化幅度数组
    amplitudes.resize(SPECTRUM_POINTS);

    // 获取时间计数器用于动态效果
    static double timeCounter = 0.0;
    timeCounter += 0.1;

    switch (testMode)
    {
    case 0: // 综合测试模式 - 验证所有文字显示
        generateComprehensiveTestSignals(amplitudes, startFreq, freqStep, timeCounter);
        break;
    case 1: // 多频点正弦波 - 验证频率轴标签
        generateMultiToneSignals(amplitudes, startFreq, freqStep, timeCounter);
        break;
    case 2: // 扫频信号 - 验证动态显示
        generateSweepSignals(amplitudes, startFreq, freqStep, timeCounter);
        break;
    case 3: // 极值测试 - 验证幅度轴标签
        generateExtremeValueSignals(amplitudes, startFreq, freqStep, timeCounter);
        break;
    default:
        generateComprehensiveTestSignals(amplitudes, startFreq, freqStep, timeCounter);
        break;
    }

    // 确保所有幅度值在有效范围内
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] = qBound(MIN_AMPLITUDE, amplitudes[i], MAX_AMPLITUDE);
    }
}

void SignalAnalysisWidget::generateComprehensiveTestSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter)
{
    // 1. 基础噪声底噪 (-110到-115dBm)
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] = -115.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 5.0;
    }

    // 2. 多个固定频率的正弦波信号 - 用于验证频率轴标签显示
    QVector<double> testFrequencies = {
        m_centerFrequency - 0.4 * m_bandwidth, // 左侧信号 (-20dBm)
        m_centerFrequency - 0.2 * m_bandwidth, // 左中信号 (-35dBm)
        m_centerFrequency,                     // 中心信号 (-15dBm)
        m_centerFrequency + 0.2 * m_bandwidth, // 右中信号 (-40dBm)
        m_centerFrequency + 0.4 * m_bandwidth  // 右侧信号 (-25dBm)
    };

    QVector<double> testAmplitudes = {-20.0, -35.0, -15.0, -40.0, -25.0};

    for (int j = 0; j < testFrequencies.size(); ++j)
    {
        double freq = testFrequencies[j];
        double amplitude = testAmplitudes[j] + 3.0 * sin(timeCounter * 0.1 * (j + 1)); // 动态变化
        int centerIndex = (freq - startFreq) / freqStep;

        if (centerIndex >= 5 && centerIndex < SPECTRUM_POINTS - 5)
        {
            // 生成窄带信号
            for (int i = centerIndex - 3; i <= centerIndex + 3; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);
                    double shape = exp(-distance * distance / 2.0);
                    amplitudes[i] = qMax(amplitudes[i], amplitude * shape);
                }
            }
        }
    }

    // 3. 扫频信号 - 用于验证动态显示
    double sweepFreq = m_centerFrequency + 0.3 * m_bandwidth * sin(timeCounter * 0.05);
    int sweepIndex = (sweepFreq - startFreq) / freqStep;
    if (sweepIndex >= 0 && sweepIndex < SPECTRUM_POINTS)
    {
        double sweepAmplitude = -50.0 + 8.0 * cos(timeCounter * 0.08);
        for (int i = qMax(0, sweepIndex - 4); i <= qMin(SPECTRUM_POINTS - 1, sweepIndex + 4); ++i)
        {
            double distance = qAbs(i - sweepIndex);
            double shape = exp(-distance / 2.0);
            amplitudes[i] = qMax(amplitudes[i], sweepAmplitude * shape);
        }
    }

    // 4. 极值信号 - 用于验证幅度轴标签显示
    // 高幅度信号 (接近0dBm)
    int highIndex = SPECTRUM_POINTS * 0.15;
    if (highIndex >= 0 && highIndex < SPECTRUM_POINTS)
    {
        double highAmplitude = -5.0 + 2.0 * sin(timeCounter * 0.12);
        amplitudes[highIndex] = qMax(amplitudes[highIndex], highAmplitude);
    }

    // 低幅度信号 (接近-120dBm)
    int lowIndex = SPECTRUM_POINTS * 0.85;
    if (lowIndex >= 0 && lowIndex < SPECTRUM_POINTS)
    {
        double lowAmplitude = -118.0 + 1.0 * cos(timeCounter * 0.15);
        amplitudes[lowIndex] = qMax(amplitudes[lowIndex], lowAmplitude);
    }

    // 5. 宽带信号 - 用于验证颜色标尺显示
    int wideStart = SPECTRUM_POINTS * 0.6;
    int wideEnd = SPECTRUM_POINTS * 0.8;
    double wideAmplitude = -60.0 + 10.0 * sin(timeCounter * 0.06);
    for (int i = wideStart; i < wideEnd && i < SPECTRUM_POINTS; ++i)
    {
        double envelope = sin(M_PI * (i - wideStart) / (wideEnd - wideStart));
        amplitudes[i] = qMax(amplitudes[i], wideAmplitude * envelope);
    }
}

void SignalAnalysisWidget::generateMultiToneSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter)
{
    // 基础噪声底噪
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] = -110.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 8.0;
    }

    // 生成多个标准频率的正弦波信号，用于验证频率轴标签
    // 这些频率会产生清晰的频率标签，便于验证文字显示效果
    QVector<double> standardFreqs;
    QVector<double> standardAmps;

    // 根据带宽生成合适的测试频率
    if (m_bandwidth >= 100e6) // >= 100MHz
    {
        // 大带宽：每10MHz一个信号
        for (double offset = -40e6; offset <= 40e6; offset += 10e6)
        {
            standardFreqs.append(m_centerFrequency + offset);
            standardAmps.append(-30.0 + 10.0 * sin(timeCounter * 0.1 + offset / 10e6));
        }
    }
    else if (m_bandwidth >= 10e6) // >= 10MHz
    {
        // 中等带宽：每1MHz一个信号
        for (double offset = -4e6; offset <= 4e6; offset += 1e6)
        {
            standardFreqs.append(m_centerFrequency + offset);
            standardAmps.append(-25.0 + 8.0 * cos(timeCounter * 0.08 + offset / 1e6));
        }
    }
    else // < 10MHz
    {
        // 小带宽：每100kHz一个信号
        for (double offset = -400e3; offset <= 400e3; offset += 100e3)
        {
            standardFreqs.append(m_centerFrequency + offset);
            standardAmps.append(-35.0 + 12.0 * sin(timeCounter * 0.12 + offset / 100e3));
        }
    }

    // 生成信号
    for (int j = 0; j < standardFreqs.size(); ++j)
    {
        double freq = standardFreqs[j];
        double amplitude = standardAmps[j];
        int centerIndex = (freq - startFreq) / freqStep;

        if (centerIndex >= 2 && centerIndex < SPECTRUM_POINTS - 2)
        {
            // 生成尖锐的单频信号
            for (int i = centerIndex - 2; i <= centerIndex + 2; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);
                    double shape = (distance == 0) ? 1.0 : exp(-distance * distance * 2.0);
                    amplitudes[i] = qMax(amplitudes[i], amplitude * shape);
                }
            }
        }
    }

    // 添加一些动态变化的信号，用于验证实时更新
    double movingFreq1 = m_centerFrequency + 0.2 * m_bandwidth * sin(timeCounter * 0.03);
    double movingFreq2 = m_centerFrequency - 0.3 * m_bandwidth * cos(timeCounter * 0.05);

    QVector<double> movingFreqs = {movingFreq1, movingFreq2};
    QVector<double> movingAmps = {-20.0 + 5.0 * sin(timeCounter * 0.1), -40.0 + 8.0 * cos(timeCounter * 0.07)};

    for (int j = 0; j < movingFreqs.size(); ++j)
    {
        double freq = movingFreqs[j];
        double amplitude = movingAmps[j];
        int centerIndex = (freq - startFreq) / freqStep;

        if (centerIndex >= 3 && centerIndex < SPECTRUM_POINTS - 3)
        {
            for (int i = centerIndex - 3; i <= centerIndex + 3; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);
                    double shape = exp(-distance * distance / 3.0);
                    amplitudes[i] = qMax(amplitudes[i], amplitude * shape);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateSweepSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter)
{
    // 基础噪声底噪
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] = -105.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 10.0;
    }

    // 主扫频信号 - 慢速扫描整个频带
    double sweepPeriod = 50.0;                                        // 50秒完成一次扫描
    double sweepPhase = fmod(timeCounter, sweepPeriod) / sweepPeriod; // 0到1
    double sweepFreq = startFreq + sweepPhase * m_bandwidth;
    int sweepIndex = (sweepFreq - startFreq) / freqStep;

    if (sweepIndex >= 5 && sweepIndex < SPECTRUM_POINTS - 5)
    {
        double sweepAmplitude = -25.0 + 5.0 * sin(timeCounter * 0.1);
        // 生成扫频信号的频谱特征
        for (int i = sweepIndex - 5; i <= sweepIndex + 5; ++i)
        {
            if (i >= 0 && i < SPECTRUM_POINTS)
            {
                double distance = qAbs(i - sweepIndex);
                double shape = exp(-distance / 3.0);
                amplitudes[i] = qMax(amplitudes[i], sweepAmplitude * shape);
            }
        }
    }

    // 快速扫频信号 - 验证动态响应
    double fastSweepFreq = m_centerFrequency + 0.4 * m_bandwidth * sin(timeCounter * 0.2);
    int fastSweepIndex = (fastSweepFreq - startFreq) / freqStep;

    if (fastSweepIndex >= 3 && fastSweepIndex < SPECTRUM_POINTS - 3)
    {
        double fastAmplitude = -45.0 + 8.0 * cos(timeCounter * 0.15);
        for (int i = fastSweepIndex - 3; i <= fastSweepIndex + 3; ++i)
        {
            if (i >= 0 && i < SPECTRUM_POINTS)
            {
                double distance = qAbs(i - fastSweepIndex);
                double shape = exp(-distance * distance / 2.0);
                amplitudes[i] = qMax(amplitudes[i], fastAmplitude * shape);
            }
        }
    }

    // 三角波扫频 - 来回扫描
    double trianglePhase = fmod(timeCounter * 0.1, 2.0);
    if (trianglePhase > 1.0)
        trianglePhase = 2.0 - trianglePhase; // 三角波
    double triangleFreq = startFreq + trianglePhase * m_bandwidth;
    int triangleIndex = (triangleFreq - startFreq) / freqStep;

    if (triangleIndex >= 4 && triangleIndex < SPECTRUM_POINTS - 4)
    {
        double triangleAmplitude = -35.0 + 6.0 * sin(timeCounter * 0.08);
        for (int i = triangleIndex - 4; i <= triangleIndex + 4; ++i)
        {
            if (i >= 0 && i < SPECTRUM_POINTS)
            {
                double distance = qAbs(i - triangleIndex);
                double shape = exp(-distance / 2.5);
                amplitudes[i] = qMax(amplitudes[i], triangleAmplitude * shape);
            }
        }
    }

    // 多个同时扫频的信号
    for (int j = 0; j < 3; ++j)
    {
        double multiSweepFreq = m_centerFrequency + 0.2 * m_bandwidth * sin(timeCounter * (0.05 + j * 0.02));
        int multiIndex = (multiSweepFreq - startFreq) / freqStep;

        if (multiIndex >= 2 && multiIndex < SPECTRUM_POINTS - 2)
        {
            double multiAmplitude = -55.0 - j * 10.0 + 4.0 * cos(timeCounter * (0.12 + j * 0.03));
            for (int i = multiIndex - 2; i <= multiIndex + 2; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - multiIndex);
                    double shape = exp(-distance * distance);
                    amplitudes[i] = qMax(amplitudes[i], multiAmplitude * shape);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateExtremeValueSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter)
{
    // 基础噪声底噪 - 使用接近最小值的噪声
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        amplitudes[i] = -118.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 4.0;
    }

    // 极高幅度信号 (接近0dBm) - 验证高端幅度标签
    QVector<int> highIndices = {
        static_cast<int>(SPECTRUM_POINTS * 0.1),
        static_cast<int>(SPECTRUM_POINTS * 0.3),
        static_cast<int>(SPECTRUM_POINTS * 0.7),
        static_cast<int>(SPECTRUM_POINTS * 0.9)};

    for (int j = 0; j < highIndices.size(); ++j)
    {
        int index = highIndices[j];
        if (index >= 1 && index < SPECTRUM_POINTS - 1)
        {
            double highAmplitude = -2.0 + 1.5 * sin(timeCounter * (0.1 + j * 0.02));
            amplitudes[index] = qMax(amplitudes[index], highAmplitude);
            // 添加一些旁瓣
            if (index > 0)
                amplitudes[index - 1] = qMax(amplitudes[index - 1], highAmplitude - 20.0);
            if (index < SPECTRUM_POINTS - 1)
                amplitudes[index + 1] = qMax(amplitudes[index + 1], highAmplitude - 20.0);
        }
    }

    // 极低幅度信号 (接近-120dBm) - 验证低端幅度标签
    QVector<int> lowIndices = {
        static_cast<int>(SPECTRUM_POINTS * 0.2),
        static_cast<int>(SPECTRUM_POINTS * 0.5),
        static_cast<int>(SPECTRUM_POINTS * 0.8)};

    for (int j = 0; j < lowIndices.size(); ++j)
    {
        int index = lowIndices[j];
        if (index >= 0 && index < SPECTRUM_POINTS)
        {
            double lowAmplitude = -119.0 + 0.5 * cos(timeCounter * (0.08 + j * 0.03));
            amplitudes[index] = qMax(amplitudes[index], lowAmplitude);
        }
    }

    // 中等幅度的阶梯信号 - 验证中间范围的标签
    int stepStart = SPECTRUM_POINTS * 0.4;
    int stepEnd = SPECTRUM_POINTS * 0.6;
    int stepSize = (stepEnd - stepStart) / 6; // 6个阶梯

    for (int step = 0; step < 6; ++step)
    {
        double stepAmplitude = -100.0 + step * 15.0 + 3.0 * sin(timeCounter * 0.1);
        int stepStartIdx = stepStart + step * stepSize;
        int stepEndIdx = stepStart + (step + 1) * stepSize;

        for (int i = stepStartIdx; i < stepEndIdx && i < SPECTRUM_POINTS; ++i)
        {
            if (i >= 0)
            {
                amplitudes[i] = qMax(amplitudes[i], stepAmplitude);
            }
        }
    }

    // 动态变化的极值信号
    double dynamicHighFreq = m_centerFrequency + 0.1 * m_bandwidth * sin(timeCounter * 0.05);
    int dynamicHighIndex = (dynamicHighFreq - startFreq) / freqStep;
    if (dynamicHighIndex >= 0 && dynamicHighIndex < SPECTRUM_POINTS)
    {
        double dynamicAmplitude = -1.0 + 0.8 * sin(timeCounter * 0.12);
        amplitudes[dynamicHighIndex] = qMax(amplitudes[dynamicHighIndex], dynamicAmplitude);
    }

    double dynamicLowFreq = m_centerFrequency - 0.1 * m_bandwidth * cos(timeCounter * 0.04);
    int dynamicLowIndex = (dynamicLowFreq - startFreq) / freqStep;
    if (dynamicLowIndex >= 0 && dynamicLowIndex < SPECTRUM_POINTS)
    {
        double dynamicLowAmplitude = -119.5 + 0.3 * cos(timeCounter * 0.15);
        amplitudes[dynamicLowIndex] = qMax(amplitudes[dynamicLowIndex], dynamicLowAmplitude);
    }
}

void SignalAnalysisWidget::updateFrequencyRangeDisplay()
{
    if (ui->frequencyRangeLabel)
    {
        double startFreq = m_centerFrequency - m_bandwidth / 2.0;
        double endFreq = m_centerFrequency + m_bandwidth / 2.0;

        QString rangeText;
        if (m_bandwidth >= 1e9)
        {
            rangeText = QString("%1 - %2 GHz")
                            .arg(startFreq / 1e9, 0, 'f', 3)
                            .arg(endFreq / 1e9, 0, 'f', 3);
        }
        else if (m_bandwidth >= 1e6)
        {
            rangeText = QString("%1 - %2 MHz")
                            .arg(startFreq / 1e6, 0, 'f', 1)
                            .arg(endFreq / 1e6, 0, 'f', 1);
        }
        else if (m_bandwidth >= 1e3)
        {
            rangeText = QString("%1 - %2 kHz")
                            .arg(startFreq / 1e3, 0, 'f', 1)
                            .arg(endFreq / 1e3, 0, 'f', 1);
        }
        else
        {
            rangeText = QString("%1 - %2 Hz")
                            .arg(startFreq, 0, 'f', 0)
                            .arg(endFreq, 0, 'f', 0);
        }

        ui->frequencyRangeLabel->setText(rangeText);
    }
}

void SignalAnalysisWidget::updateDataTable()
{
    if (!ui->dataTable)
    {
        return;
    }

    // 生成或更新假信号数据
    generateFakeSignalData();

    // 更新信号列表表格
    updateSignalListTable();
}

void SignalAnalysisWidget::generateFakeSignalData()
{
    // 如果信号列表为空或需要更新，生成新的信号数据
    if (m_detectedSignals.isEmpty() || (m_timeCounter - m_detectedSignals.first().lastUpdateTime) > 5.0)
    {
        m_detectedSignals.clear();

        // 调制方式列表
        QStringList modulations = {"FSK", "PSK", "QAM", "OFDM", "AM", "FM", "BPSK", "QPSK", "16QAM", "64QAM", "MSK", "GMSK"};

        // 通讯体制列表
        QStringList protocols = {"GSM", "CDMA", "LTE", "WiFi", "蓝牙", "ZigBee", "LoRa", "NB-IoT", "卫星通信", "微波通信", "短波", "超短波"};

        // 生成8-12个随机信号
        int signalCount = 8 + QRandomGenerator::global()->bounded(5);

        for (int i = 0; i < signalCount; ++i)
        {
            SignalInfo signal;
            signal.id = i + 1;

            // 中心频率：在当前频率范围内随机分布
            double freqRange = m_bandwidth * 0.8; // 使用80%的带宽避免边缘
            signal.centerFreq = m_centerFrequency + (QRandomGenerator::global()->generateDouble() - 0.5) * freqRange;

            // 带宽：根据调制方式和应用场景
            double baseBandwidth = 1000 + QRandomGenerator::global()->bounded(50000); // 1-50kHz基础带宽
            if (i < 3)                                                                // 前几个信号可能是宽带信号
            {
                baseBandwidth *= (2 + QRandomGenerator::global()->bounded(8)); // 2-10倍
            }
            signal.bandwidth = baseBandwidth;

            // 调制方式
            signal.modulation = modulations[QRandomGenerator::global()->bounded(modulations.size())];

            // 数据速率：根据调制方式调整
            if (signal.modulation.contains("QAM") || signal.modulation == "OFDM")
            {
                signal.dataRate = 100 + QRandomGenerator::global()->bounded(10000); // 0.1-10Mbps
            }
            else if (signal.modulation.contains("PSK"))
            {
                signal.dataRate = 10 + QRandomGenerator::global()->bounded(1000); // 10bps-1Mbps
            }
            else
            {
                signal.dataRate = 1 + QRandomGenerator::global()->bounded(500); // 1bps-500kbps
            }

            // 信噪比：15-45dB
            signal.snr = 15.0 + QRandomGenerator::global()->bounded(30);

            // 频偏：±1000Hz
            signal.freqOffset = (QRandomGenerator::global()->generateDouble() - 0.5) * 2000;

            // 通讯体制
            signal.protocol = protocols[QRandomGenerator::global()->bounded(protocols.size())];

            // 激活状态：85%概率激活
            signal.active = QRandomGenerator::global()->bounded(100) < 85;

            signal.lastUpdateTime = m_timeCounter;

            m_detectedSignals.append(signal);
        }
    }
    else
    {
        // 更新现有信号的动态参数
        for (auto &signal : m_detectedSignals)
        {
            // 随机更新信噪比 (±2dB变化)
            signal.snr += (QRandomGenerator::global()->generateDouble() - 0.5) * 4.0;
            signal.snr = qBound(5.0, signal.snr, 50.0);

            // 随机更新频偏 (±50Hz变化)
            signal.freqOffset += (QRandomGenerator::global()->generateDouble() - 0.5) * 100.0;
            signal.freqOffset = qBound(-2000.0, signal.freqOffset, 2000.0);

            // 随机更新数据速率 (±10%变化)
            double rateChange = 1.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 0.2;
            signal.dataRate *= rateChange;
            signal.dataRate = qMax(1.0, signal.dataRate);

            // 偶尔改变激活状态 (2%概率)
            if (QRandomGenerator::global()->bounded(100) < 2)
            {
                signal.active = !signal.active;
            }

            signal.lastUpdateTime = m_timeCounter;
        }
    }
}

void SignalAnalysisWidget::updateSignalListTable()
{
    if (!ui->dataTable)
    {
        return;
    }

    // 确保表格有足够的行
    if (ui->dataTable->rowCount() < m_detectedSignals.size())
    {
        ui->dataTable->setRowCount(m_detectedSignals.size());
    }

    // 更新表格内容
    for (int i = 0; i < m_detectedSignals.size() && i < ui->dataTable->rowCount(); ++i)
    {
        const SignalInfo &signal = m_detectedSignals[i];

        // 序号
        QTableWidgetItem *idItem = new QTableWidgetItem(QString::number(signal.id));
        idItem->setFlags(idItem->flags() & ~Qt::ItemIsEditable);
        idItem->setTextAlignment(Qt::AlignCenter);
        ui->dataTable->setItem(i, 0, idItem);

        // 中心频率 (转换为KHz)
        QTableWidgetItem *freqItem = new QTableWidgetItem(QString::number(signal.centerFreq / 1000.0, 'f', 1));
        freqItem->setFlags(freqItem->flags() & ~Qt::ItemIsEditable);
        freqItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        ui->dataTable->setItem(i, 1, freqItem);

        // 带宽 (转换为KHz)
        QTableWidgetItem *bwItem = new QTableWidgetItem(QString::number(signal.bandwidth / 1000.0, 'f', 1));
        bwItem->setFlags(bwItem->flags() & ~Qt::ItemIsEditable);
        bwItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        ui->dataTable->setItem(i, 2, bwItem);

        // 调制方式
        QTableWidgetItem *modItem = new QTableWidgetItem(signal.modulation);
        modItem->setFlags(modItem->flags() & ~Qt::ItemIsEditable);
        modItem->setTextAlignment(Qt::AlignCenter);
        ui->dataTable->setItem(i, 3, modItem);

        // 速率 (转换为kbps)
        QTableWidgetItem *rateItem = new QTableWidgetItem(QString::number(signal.dataRate / 1000.0, 'f', 1));
        rateItem->setFlags(rateItem->flags() & ~Qt::ItemIsEditable);
        rateItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        ui->dataTable->setItem(i, 4, rateItem);

        // 信噪比
        QTableWidgetItem *snrItem = new QTableWidgetItem(QString::number(signal.snr, 'f', 1));
        snrItem->setFlags(snrItem->flags() & ~Qt::ItemIsEditable);
        snrItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        // 根据信噪比设置颜色
        if (signal.snr < 15.0)
        {
            snrItem->setForeground(QBrush(QColor(255, 100, 100))); // 红色
        }
        else if (signal.snr < 25.0)
        {
            snrItem->setForeground(QBrush(QColor(255, 165, 0))); // 橙色
        }
        else
        {
            snrItem->setForeground(QBrush(QColor(100, 200, 100))); // 绿色
        }
        ui->dataTable->setItem(i, 5, snrItem);

        // 频偏
        QTableWidgetItem *offsetItem = new QTableWidgetItem(QString::number(signal.freqOffset, 'f', 0));
        offsetItem->setFlags(offsetItem->flags() & ~Qt::ItemIsEditable);
        offsetItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        ui->dataTable->setItem(i, 6, offsetItem);

        // 通讯体制
        QTableWidgetItem *protocolItem = new QTableWidgetItem(signal.protocol);
        protocolItem->setFlags(protocolItem->flags() & ~Qt::ItemIsEditable);
        protocolItem->setTextAlignment(Qt::AlignCenter);
        // 根据激活状态设置颜色
        if (signal.active)
        {
            protocolItem->setForeground(QBrush(QColor(100, 200, 100))); // 绿色
        }
        else
        {
            protocolItem->setForeground(QBrush(QColor(150, 150, 150))); // 灰色
        }
        ui->dataTable->setItem(i, 7, protocolItem);
    }

    // 清空未使用的行
    for (int i = m_detectedSignals.size(); i < ui->dataTable->rowCount(); ++i)
    {
        for (int j = 0; j < ui->dataTable->columnCount(); ++j)
        {
            ui->dataTable->setItem(i, j, new QTableWidgetItem(""));
        }
    }
}

void SignalAnalysisWidget::synchronizeFrequencyAxes()
{
    if (m_synchronizing)
        return;

    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;

    // 确保两个图表使用完全相同的频率范围
    if (m_spectrumPlot)
    {
        m_spectrumPlot->setFrequencyRange(startFreq, endFreq);
    }

    if (m_waterfallPlot)
    {
        m_waterfallPlot->setFrequencyRange(startFreq, endFreq);
    }

    // 验证频率范围设置是否成功
    if (m_spectrumPlot && m_waterfallPlot)
    {
        auto spectrumRange = m_spectrumPlot->frequencyRange();
        auto waterfallRange = m_waterfallPlot->frequencyRange();

        // 如果范围不一致，强制重新设置
        if (qAbs(spectrumRange.first - waterfallRange.first) > 1.0 ||
            qAbs(spectrumRange.second - waterfallRange.second) > 1.0)
        {
            m_spectrumPlot->setFrequencyRange(startFreq, endFreq);
            m_waterfallPlot->setFrequencyRange(startFreq, endFreq);
        }
    }
}

QMap<QString, double> SignalAnalysisWidget::calculateAnalysisParameters(const QVector<double> &amplitudes)
{
    QMap<QString, double> params;

    if (amplitudes.isEmpty())
    {
        return params;
    }

    // 转换为线性功率
    QVector<double> linearPower;
    for (double amp : amplitudes)
    {
        linearPower.append(pow(10.0, amp / 10.0)); // dBm转换为mW
    }

    // 峰值功率
    double maxAmp = *std::max_element(amplitudes.begin(), amplitudes.end());
    params["峰值功率"] = maxAmp;

    // 平均功率
    double avgLinear = std::accumulate(linearPower.begin(), linearPower.end(), 0.0) / linearPower.size();
    params["平均功率"] = 10.0 * log10(avgLinear);

    // RMS功率
    double rmsLinear = 0.0;
    for (double p : linearPower)
    {
        rmsLinear += p * p;
    }
    rmsLinear = sqrt(rmsLinear / linearPower.size());
    params["RMS功率"] = 10.0 * log10(rmsLinear);

    // 峰值因子
    params["峰值因子"] = params["峰值功率"] - params["平均功率"];

    // 占用带宽（简化计算）
    params["占用带宽"] = m_bandwidth / 1e6; // 转换为MHz

    // 信噪比（简化计算）
    double minAmp = *std::min_element(amplitudes.begin(), amplitudes.end());
    params["信噪比"] = maxAmp - minAmp;

    // SFDR（无杂散动态范围）
    params["SFDR"] = 60.0 + QRandomGenerator::global()->bounded(20.0);

    // THD（总谐波失真）
    params["THD"] = -50.0 + QRandomGenerator::global()->bounded(20.0);

    // 相位噪声
    params["相位噪声"] = -120.0 + QRandomGenerator::global()->bounded(20.0);

    // 频率稳定度
    params["频率稳定度"] = QRandomGenerator::global()->bounded(10.0);

    return params;
}

void SignalAnalysisWidget::initializeDynamicDataParameters()
{
    // 初始化信号相位数组（用于多个载波信号）
    m_signalPhases.clear();
    m_signalAmplitudes.clear();
    m_carrierFrequencies.clear();
    m_carrierDriftRates.clear();

    // 创建7个载波信号的初始参数（增加信号数量以提高连续性）
    for (int i = 0; i < 7; ++i)
    {
        m_signalPhases.append(QRandomGenerator::global()->generateDouble() * 2.0 * M_PI);
        m_signalAmplitudes.append(-35.0 + QRandomGenerator::global()->bounded(45.0)); // -35dBm到10dBm

        // 载波频率分布在整个带宽内
        double freqOffset = (i - 3.0) / 6.0; // -0.5 到 0.5
        m_carrierFrequencies.append(m_centerFrequency + freqOffset * m_bandwidth * 0.8);

        // 载波频率漂移速率（Hz/s）
        m_carrierDriftRates.append((QRandomGenerator::global()->generateDouble() - 0.5) * 1000.0);
    }

    // 初始化噪声缓冲区
    m_noiseBuffer.clear();
    m_noiseBuffer.resize(SPECTRUM_POINTS);
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        m_noiseBuffer[i] = -115.0 + (QRandomGenerator::global()->generateDouble() - 0.5) * 8.0;
    }

    // 初始化上一帧数据
    m_previousAmplitudes.clear();
    m_previousAmplitudes.resize(SPECTRUM_POINTS);
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        m_previousAmplitudes[i] = -115.0;
    }

    // 初始化FFT参数
    m_spectralResolution = m_bandwidth / m_fftSize;

    // 初始化基频数组（模拟多个信号源）
    m_fundamentalFreqs.clear();
    m_fundamentalAmps.clear();
    m_harmonicPhases.clear();

    // 创建5个基频信号
    for (int i = 0; i < 5; ++i)
    {
        // 基频分布在带宽内
        double freqOffset = (i - 2.0) / 4.0; // -0.5 到 0.5
        m_fundamentalFreqs.append(m_centerFrequency + freqOffset * m_bandwidth * 0.6);

        // 基频幅度 (-20dBm 到 -60dBm)
        m_fundamentalAmps.append(-20.0 - QRandomGenerator::global()->bounded(40.0));

        // 谐波相位
        m_harmonicPhases.append(QRandomGenerator::global()->generateDouble() * 2.0 * M_PI);

        // 信号激活状态（随机初始状态）
        m_signalActive.append(QRandomGenerator::global()->bounded(100) > 30); // 70%概率激活
    }

    // 初始化突发信号参数
    m_burstSignalTimes.clear();
    m_burstSignalFreqs.clear();
    m_burstSignalAmps.clear();

    // 创建3个潜在的突发信号
    for (int i = 0; i < 3; ++i)
    {
        m_burstSignalTimes.append(0.0);
        m_burstSignalFreqs.append(m_centerFrequency + (QRandomGenerator::global()->generateDouble() - 0.5) * m_bandwidth);
        m_burstSignalAmps.append(-15.0 - QRandomGenerator::global()->bounded(30.0));
    }

    // 重置时间计数器和突发信号时间
    m_timeCounter = 0.0;
    m_lastBurstTime = 0.0;
    m_fadingPhase = 0.0;
}

void SignalAnalysisWidget::generateDynamicRFData(QVector<double> &frequencies, QVector<double> &amplitudes)
{
    frequencies.clear();
    amplitudes.clear();

    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;
    double freqStep = m_bandwidth / (SPECTRUM_POINTS - 1);

    // 生成频率数组
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        double freq = startFreq + i * freqStep;
        frequencies.append(freq);
    }

    // 初始化幅度数组
    amplitudes.resize(SPECTRUM_POINTS);

    // 1. 更新载波频率（实现频率漂移）
    updateCarrierFrequencies();

    // 2. 生成连续的噪声底噪
    generateNoiseFloor(amplitudes);

    // 3. 生成连续的载波信号
    generateContinuousSignals(amplitudes, startFreq, freqStep);

    // 4. 生成干扰信号
    generateInterferenceSignals(amplitudes, startFreq, freqStep);

    // 5. 应用时变特性
    applyTimeVariation(amplitudes);

    // 6. 应用连续性平滑处理
    applyContinuousSmoothing(amplitudes);

    // 7. 确保幅度在合理范围内
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        amplitudes[i] = qBound(MIN_AMPLITUDE, amplitudes[i], MAX_AMPLITUDE);
    }

    // 8. 保存当前帧数据用于下一帧平滑
    m_previousAmplitudes = amplitudes;
}

void SignalAnalysisWidget::generateNoiseFloor(QVector<double> &amplitudes)
{
    // 生成连续的高斯白噪声底噪 (-110到-120dBm)
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        // 使用Box-Muller变换生成高斯噪声
        static bool hasSpare = false;
        static double spare;

        double noiseValue;
        if (hasSpare)
        {
            noiseValue = -115.0 + spare * 2.5; // 减小噪声幅度以提高连续性
            hasSpare = false;
        }
        else
        {
            double u = QRandomGenerator::global()->generateDouble();
            double v = QRandomGenerator::global()->generateDouble();
            double mag = 2.5 * sqrt(-2.0 * log(u));
            spare = mag * cos(2.0 * M_PI * v);
            noiseValue = -115.0 + mag * sin(2.0 * M_PI * v);
            hasSpare = true;
        }

        // 添加缓慢变化的噪声基线
        double baselineVariation = 1.5 * sin(m_timeCounter * 0.008 + i * 0.0008);

        // 添加频率相关的噪声变化
        double freqVariation = 0.5 * sin(m_timeCounter * 0.012 + i * 0.002);

        // 如果有历史噪声数据，进行平滑
        if (i < m_noiseBuffer.size())
        {
            // 噪声也需要连续性
            double smoothingFactor = 0.05; // 噪声的平滑因子更小
            noiseValue = smoothingFactor * noiseValue + (1.0 - smoothingFactor) * m_noiseBuffer[i];
            m_noiseBuffer[i] = noiseValue;
        }

        amplitudes[i] = noiseValue + baselineVariation + freqVariation;
    }
}

void SignalAnalysisWidget::generateCarrierSignals(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成多个载波信号，模拟通信信号
    QVector<double> carrierFreqs = {
        m_centerFrequency - 0.35 * m_bandwidth, // 左侧载波
        m_centerFrequency - 0.15 * m_bandwidth, // 左中载波
        m_centerFrequency,                      // 中心载波
        m_centerFrequency + 0.15 * m_bandwidth, // 右中载波
        m_centerFrequency + 0.35 * m_bandwidth  // 右侧载波
    };

    for (int carrierIdx = 0; carrierIdx < carrierFreqs.size() && carrierIdx < m_signalAmplitudes.size(); ++carrierIdx)
    {
        double carrierFreq = carrierFreqs[carrierIdx];
        double baseAmplitude = m_signalAmplitudes[carrierIdx];

        // 计算载波在频谱中的位置
        int centerIndex = (carrierFreq - startFreq) / freqStep;

        if (centerIndex >= 5 && centerIndex < SPECTRUM_POINTS - 5)
        {
            // 生成载波信号的频谱特征（带有一定带宽）
            for (int i = centerIndex - 5; i <= centerIndex + 5; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);
                    double shape = exp(-distance / 2.0); // 高斯形状

                    // 添加调制边带
                    double modulation = 1.0 + 0.3 * sin(m_timeCounter * 0.1 + carrierIdx);
                    double signalLevel = baseAmplitude * shape * modulation;

                    amplitudes[i] = qMax(amplitudes[i], signalLevel);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateInterferenceSignals(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 1. 扫频干扰信号
    double sweepPeriod = 30.0; // 30秒完成一次扫描
    double sweepPhase = fmod(m_timeCounter, sweepPeriod) / sweepPeriod;
    double sweepFreq = startFreq + sweepPhase * m_bandwidth;
    int sweepIndex = (sweepFreq - startFreq) / freqStep;

    if (sweepIndex >= 3 && sweepIndex < SPECTRUM_POINTS - 3)
    {
        double sweepAmplitude = -45.0 + 8.0 * sin(m_timeCounter * 0.05);
        for (int i = sweepIndex - 3; i <= sweepIndex + 3; ++i)
        {
            if (i >= 0 && i < SPECTRUM_POINTS)
            {
                double distance = qAbs(i - sweepIndex);
                double shape = exp(-distance / 1.5);
                amplitudes[i] = qMax(amplitudes[i], sweepAmplitude * shape);
            }
        }
    }

    // 2. 脉冲干扰信号
    double pulseFreq = m_centerFrequency + 0.25 * m_bandwidth * sin(m_timeCounter * 0.03);
    int pulseIndex = (pulseFreq - startFreq) / freqStep;

    if (pulseIndex >= 0 && pulseIndex < SPECTRUM_POINTS)
    {
        // 脉冲信号：周期性出现
        double pulsePeriod = 5.0; // 5秒周期
        double pulsePhase = fmod(m_timeCounter, pulsePeriod) / pulsePeriod;

        if (pulsePhase < 0.2) // 20%的时间内出现脉冲
        {
            double pulseAmplitude = -20.0 + 5.0 * sin(m_timeCounter * 0.2);
            amplitudes[pulseIndex] = qMax(amplitudes[pulseIndex], pulseAmplitude);
        }
    }

    // 3. 宽带干扰
    double wideband_center = m_centerFrequency - 0.2 * m_bandwidth;
    int wideband_start = (wideband_center - 0.1 * m_bandwidth - startFreq) / freqStep;
    int wideband_end = (wideband_center + 0.1 * m_bandwidth - startFreq) / freqStep;

    wideband_start = qMax(0, wideband_start);
    wideband_end = qMin(SPECTRUM_POINTS - 1, wideband_end);

    double widebandLevel = -60.0 + 10.0 * sin(m_timeCounter * 0.02);
    for (int i = wideband_start; i <= wideband_end; ++i)
    {
        double variation = 3.0 * (QRandomGenerator::global()->generateDouble() - 0.5);
        amplitudes[i] = qMax(amplitudes[i], widebandLevel + variation);
    }
}

void SignalAnalysisWidget::applyTimeVariation(QVector<double> &amplitudes)
{
    // 应用全局的时变特性
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        // 1. 大气衰减变化
        double atmosphericFading = 1.0 + 0.1 * sin(m_timeCounter * 0.008 + i * 0.01);

        // 2. 多径衰落
        double multipathFading = 1.0 + 0.05 * sin(m_timeCounter * 0.15 + i * 0.02) *
                                           cos(m_timeCounter * 0.12 + i * 0.03);

        // 3. 设备增益变化
        double gainVariation = 1.0 + 0.02 * sin(m_timeCounter * 0.005);

        // 应用所有变化
        amplitudes[i] *= atmosphericFading * multipathFading * gainVariation;

        // 添加随机抖动
        amplitudes[i] += (QRandomGenerator::global()->generateDouble() - 0.5) * 0.5;
    }
}

void SignalAnalysisWidget::updateCarrierFrequencies()
{
    // 更新载波频率，实现缓慢的频率漂移
    double deltaTime = m_updateTimer->interval() / 1000.0; // 转换为秒

    for (int i = 0; i < m_carrierFrequencies.size() && i < m_carrierDriftRates.size(); ++i)
    {
        // 应用频率漂移
        m_carrierFrequencies[i] += m_carrierDriftRates[i] * deltaTime;

        // 限制载波频率在合理范围内
        double minFreq = m_centerFrequency - m_bandwidth * 0.4;
        double maxFreq = m_centerFrequency + m_bandwidth * 0.4;

        if (m_carrierFrequencies[i] < minFreq || m_carrierFrequencies[i] > maxFreq)
        {
            // 反转漂移方向
            m_carrierDriftRates[i] = -m_carrierDriftRates[i];
            m_carrierFrequencies[i] = qBound(minFreq, m_carrierFrequencies[i], maxFreq);
        }
    }
}

void SignalAnalysisWidget::generateContinuousSignals(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成连续变化的载波信号
    for (int carrierIdx = 0; carrierIdx < m_carrierFrequencies.size() && carrierIdx < m_signalAmplitudes.size(); ++carrierIdx)
    {
        double carrierFreq = m_carrierFrequencies[carrierIdx];
        double baseAmplitude = m_signalAmplitudes[carrierIdx];

        // 添加缓慢的幅度变化
        double amplitudeModulation = 1.0 + 0.2 * sin(m_timeCounter * 0.05 + carrierIdx * 0.7);
        baseAmplitude *= amplitudeModulation;

        // 计算载波在频谱中的位置
        int centerIndex = (carrierFreq - startFreq) / freqStep;

        if (centerIndex >= 8 && centerIndex < SPECTRUM_POINTS - 8)
        {
            // 生成载波信号的频谱特征（更宽的带宽以模拟调制）
            for (int i = centerIndex - 8; i <= centerIndex + 8; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);
                    double shape = exp(-distance / 3.0); // 更宽的高斯形状

                    // 添加连续的调制边带
                    double modulation = 1.0 + 0.4 * sin(m_timeCounter * 0.08 + carrierIdx + distance * 0.1);
                    double signalLevel = baseAmplitude * shape * modulation;

                    amplitudes[i] = qMax(amplitudes[i], signalLevel);
                }
            }
        }

        // 更新信号相位以保持连续性
        m_signalPhases[carrierIdx] += 0.1 * (carrierIdx + 1);
        if (m_signalPhases[carrierIdx] > 2.0 * M_PI)
        {
            m_signalPhases[carrierIdx] -= 2.0 * M_PI;
        }
    }
}

void SignalAnalysisWidget::applyContinuousSmoothing(QVector<double> &amplitudes)
{
    // 如果有上一帧数据，进行平滑处理
    if (m_previousAmplitudes.size() == amplitudes.size())
    {
        for (int i = 0; i < amplitudes.size(); ++i)
        {
            // 使用指数移动平均进行平滑
            amplitudes[i] = m_smoothingFactor * amplitudes[i] +
                            (1.0 - m_smoothingFactor) * m_previousAmplitudes[i];
        }
    }

    // 添加空间平滑（相邻点之间的平滑）
    QVector<double> smoothed = amplitudes;
    for (int i = 1; i < amplitudes.size() - 1; ++i)
    {
        smoothed[i] = 0.25 * amplitudes[i - 1] + 0.5 * amplitudes[i] + 0.25 * amplitudes[i + 1];
    }
    amplitudes = smoothed;
}

void SignalAnalysisWidget::generateFFTSpectrumData(QVector<double> &frequencies, QVector<double> &amplitudes)
{
    frequencies.clear();
    amplitudes.clear();

    double startFreq = m_centerFrequency - m_bandwidth / 2.0;
    double endFreq = m_centerFrequency + m_bandwidth / 2.0;
    double freqStep = m_bandwidth / (SPECTRUM_POINTS - 1);

    // 生成频率数组
    for (int i = 0; i < SPECTRUM_POINTS; ++i)
    {
        double freq = startFreq + i * freqStep;
        frequencies.append(freq);
    }

    // 初始化幅度数组
    amplitudes.resize(SPECTRUM_POINTS);

    // 1. 生成动态噪声底噪
    generateDynamicNoiseFloor(amplitudes);

    // 2. 生成动态基频信号（包含信号开关）
    generateDynamicFundamentalTones(amplitudes, startFreq, freqStep);

    // 3. 生成突发信号
    generateBurstSignals(amplitudes, startFreq, freqStep);

    // 4. 生成间歇性干扰
    generateIntermittentInterference(amplitudes, startFreq, freqStep);

    // 5. 生成谐波（动态）
    generateDynamicHarmonics(amplitudes, startFreq, freqStep);

    // 6. 应用多径衰落效应
    applyMultipathFading(amplitudes);

    // 7. 生成频谱泄漏效应
    generateFFTSpectralLeakage(amplitudes, startFreq, freqStep);

    // 8. 应用窗函数效应
    generateFFTWindowingEffects(amplitudes);

    // 9. 应用FFT特有的特征
    applyFFTCharacteristics(amplitudes);

    // 10. 应用连续性平滑（减少平滑以增加波动）
    applyReducedSmoothing(amplitudes);

    // 11. 确保幅度在合理范围内
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        amplitudes[i] = qBound(MIN_AMPLITUDE, amplitudes[i], MAX_AMPLITUDE);
    }

    // 12. 更新时间计数器
    m_timeCounter += 0.1; // 增加时间步长

    // 13. 保存当前帧数据用于下一帧平滑
    m_previousAmplitudes = amplitudes;
}

void SignalAnalysisWidget::generateFFTFundamentalTones(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成基频信号，模拟FFT分析的主要频率成分
    for (int toneIdx = 0; toneIdx < m_fundamentalFreqs.size() && toneIdx < m_fundamentalAmps.size(); ++toneIdx)
    {
        double fundamentalFreq = m_fundamentalFreqs[toneIdx];
        double baseAmplitude = m_fundamentalAmps[toneIdx];

        // 添加缓慢的幅度调制（模拟信号强度变化）
        double amplitudeModulation = 1.0 + 0.15 * sin(m_timeCounter * 0.03 + toneIdx * 0.5);
        baseAmplitude *= amplitudeModulation;

        // 计算基频在频谱中的位置
        int centerIndex = (fundamentalFreq - startFreq) / freqStep;

        if (centerIndex >= 3 && centerIndex < SPECTRUM_POINTS - 3)
        {
            // FFT特有的sinc函数形状（由于矩形窗函数）
            for (int i = centerIndex - 3; i <= centerIndex + 3; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);

                    // sinc函数形状：sin(πx)/(πx)
                    double x = distance * 0.5;
                    double sincValue;
                    if (x < 0.001)
                    {
                        sincValue = 1.0;
                    }
                    else
                    {
                        sincValue = sin(M_PI * x) / (M_PI * x);
                    }

                    // 应用sinc形状和相位
                    double phase = m_timeCounter * 0.1 + toneIdx;
                    double signalLevel = baseAmplitude * qAbs(sincValue) * (1.0 + 0.1 * cos(phase));

                    amplitudes[i] = qMax(amplitudes[i], signalLevel);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateFFTHarmonics(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成谐波成分
    for (int toneIdx = 0; toneIdx < m_fundamentalFreqs.size(); ++toneIdx)
    {
        double fundamentalFreq = m_fundamentalFreqs[toneIdx];
        double fundamentalAmp = m_fundamentalAmps[toneIdx];

        // 生成2次和3次谐波
        for (int harmonic = 2; harmonic <= 3; ++harmonic)
        {
            double harmonicFreq = fundamentalFreq * harmonic;

            // 检查谐波是否在当前频率范围内
            if (harmonicFreq >= startFreq && harmonicFreq <= startFreq + m_bandwidth)
            {
                int harmonicIndex = (harmonicFreq - startFreq) / freqStep;

                if (harmonicIndex >= 0 && harmonicIndex < SPECTRUM_POINTS)
                {
                    // 谐波幅度递减
                    double harmonicAmp = fundamentalAmp - 20.0 * log10(harmonic) - 10.0;

                    // 添加相位调制
                    double phase = m_harmonicPhases[toneIdx] + m_timeCounter * 0.05 * harmonic;
                    double modulation = 1.0 + 0.2 * sin(phase);

                    amplitudes[harmonicIndex] = qMax(amplitudes[harmonicIndex], harmonicAmp * modulation);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateFFTSpectralLeakage(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 模拟频谱泄漏效应（当信号频率不在FFT bin中心时）
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        if (amplitudes[i] > -100.0) // 只对有信号的地方添加泄漏
        {
            // 在相邻频率bin中添加泄漏
            for (int offset = -2; offset <= 2; ++offset)
            {
                if (offset != 0)
                {
                    int leakageIndex = i + offset;
                    if (leakageIndex >= 0 && leakageIndex < amplitudes.size())
                    {
                        double leakageLevel = amplitudes[i] - 20.0 - 5.0 * qAbs(offset);
                        amplitudes[leakageIndex] = qMax(amplitudes[leakageIndex], leakageLevel);
                    }
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateFFTWindowingEffects(QVector<double> &amplitudes)
{
    // 模拟窗函数效应（汉宁窗的影响）
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        // 汉宁窗在频域的影响：主瓣宽度增加，旁瓣抑制
        if (amplitudes[i] > -90.0)
        {
            // 轻微的主瓣展宽效应
            double windowEffect = 1.0 + 0.05 * sin(m_timeCounter * 0.02 + i * 0.1);
            amplitudes[i] *= windowEffect;
        }
    }
}

void SignalAnalysisWidget::generateDynamicNoiseFloor(QVector<double> &amplitudes)
{
    // 生成动态变化的噪声底噪
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        // 基础噪声电平 (-110dBm 到 -120dBm)
        double baseNoise = -115.0;

        // 添加时变噪声调制
        double timeModulation = 3.0 * sin(m_timeCounter * 0.05 + i * 0.01);

        // 添加随机噪声波动
        double randomFluctuation = 2.0 * (QRandomGenerator::global()->generateDouble() - 0.5);

        // 添加频率相关的噪声变化
        double freqNoise = 1.5 * sin(i * 0.02 + m_timeCounter * 0.03);

        // 偶尔的噪声突发
        if (QRandomGenerator::global()->bounded(1000) < 5) // 0.5%概率
        {
            randomFluctuation += 8.0 * QRandomGenerator::global()->generateDouble();
        }

        amplitudes[i] = baseNoise + timeModulation + randomFluctuation + freqNoise;
    }
}

void SignalAnalysisWidget::generateDynamicFundamentalTones(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 动态更新信号激活状态
    for (int toneIdx = 0; toneIdx < m_signalActive.size(); ++toneIdx)
    {
        // 随机信号开关 (每帧有2%概率改变状态)
        if (QRandomGenerator::global()->bounded(100) < 2)
        {
            m_signalActive[toneIdx] = !m_signalActive[toneIdx];
        }
    }

    // 生成激活的基频信号
    for (int toneIdx = 0; toneIdx < m_fundamentalFreqs.size() && toneIdx < m_fundamentalAmps.size(); ++toneIdx)
    {
        if (!m_signalActive[toneIdx])
            continue; // 跳过未激活的信号

        double fundamentalFreq = m_fundamentalFreqs[toneIdx];
        double baseAmplitude = m_fundamentalAmps[toneIdx];

        // 添加强烈的幅度调制和波动
        double fastModulation = 8.0 * sin(m_timeCounter * 0.2 + toneIdx * 1.2);
        double slowModulation = 5.0 * sin(m_timeCounter * 0.02 + toneIdx * 0.8);
        double randomVariation = 3.0 * (QRandomGenerator::global()->generateDouble() - 0.5);

        // 偶尔的信号衰落
        double fadingEffect = 1.0;
        if (QRandomGenerator::global()->bounded(100) < 10) // 10%概率
        {
            fadingEffect = 0.3 + 0.7 * QRandomGenerator::global()->generateDouble();
        }

        baseAmplitude = (baseAmplitude + fastModulation + slowModulation + randomVariation) * fadingEffect;

        // 计算基频在频谱中的位置
        int centerIndex = (fundamentalFreq - startFreq) / freqStep;

        if (centerIndex >= 3 && centerIndex < SPECTRUM_POINTS - 3)
        {
            // 更宽的sinc函数形状，增加变化
            for (int i = centerIndex - 3; i <= centerIndex + 3; ++i)
            {
                if (i >= 0 && i < SPECTRUM_POINTS)
                {
                    double distance = qAbs(i - centerIndex);

                    // sinc函数形状：sin(πx)/(πx)
                    double x = distance * 0.4; // 更宽的主瓣
                    double sincValue;
                    if (x < 0.001)
                    {
                        sincValue = 1.0;
                    }
                    else
                    {
                        sincValue = sin(M_PI * x) / (M_PI * x);
                    }

                    // 应用sinc形状和动态相位
                    double phase = m_timeCounter * 0.3 + toneIdx + distance * 0.5;
                    double signalLevel = baseAmplitude * qAbs(sincValue) * (1.0 + 0.2 * cos(phase));

                    amplitudes[i] = qMax(amplitudes[i], signalLevel);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateBurstSignals(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 检查是否应该生成新的突发信号
    if (m_timeCounter - m_lastBurstTime > 2.0 + QRandomGenerator::global()->bounded(8.0)) // 2-10秒间隔
    {
        // 随机选择一个突发信号
        int burstIdx = QRandomGenerator::global()->bounded(m_burstSignalFreqs.size());

        // 更新突发信号参数
        m_burstSignalFreqs[burstIdx] = m_centerFrequency + (QRandomGenerator::global()->generateDouble() - 0.5) * m_bandwidth * 0.8;
        m_burstSignalAmps[burstIdx] = -10.0 - QRandomGenerator::global()->bounded(25.0);
        m_burstSignalTimes[burstIdx] = m_timeCounter;

        m_lastBurstTime = m_timeCounter;
    }

    // 生成活跃的突发信号
    for (int burstIdx = 0; burstIdx < m_burstSignalTimes.size(); ++burstIdx)
    {
        double timeSinceBurst = m_timeCounter - m_burstSignalTimes[burstIdx];

        // 突发信号持续时间：0.5-3秒
        if (timeSinceBurst >= 0 && timeSinceBurst <= 3.0)
        {
            double burstFreq = m_burstSignalFreqs[burstIdx];
            double burstAmp = m_burstSignalAmps[burstIdx];

            // 突发信号包络（快速上升，指数衰减）
            double envelope;
            if (timeSinceBurst < 0.1)
            {
                envelope = timeSinceBurst / 0.1; // 快速上升
            }
            else
            {
                envelope = exp(-(timeSinceBurst - 0.1) / 1.0); // 指数衰减
            }

            // 添加突发信号的频率抖动
            double freqJitter = 0.01 * m_bandwidth * sin(timeSinceBurst * 10.0);
            burstFreq += freqJitter;

            int burstIndex = (burstFreq - startFreq) / freqStep;

            if (burstIndex >= 2 && burstIndex < SPECTRUM_POINTS - 2)
            {
                // 突发信号有更宽的频谱
                for (int i = burstIndex - 2; i <= burstIndex + 2; ++i)
                {
                    if (i >= 0 && i < SPECTRUM_POINTS)
                    {
                        double distance = qAbs(i - burstIndex);
                        double spreadFactor = exp(-distance * distance / 2.0);
                        double signalLevel = burstAmp * envelope * spreadFactor;

                        amplitudes[i] = qMax(amplitudes[i], signalLevel);
                    }
                }
            }
        }
    }
}

void SignalAnalysisWidget::applyFFTCharacteristics(QVector<double> &amplitudes)
{
    // 应用FFT特有的特征
    for (int i = 0; i < amplitudes.size(); ++i)
    {
        // 1. 增强的量化噪声（ADC量化效应）
        double quantizationNoise = 1.0 * (QRandomGenerator::global()->generateDouble() - 0.5);
        amplitudes[i] += quantizationNoise;

        // 2. 频率分辨率限制效应
        if (i > 0 && i < amplitudes.size() - 1)
        {
            // 轻微的频率bin平均效应
            double binAveraging = 0.05 * (amplitudes[i - 1] + amplitudes[i + 1]);
            amplitudes[i] = 0.9 * amplitudes[i] + 0.1 * binAveraging;
        }

        // 3. 动态范围限制
        if (amplitudes[i] > -10.0)
        {
            // 模拟ADC饱和效应
            amplitudes[i] = -10.0 + (amplitudes[i] + 10.0) * 0.1;
        }
    }
}

void SignalAnalysisWidget::generateIntermittentInterference(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成间歇性干扰信号
    for (int i = 0; i < amplitudes.size(); i += 50) // 每50个频率点检查一次
    {
        if (QRandomGenerator::global()->bounded(100) < 8) // 8%概率出现干扰
        {
            double interferenceFreq = startFreq + i * freqStep;
            double interferenceAmp = -25.0 - QRandomGenerator::global()->bounded(20.0);

            // 干扰信号的时变特性
            double timeModulation = sin(m_timeCounter * 0.8 + i * 0.05);
            interferenceAmp += 5.0 * timeModulation;

            // 干扰信号有一定的带宽
            int interferenceIndex = i;
            for (int j = interferenceIndex - 5; j <= interferenceIndex + 5; ++j)
            {
                if (j >= 0 && j < amplitudes.size())
                {
                    double distance = qAbs(j - interferenceIndex);
                    double spreadFactor = exp(-distance * distance / 8.0);
                    double signalLevel = interferenceAmp * spreadFactor;

                    amplitudes[j] = qMax(amplitudes[j], signalLevel);
                }
            }
        }
    }
}

void SignalAnalysisWidget::generateDynamicHarmonics(QVector<double> &amplitudes, double startFreq, double freqStep)
{
    // 生成动态谐波成分
    for (int toneIdx = 0; toneIdx < m_fundamentalFreqs.size(); ++toneIdx)
    {
        if (!m_signalActive[toneIdx])
            continue;

        double fundamentalFreq = m_fundamentalFreqs[toneIdx];
        double fundamentalAmp = m_fundamentalAmps[toneIdx];

        // 生成2次和3次谐波，但有时变特性
        for (int harmonic = 2; harmonic <= 3; ++harmonic)
        {
            double harmonicFreq = fundamentalFreq * harmonic;

            // 检查谐波是否在当前频率范围内
            if (harmonicFreq >= startFreq && harmonicFreq <= startFreq + m_bandwidth)
            {
                int harmonicIndex = (harmonicFreq - startFreq) / freqStep;

                if (harmonicIndex >= 0 && harmonicIndex < SPECTRUM_POINTS)
                {
                    // 谐波幅度递减，但有随机变化
                    double harmonicAmp = fundamentalAmp - 15.0 * log10(harmonic) - 8.0;

                    // 添加强烈的时变调制
                    double phase = m_harmonicPhases[toneIdx] + m_timeCounter * 0.1 * harmonic;
                    double modulation = 1.0 + 0.4 * sin(phase) + 0.2 * sin(phase * 2.3);

                    // 谐波有时会消失
                    if (QRandomGenerator::global()->bounded(100) < 15) // 15%概率消失
                    {
                        modulation *= 0.1;
                    }

                    amplitudes[harmonicIndex] = qMax(amplitudes[harmonicIndex], harmonicAmp * modulation);
                }
            }
        }
    }
}

void SignalAnalysisWidget::applyMultipathFading(QVector<double> &amplitudes)
{
    // 应用多径衰落效应
    m_fadingPhase += 0.02; // 衰落相位演化

    for (int i = 0; i < amplitudes.size(); ++i)
    {
        if (amplitudes[i] > -100.0) // 只对有信号的地方应用衰落
        {
            // 瑞利衰落模拟
            double fadingDepth = 3.0 * sin(m_fadingPhase + i * 0.01) + 2.0 * cos(m_fadingPhase * 1.7 + i * 0.008);

            // 偶尔的深度衰落
            if (QRandomGenerator::global()->bounded(1000) < 3) // 0.3%概率
            {
                fadingDepth -= 15.0 * QRandomGenerator::global()->generateDouble();
            }

            amplitudes[i] += fadingDepth;
        }
    }
}

void SignalAnalysisWidget::applyReducedSmoothing(QVector<double> &amplitudes)
{
    // 减少平滑以增加波动
    if (!m_previousAmplitudes.isEmpty() && m_previousAmplitudes.size() == amplitudes.size())
    {
        QVector<double> smoothed = amplitudes;

        // 只应用很少的平滑（10%而不是15%）
        double reducedSmoothingFactor = 0.1;

        for (int i = 0; i < amplitudes.size(); ++i)
        {
            smoothed[i] = (1.0 - reducedSmoothingFactor) * amplitudes[i] +
                          reducedSmoothingFactor * m_previousAmplitudes[i];

            // 添加额外的随机波动
            double extraFluctuation = 0.5 * (QRandomGenerator::global()->generateDouble() - 0.5);
            smoothed[i] += extraFluctuation;
        }

        amplitudes = smoothed;
    }
}

double SignalAnalysisWidget::parseFrequencyText(const QString &text, const QString &unit)
{
    bool ok;
    double value = text.toDouble(&ok);

    if (!ok || value <= 0)
    {
        return 0.0;
    }

    // 转换为Hz
    if (unit == "Hz")
    {
        return value;
    }
    else if (unit == "kHz")
    {
        return value * 1e3;
    }
    else if (unit == "MHz")
    {
        return value * 1e6;
    }
    else if (unit == "GHz")
    {
        return value * 1e9;
    }

    return value; // 默认返回原值
}

QString SignalAnalysisWidget::formatFrequencyText(double frequency, const QString &unit)
{
    double value;

    if (unit == "Hz")
    {
        value = frequency;
    }
    else if (unit == "kHz")
    {
        value = frequency / 1e3;
    }
    else if (unit == "MHz")
    {
        value = frequency / 1e6;
    }
    else if (unit == "GHz")
    {
        value = frequency / 1e9;
    }
    else
    {
        value = frequency;
    }

    return QString::number(value, 'f', 3);
}

void SignalAnalysisWidget::updateFrequencyInputs()
{
    if (ui->centerFreqLineEdit && ui->centerFreqUnitCombo)
    {
        QString unit = ui->centerFreqUnitCombo->currentText();
        QString text = formatFrequencyText(m_centerFrequency, unit);
        ui->centerFreqLineEdit->setText(text);
    }

    if (ui->spanLineEdit && ui->spanUnitCombo)
    {
        QString unit = ui->spanUnitCombo->currentText();
        QString text = formatFrequencyText(m_bandwidth, unit);
        ui->spanLineEdit->setText(text);
    }
}
