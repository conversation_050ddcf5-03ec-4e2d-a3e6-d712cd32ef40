# LiteAPPStar 测试运行脚本
Write-Host "测试运行 LiteAPPStar..." -ForegroundColor Green

# 设置Qt环境变量
$QT_DIR = "D:\Qt\Qt5.14.2\5.14.2\msvc2017_64"
$env:PATH = "$QT_DIR\bin;$env:PATH"

# 检查可执行文件是否存在
if (-not (Test-Path "build\bin\Debug\LiteAPPStar.exe")) {
    Write-Host "可执行文件不存在，请先运行构建脚本" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "启动应用程序..." -ForegroundColor Yellow
Write-Host "注意：应用程序将在新窗口中打开" -ForegroundColor Cyan

# 启动应用程序
Start-Process -FilePath "build\bin\Debug\LiteAPPStar.exe" -WorkingDirectory (Get-Location)

Write-Host "应用程序已启动！" -ForegroundColor Green
Write-Host "如果应用程序没有正常启动，请检查Qt库是否正确安装。" -ForegroundColor Yellow
