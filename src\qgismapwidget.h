#ifndef QGISMAPWIDGET_H
#define QGISMAPWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QGroupBox>
#include <QSplitter>
#include <QTimer>
#include <QTextEdit>
#include <QTabWidget>
#include <QSlider>
#include <QSpinBox>
#include <QCheckBox>
#include <QProgressBar>
#include <QStatusBar>
#include <QListWidget>
#include <QTreeWidget>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QDateTime>
#include <QMap>
#include <QPair>

// 包含TileMapView头文件
#include "tilemapview.h"

// 前向声明
class DatabaseManager;
class QueryResult;
class GeographicDataPanel;

// 如果安装了QGIS开发库，可以启用这些头文件
#ifdef QGIS_ENABLED
#include <qgsapplication.h>
#include <qgsmapcanvas.h>
#include <qgsvectorlayer.h>
#include <qgsrasterlayer.h>
#include <qgsproject.h>
#include <qgslayertreeview.h>
#include <qgslayertreemodel.h>
#include <qgsmaptool.h>
#include <qgsmaptoolpan.h>
#include <qgsmaptoolzoom.h>
#include <qgscoordinatereferencesystem.h>
#include <qgspoint.h>
#include <qgsgeometry.h>
#include <qgsfeature.h>
#include <qgssymbol.h>
#include <qgsrenderer.h>
#endif

/**
 * @brief QGIS集成地图组件
 *
 * 提供专业的GIS功能，包括：
 * - 多种数据格式支持（Shapefile、GeoJSON、KML等）
 * - 图层管理和样式设置
 * - 空间分析工具
 * - 坐标系统转换
 * - 专业制图功能
 */
class QgisMapWidget : public QWidget
{
    Q_OBJECT

public:
    explicit QgisMapWidget(QWidget *parent = nullptr);
    ~QgisMapWidget();

    // 地图控制接口
    void setCenter(double latitude, double longitude);
    void setZoomLevel(int zoom);
    void addVectorLayer(const QString &path, const QString &name);
    void addRasterLayer(const QString &path, const QString &name);
    void removeLayer(const QString &layerId);
    void clearLayers();

    // 坐标系统
    void setCrs(const QString &crsCode);
    QString getCurrentCrs() const;

    // 地图工具
    void setPanTool();
    void setZoomInTool();
    void setZoomOutTool();
    void setIdentifyTool();

signals:
    // 地图事件信号
    void mapClicked(double x, double y);
    void mapExtentChanged();
    void layerAdded(const QString &layerId, const QString &name);
    void layerRemoved(const QString &layerId);
    void crsChanged(const QString &crsCode);

public slots:
    // 地图操作
    void zoomToFullExtent();
    void zoomToLayer(const QString &layerId);
    void refreshMap();
    void exportMap(const QString &filePath, const QString &format = "PNG");

    // 图层管理
    void toggleLayerVisibility(const QString &layerId, bool visible);
    void setLayerOpacity(const QString &layerId, double opacity);
    void moveLayerUp(const QString &layerId);
    void moveLayerDown(const QString &layerId);

    // 地理数据处理
    void refreshGeographicData();
    void showGeographicDataPanel();
    void hideGeographicDataPanel();
    void toggleGeographicDataPanel();

private slots:
    void onMapCanvasClicked();
    void onExtentChanged();
    void onLayerTreeChanged();
    void onAddVectorLayerClicked();
    void onAddRasterLayerClicked();
    void onRemoveLayerClicked();
    void onLayerPropertiesClicked();
    void onExportMapClicked();

    // 浮动工具面板槽函数
    void onLayerToggleClicked();
    void onMeasureClicked();
    void onMapClicked(double lat, double lng);

    // 地理数据面板槽函数
    void onGeographicDataPanelClosed();
    void onGeographicDataRefreshRequested();
    void onGeographicDataRowSelected(int row, const QStringList &data);
    void onDataPanelToggleClicked();

    // 定位功能槽函数
    void onLocationClicked();

    // 获取地图视图（用于子组件访问缩放级别等信息）
    TileMapView *getTileMapView() const { return m_tileMapView; }

protected:
    void resizeEvent(QResizeEvent *event) override;

private:
    // UI初始化
    void setupUI();
    void createMapCanvas();
    void createToolbar();
    void createFloatingToolPanel();
    void createLayerSelectorPanel();
    void createGeographicDataPanel();
    void createTopRightControlPanel();

    void createMapInfoPanel();
    void updateMapInfoPanel();
    void repositionMapInfoPanel(); // 重新定位地图信息面板
    void updateMouseCoordinateDisplay(double lat, double lng);
    void calculateDistanceAndDrawLine();
    double calculateHaversineDistance(double lat1, double lng1, double lat2, double lng2);
    void clearMeasureLines();
    void drawMeasureLine(double lat1, double lng1, double lat2, double lng2, double distance);
    void updateMeasureLinePositions(); // 更新测距线位置
    void drawStartPoint(double lat, double lng);
    void updateDynamicLine(double lat, double lng);
    void clearDynamicLine();

    // 目标图标管理
    enum TargetType
    {
        AIRCRAFT = 0,
        VEHICLE = 1,
        SHIP = 2
    };

    // 目标信息结构
    struct TargetInfo
    {
        QString id;
        TargetType type;
        QString label;
        double lat;
        double lng;
        QDateTime createTime;
        QString status;
        QStringList relatedMessages;
    };

    void addTargetIcon(double lat, double lng, TargetType type, const QString &label = "");
    void removeTargetIcon(const QString &iconId);
    void clearAllTargetIcons();
    void updateTargetIconPositions();
    void repositionFloatingToolPanel();
    void showTargetSelectionMenu(QPushButton *sourceButton);
    void addTargetAtMapCenter(TargetType type);
    void showTargetContextMenu(const QString &targetId, const QPoint &position);
    void showTargetTrajectory(const QString &targetId);
    void hideTargetTrajectory();
    void showTargetDetails(const QString &targetId);
    void showTargetMessages(const QString &targetId);
    void drawTrajectoryLine(double lat1, double lng1, double lat2, double lng2, int segmentIndex);
    void updateTrajectoryLinePositions(); // 更新轨迹线位置
    QPixmap createTargetIcon(TargetType type, int size = 32);
    void showBAreaLogs();
    void hideBAreaLogs();
    void updateBAreaLogPositions();
    void createLayerPanel();
    void createPropertiesPanel();
    void createStatusBar();
    void connectSignals();

    // QGIS初始化
    void initializeQgis();
    void setupMapTools();
    void loadDefaultLayers();

    // 辅助方法
    void updateLayerList();
    void updateMapInfo();
    void updateMapPlaceholder();
    void showLayerProperties(const QString &layerId);

    // 地理数据处理方法
    void processGeographicData();

    // 控制面板位置更新
    void updateTopRightControlPanelPosition();

    // 定位功能方法
    void showLocationDialog();
    void locateToCoordinate(double latitude, double longitude);
    void addLocationMarker(double latitude, double longitude);

    // UI组件
    QVBoxLayout *m_mainLayout;

    // 地图画布区域
    QWidget *m_mapContainer;
#ifdef QGIS_ENABLED
    QgsMapCanvas *m_mapCanvas;
    QgsMapToolPan *m_panTool;
    QgsMapToolZoomIn *m_zoomInTool;
    QgsMapToolZoomOut *m_zoomOutTool;
#else
    TileMapView *m_tileMapView;
    QLabel *m_mapPlaceholder; // 保留作为备用
#endif

    // 工具栏
    QWidget *m_toolbar;
    QPushButton *m_panBtn;
    QPushButton *m_zoomInBtn;
    QPushButton *m_zoomOutBtn;
    QPushButton *m_zoomFullBtn;
    QPushButton *m_identifyBtn;
    QPushButton *m_refreshBtn;

    // 浮动工具面板
    QWidget *m_floatingToolPanel;
    QPushButton *m_layerToggleBtn;
    QPushButton *m_measureBtn;

    // 右上角控制面板
    QWidget *m_topRightControlPanel;
    QPushButton *m_dataPanelToggleBtn;
    QPushButton *m_topRightMeasureBtn; // 右上角控制面板中的测量按钮

    // 定位标记
    QWidget *m_locationMarker;
    QWidget *m_layerSelectorPanel;

    // 地图信息面板
    QWidget *m_mapInfoPanel;
    QLabel *m_crsInfoLabel;
    QLabel *m_scaleInfoLabel;
    QLabel *m_zoomInfoLabel;
    QLabel *m_mouseCoordInfoLabel;

    // 图层面板
    QWidget *m_layerPanel;
    QVBoxLayout *m_layerLayout;
    QGroupBox *m_layerGroup;
#ifdef QGIS_ENABLED
    QgsLayerTreeView *m_layerTreeView;
    QgsLayerTreeModel *m_layerTreeModel;
#else
    QTreeWidget *m_layerTreeWidget;
#endif
    QPushButton *m_addVectorBtn;
    QPushButton *m_addRasterBtn;
    QPushButton *m_removeLayerBtn;
    QPushButton *m_layerPropsBtn;

    // 属性面板
    QWidget *m_propertiesPanel;
    QTabWidget *m_propertiesTabs;

    // 地图信息标签页
    QWidget *m_mapInfoTab;
    QLabel *m_crsLabel;
    QLabel *m_extentLabel;
    QLabel *m_scaleLabel;
    QComboBox *m_crsCombo;

    // 图层属性标签页
    QWidget *m_layerPropsTab;
    QLineEdit *m_layerNameEdit;
    QSlider *m_opacitySlider;
    QLabel *m_opacityLabel;
    QCheckBox *m_visibilityCheck;

    // 坐标输入标签页
    QWidget *m_coordTab;
    QLineEdit *m_xCoordEdit;
    QLineEdit *m_yCoordEdit;
    QPushButton *m_gotoCoordBtn;
    QLabel *m_mouseCoordLabel;

    // 状态栏
    QStatusBar *m_statusBar;
    QLabel *m_coordStatusLabel;
    QLabel *m_scaleStatusLabel;
    QLabel *m_crsStatusLabel;

    // 地理数据面板
    GeographicDataPanel *m_geographicDataPanel;

    // 数据成员
    QString m_currentCrs;
    QStringList m_layerIds;
    QString m_selectedLayerId;
    QList<QPair<double, double>> m_currentCoordinates;

    // 测距工具状态
    bool m_measureMode;
    QList<QPair<double, double>> m_measurePoints;
    double m_totalDistance;
    QList<QWidget *> m_measureLines; // 存储测距线条widget
    QWidget *m_startPointWidget;     // 起始点标记widget
    QWidget *m_dynamicLineWidget;    // 动态测量线widget
    bool m_hasStartPoint;            // 是否已设置起始点

    // B区域log标记
    QList<QWidget *> m_bAreaLogMarkers;
    QList<QPair<double, double>> m_bAreaLogPositions;

    // 目标图标管理
    QHash<QString, QWidget *> m_targetIcons;                 // 存储目标图标widget
    QHash<QString, QPair<double, double>> m_targetPositions; // 存储目标位置
    QHash<QString, TargetType> m_targetTypes;                // 存储目标类型
    QHash<QString, TargetInfo> m_targetInfos;                // 存储目标详细信息

    // 轨迹显示
    QList<QWidget *> m_trajectoryLines;  // 存储轨迹线条widget
    bool m_trajectoryVisible;            // 轨迹是否可见
    QString m_currentTrajectoryTargetId; // 当前显示轨迹的目标ID

    // 常量
    static const QString DEFAULT_CRS;
    static const int DEFAULT_ZOOM;
    static const double DEFAULT_LATITUDE;
    static const double DEFAULT_LONGITUDE;
    static const int LAYER_PANEL_WIDTH;
    static const int PROPERTIES_PANEL_WIDTH;
};

#endif // QGISMAPWIDGET_H
