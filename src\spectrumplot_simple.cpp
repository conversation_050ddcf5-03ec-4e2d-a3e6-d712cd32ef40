#include "spectrumplot_simple.h"
#include <QApplication>
#include <QContextMenuEvent>
#include <QDebug>

// 静态颜色定义 - 采用现代蓝紫色配色方案
const QColor SpectrumPlotSimple::SPECTRUM_COLOR = QColor(64, 158, 255); // 亮蓝色
const QColor SpectrumPlotSimple::HOLD_COLOR = QColor(255, 107, 129);    // 珊瑚红
const QColor SpectrumPlotSimple::GRID_COLOR = QColor(70, 70, 70);       // 深灰色
const QColor SpectrumPlotSimple::BACKGROUND_COLOR = QColor(25, 25, 35); // 深蓝灰色
const QColor SpectrumPlotSimple::TEXT_COLOR = QColor(200, 200, 200);    // 白灰色
const QColor SpectrumPlotSimple::AXIS_COLOR = QColor(180, 180, 180);    // 浅灰色

SpectrumPlotSimple::SpectrumPlotSimple(QWidget *parent)
    : QWidget(parent), m_startFreq(0.0), m_endFreq(1000000.0), m_minAmplitude(-120.0), m_maxAmplitude(0.0), m_holdMode(NoHold), m_avgCount(0), m_marginLeft(80), m_marginRight(20), m_marginTop(20), m_marginBottom(30), m_contextMenu(nullptr), m_dragging(false), m_dragStartFreq(0.0), m_dragStartEndFreq(0.0)
{
    setMinimumSize(400, 300);
    setMouseTracking(true);

    // 创建右键菜单
    m_contextMenu = new QMenu(this);

    // 保持功能子菜单
    QMenu *holdMenu = m_contextMenu->addMenu("保持功能");
    m_noHoldAction = holdMenu->addAction("关闭保持", this, &SpectrumPlotSimple::setNoHold);
    m_maxHoldAction = holdMenu->addAction("最大值保持", this, &SpectrumPlotSimple::setMaxHold);
    m_minHoldAction = holdMenu->addAction("最小值保持", this, &SpectrumPlotSimple::setMinHold);
    m_avgHoldAction = holdMenu->addAction("平均值保持", this, &SpectrumPlotSimple::setAvgHold);

    // 设置保持功能菜单项为可选中状态
    m_noHoldAction->setCheckable(true);
    m_maxHoldAction->setCheckable(true);
    m_minHoldAction->setCheckable(true);
    m_avgHoldAction->setCheckable(true);
    m_noHoldAction->setChecked(true); // 默认无保持

    m_contextMenu->addSeparator();
    m_clearHoldAction = m_contextMenu->addAction("清除保持数据", this, &SpectrumPlotSimple::clearHold);
    m_contextMenu->addSeparator();
    m_autoScaleAction = m_contextMenu->addAction("自动缩放", this, &SpectrumPlotSimple::autoScale);
    m_resetZoomAction = m_contextMenu->addAction("重置缩放", this, &SpectrumPlotSimple::resetZoom);
}

SpectrumPlotSimple::~SpectrumPlotSimple()
{
    // Qt会自动清理子对象
}

void SpectrumPlotSimple::setFrequencyRange(double startFreq, double endFreq)
{
    if (startFreq != m_startFreq || endFreq != m_endFreq)
    {
        m_startFreq = startFreq;
        m_endFreq = endFreq;
        update();
        emit frequencyRangeChanged(startFreq, endFreq);
    }
}

void SpectrumPlotSimple::setAmplitudeRange(double minAmplitude, double maxAmplitude)
{
    if (minAmplitude != m_minAmplitude || maxAmplitude != m_maxAmplitude)
    {
        m_minAmplitude = minAmplitude;
        m_maxAmplitude = maxAmplitude;
        update();
    }
}

void SpectrumPlotSimple::updateSpectrumData(const QVector<double> &frequencies, const QVector<double> &amplitudes)
{
    if (frequencies.size() != amplitudes.size() || frequencies.isEmpty())
    {
        return;
    }

    m_frequencies = frequencies;
    m_amplitudes = amplitudes;

    // 更新保持数据
    if (m_holdMode != NoHold)
    {
        updateHoldData(amplitudes);
    }

    update();
}

void SpectrumPlotSimple::setHoldMode(HoldMode mode)
{
    if (m_holdMode != mode)
    {
        m_holdMode = mode;

        if (mode == NoHold)
        {
            m_holdData.clear();
            m_avgBuffer.clear();
            m_avgCount = 0;
        }
        else if (!m_amplitudes.isEmpty())
        {
            // 确保保持数据大小正确
            m_holdData.resize(m_amplitudes.size());
            m_holdData = m_amplitudes;

            if (mode == AvgHold)
            {
                m_avgBuffer.resize(m_amplitudes.size());
                m_avgBuffer = m_amplitudes;
                m_avgCount = 1;
            }
        }

        updateHoldMenuState();
        update();
    }
}

void SpectrumPlotSimple::clearHoldData()
{
    m_holdData.clear();
    m_avgBuffer.clear();
    m_avgCount = 0;
    update();
}

QPair<double, double> SpectrumPlotSimple::frequencyRange() const
{
    return QPair<double, double>(m_startFreq, m_endFreq);
}

QPair<double, double> SpectrumPlotSimple::amplitudeRange() const
{
    return QPair<double, double>(m_minAmplitude, m_maxAmplitude);
}

void SpectrumPlotSimple::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 计算绘图区域
    m_plotRect = QRect(m_marginLeft, m_marginTop,
                       width() - m_marginLeft - m_marginRight,
                       height() - m_marginTop - m_marginBottom);

    // 绘制背景和网格
    drawBackground(painter);

    // 绘制坐标轴
    drawAxes(painter);

    // 绘制频谱曲线
    drawSpectrum(painter);

    // 绘制保持曲线
    if (m_holdMode != NoHold && !m_holdData.isEmpty())
    {
        drawHoldData(painter);
    }
}

void SpectrumPlotSimple::drawBackground(QPainter &painter)
{
    // 绘制背景
    painter.fillRect(rect(), BACKGROUND_COLOR);

    // 绘制绘图区域背景
    painter.fillRect(m_plotRect, BACKGROUND_COLOR.lighter(110));

    // 绘制网格
    painter.setPen(QPen(GRID_COLOR, 1, Qt::DotLine));

    // 垂直网格线
    int numVerticalLines = 10;
    for (int i = 0; i <= numVerticalLines; ++i)
    {
        int x = m_plotRect.left() + i * m_plotRect.width() / numVerticalLines;
        painter.drawLine(x, m_plotRect.top(), x, m_plotRect.bottom());
    }

    // 水平网格线
    int numHorizontalLines = 8;
    for (int i = 0; i <= numHorizontalLines; ++i)
    {
        int y = m_plotRect.top() + i * m_plotRect.height() / numHorizontalLines;
        painter.drawLine(m_plotRect.left(), y, m_plotRect.right(), y);
    }
}

void SpectrumPlotSimple::drawAxes(QPainter &painter)
{
    painter.setPen(QPen(AXIS_COLOR, 2));
    painter.setFont(QFont("Microsoft YaHei", 8));

    // 绘制坐标轴框架
    painter.drawRect(m_plotRect);

    // 绘制频率轴标签
    painter.setPen(QPen(TEXT_COLOR, 1));
    int numFreqLabels = 10; // 与网格线数量对应
    for (int i = 0; i <= numFreqLabels; ++i)
    {
        // 使用与网格线完全相同的X坐标计算方法
        int x = m_plotRect.left() + i * m_plotRect.width() / numFreqLabels;

        // 只在偶数位置显示标签，避免过于密集
        if (i % 2 == 0)
        {
            double freq = m_startFreq + i * (m_endFreq - m_startFreq) / numFreqLabels;
            QString label = formatFrequency(freq);
            QFontMetrics fm(painter.font());
            QRect textRect = fm.boundingRect(label);
            painter.drawText(x - textRect.width() / 2, m_plotRect.bottom() + 15, label);
        }

        // 绘制刻度线
        painter.drawLine(x, m_plotRect.bottom(), x, m_plotRect.bottom() + 5);
    }

    // 绘制幅度轴标签
    int numAmpLabels = 8;
    for (int i = 0; i <= numAmpLabels; ++i)
    {
        double amp = m_minAmplitude + i * (m_maxAmplitude - m_minAmplitude) / numAmpLabels;
        int y = amplitudeToY(amp);
        QString label = QString("%1").arg(amp, 0, 'f', 0);

        QFontMetrics fm(painter.font());
        QRect textRect = fm.boundingRect(label);

        // 避免最下面的标签与频率轴标签重叠
        int textY = y + textRect.height() / 2;
        if (i == numAmpLabels)
        {                                                 // 最下面的标签
            textY = qMin(textY, m_plotRect.bottom() - 5); // 确保不超出绘图区域底部
        }

        painter.drawText(m_marginLeft - textRect.width() - 15, textY, label);

        // 绘制刻度线
        painter.drawLine(m_plotRect.left() - 5, y, m_plotRect.left(), y);
    }

    // 绘制轴标题
    painter.save();
    painter.translate(20, m_plotRect.center().y());
    painter.rotate(-90);
    painter.drawText(-50, 0, "幅度 (dBm)");
    painter.restore();

    // 移除底部"频率"标题，避免重复显示
}

void SpectrumPlotSimple::drawSpectrum(QPainter &painter)
{
    if (m_frequencies.size() < 2 || m_amplitudes.size() < 2)
    {
        return;
    }

    painter.setPen(QPen(SPECTRUM_COLOR, 2));

    // 创建路径
    QPainterPath path;
    bool firstPoint = true;

    for (int i = 0; i < m_frequencies.size() && i < m_amplitudes.size(); ++i)
    {
        int x = frequencyToX(m_frequencies[i]);
        int y = amplitudeToY(m_amplitudes[i]);

        if (x >= m_plotRect.left() && x <= m_plotRect.right())
        {
            if (firstPoint)
            {
                path.moveTo(x, y);
                firstPoint = false;
            }
            else
            {
                path.lineTo(x, y);
            }
        }
    }

    painter.drawPath(path);

    // 绘制填充区域
    if (!path.isEmpty())
    {
        QPainterPath fillPath = path;
        QPointF lastPoint = path.currentPosition();
        fillPath.lineTo(lastPoint.x(), m_plotRect.bottom());
        fillPath.lineTo(m_plotRect.left(), m_plotRect.bottom());
        fillPath.closeSubpath();

        QColor fillColor = SPECTRUM_COLOR;
        fillColor.setAlpha(30);
        painter.fillPath(fillPath, QBrush(fillColor));
    }
}

void SpectrumPlotSimple::drawHoldData(QPainter &painter)
{
    if (m_frequencies.size() < 2 || m_holdData.size() < 2)
    {
        return;
    }

    painter.setPen(QPen(HOLD_COLOR, 1, Qt::DashLine));

    QPainterPath path;
    bool firstPoint = true;

    for (int i = 0; i < m_frequencies.size() && i < m_holdData.size(); ++i)
    {
        int x = frequencyToX(m_frequencies[i]);
        int y = amplitudeToY(m_holdData[i]);

        if (x >= m_plotRect.left() && x <= m_plotRect.right())
        {
            if (firstPoint)
            {
                path.moveTo(x, y);
                firstPoint = false;
            }
            else
            {
                path.lineTo(x, y);
            }
        }
    }

    painter.drawPath(path);
}

void SpectrumPlotSimple::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_plotRect.contains(event->pos()))
    {
        // 开始拖拽
        m_dragging = true;
        m_lastMousePos = event->pos();
        m_dragStartFreq = m_startFreq;
        m_dragStartEndFreq = m_endFreq;

        // 设置鼠标光标为拖拽状态
        setCursor(Qt::ClosedHandCursor);

        // 发射数据点选择信号
        double freq = xToFrequency(event->pos().x());
        double amp = yToAmplitude(event->pos().y());
        emit dataPointSelected(freq, amp);
    }

    QWidget::mousePressEvent(event);
}

void SpectrumPlotSimple::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton))
    {
        // 计算鼠标移动的距离
        int deltaX = event->pos().x() - m_lastMousePos.x();

        // 计算频率偏移量
        double freqRange = m_dragStartEndFreq - m_dragStartFreq;
        double freqDelta = -deltaX * freqRange / m_plotRect.width(); // 负号使拖拽方向符合直觉

        // 计算新的频率范围
        double newStartFreq = m_dragStartFreq + freqDelta;
        double newEndFreq = m_dragStartEndFreq + freqDelta;

        // 设置新的频率范围
        setFrequencyRange(newStartFreq, newEndFreq);
    }
    else if (m_plotRect.contains(event->pos()))
    {
        // 在绘图区域内时显示拖拽光标
        setCursor(Qt::OpenHandCursor);
    }
    else
    {
        // 在绘图区域外时恢复默认光标
        setCursor(Qt::ArrowCursor);
    }

    QWidget::mouseMoveEvent(event);
}

void SpectrumPlotSimple::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_dragging)
    {
        // 结束拖拽
        m_dragging = false;

        // 恢复光标
        if (m_plotRect.contains(event->pos()))
        {
            setCursor(Qt::OpenHandCursor);
        }
        else
        {
            setCursor(Qt::ArrowCursor);
        }
    }

    QWidget::mouseReleaseEvent(event);
}

void SpectrumPlotSimple::wheelEvent(QWheelEvent *event)
{
    // 简单的缩放功能
    if (m_plotRect.contains(event->position().toPoint()))
    {
        double scaleFactor = event->angleDelta().y() > 0 ? 0.9 : 1.1;
        double centerFreq = (m_startFreq + m_endFreq) / 2.0;
        double bandwidth = m_endFreq - m_startFreq;
        double newBandwidth = bandwidth * scaleFactor;

        setFrequencyRange(centerFreq - newBandwidth / 2.0, centerFreq + newBandwidth / 2.0);
    }

    QWidget::wheelEvent(event);
}

void SpectrumPlotSimple::contextMenuEvent(QContextMenuEvent *event)
{
    if (m_contextMenu)
    {
        m_contextMenu->exec(event->globalPos());
    }
}

void SpectrumPlotSimple::autoScale()
{
    if (!m_amplitudes.isEmpty())
    {
        double minAmp = *std::min_element(m_amplitudes.begin(), m_amplitudes.end());
        double maxAmp = *std::max_element(m_amplitudes.begin(), m_amplitudes.end());
        double margin = (maxAmp - minAmp) * 0.1;
        setAmplitudeRange(minAmp - margin, maxAmp + margin);
    }
}

void SpectrumPlotSimple::resetZoom()
{
    if (!m_frequencies.isEmpty())
    {
        double minFreq = *std::min_element(m_frequencies.begin(), m_frequencies.end());
        double maxFreq = *std::max_element(m_frequencies.begin(), m_frequencies.end());
        setFrequencyRange(minFreq, maxFreq);
    }

    setAmplitudeRange(-120.0, 0.0);
}

void SpectrumPlotSimple::clearHold()
{
    clearHoldData();
}

void SpectrumPlotSimple::setMaxHold()
{
    setHoldMode(MaxHold);
    updateHoldMenuState();
}

void SpectrumPlotSimple::setMinHold()
{
    setHoldMode(MinHold);
    updateHoldMenuState();
}

void SpectrumPlotSimple::setAvgHold()
{
    setHoldMode(AvgHold);
    updateHoldMenuState();
}

void SpectrumPlotSimple::setNoHold()
{
    setHoldMode(NoHold);
    updateHoldMenuState();
}

void SpectrumPlotSimple::updateHoldMenuState()
{
    if (!m_noHoldAction || !m_maxHoldAction || !m_minHoldAction || !m_avgHoldAction)
        return;

    // 清除所有选中状态
    m_noHoldAction->setChecked(false);
    m_maxHoldAction->setChecked(false);
    m_minHoldAction->setChecked(false);
    m_avgHoldAction->setChecked(false);

    // 根据当前保持模式设置对应的选中状态
    switch (m_holdMode)
    {
    case NoHold:
        m_noHoldAction->setChecked(true);
        break;
    case MaxHold:
        m_maxHoldAction->setChecked(true);
        break;
    case MinHold:
        m_minHoldAction->setChecked(true);
        break;
    case AvgHold:
        m_avgHoldAction->setChecked(true);
        break;
    }
}

int SpectrumPlotSimple::frequencyToX(double frequency) const
{
    if (m_endFreq == m_startFreq)
        return m_plotRect.left();

    double ratio = (frequency - m_startFreq) / (m_endFreq - m_startFreq);
    return m_plotRect.left() + static_cast<int>(ratio * m_plotRect.width());
}

int SpectrumPlotSimple::amplitudeToY(double amplitude) const
{
    if (m_maxAmplitude == m_minAmplitude)
        return m_plotRect.center().y();

    double ratio = (amplitude - m_minAmplitude) / (m_maxAmplitude - m_minAmplitude);
    return m_plotRect.bottom() - static_cast<int>(ratio * m_plotRect.height());
}

double SpectrumPlotSimple::xToFrequency(int x) const
{
    if (m_plotRect.width() == 0)
        return m_startFreq;

    double ratio = static_cast<double>(x - m_plotRect.left()) / m_plotRect.width();
    return m_startFreq + ratio * (m_endFreq - m_startFreq);
}

double SpectrumPlotSimple::yToAmplitude(int y) const
{
    if (m_plotRect.height() == 0)
        return m_minAmplitude;

    double ratio = static_cast<double>(m_plotRect.bottom() - y) / m_plotRect.height();
    return m_minAmplitude + ratio * (m_maxAmplitude - m_minAmplitude);
}

QString SpectrumPlotSimple::formatFrequency(double frequency) const
{
    if (frequency >= 1e9)
    {
        return QString("%1G").arg(frequency / 1e9, 0, 'f', 1);
    }
    else if (frequency >= 1e6)
    {
        return QString("%1M").arg(frequency / 1e6, 0, 'f', 1);
    }
    else if (frequency >= 1e3)
    {
        return QString("%1k").arg(frequency / 1e3, 0, 'f', 1);
    }
    else
    {
        return QString("%1").arg(frequency, 0, 'f', 0);
    }
}

void SpectrumPlotSimple::updateHoldData(const QVector<double> &amplitudes)
{
    // 检查输入数据有效性
    if (amplitudes.isEmpty())
    {
        return;
    }

    // 如果保持数据大小不匹配，重新初始化
    if (m_holdData.size() != amplitudes.size())
    {
        m_holdData = amplitudes;
        if (m_holdMode == AvgHold)
        {
            m_avgBuffer = amplitudes;
            m_avgCount = 1;
        }
        return;
    }

    // 对于平均值保持，确保avgBuffer大小正确
    if (m_holdMode == AvgHold && m_avgBuffer.size() != amplitudes.size())
    {
        m_avgBuffer = amplitudes;
        m_avgCount = 1;
        m_holdData = m_avgBuffer;
        return;
    }

    switch (m_holdMode)
    {
    case MaxHold:
        for (int i = 0; i < amplitudes.size() && i < m_holdData.size(); ++i)
        {
            if (amplitudes[i] > m_holdData[i])
            {
                m_holdData[i] = amplitudes[i];
            }
        }
        break;

    case MinHold:
        for (int i = 0; i < amplitudes.size() && i < m_holdData.size(); ++i)
        {
            if (amplitudes[i] < m_holdData[i])
            {
                m_holdData[i] = amplitudes[i];
            }
        }
        break;

    case AvgHold:
        m_avgCount++;
        for (int i = 0; i < amplitudes.size() && i < m_avgBuffer.size(); ++i)
        {
            m_avgBuffer[i] = (m_avgBuffer[i] * (m_avgCount - 1) + amplitudes[i]) / m_avgCount;
        }
        m_holdData = m_avgBuffer;
        break;

    default:
        break;
    }
}
