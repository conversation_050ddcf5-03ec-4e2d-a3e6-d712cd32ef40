#ifndef QTLOCATIONMAPVIEW_H
#define QTLOCATIONMAPVIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QSlider>
#include <QSpinBox>
#include <QGroupBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QSplitter>

// Qt Quick 相关头文件
#include <QtQuick/QQuickView>
#include <QtQml/QQmlEngine>
#include <QtQml/QQmlContext>
#include <QtQuickWidgets/QQuickWidget>

/**
 * @brief Qt Location 地图视图组件
 *
 * 使用Qt Location框架提供专业的地图显示功能，支持：
 * - 多种地图提供商（OpenStreetMap、Mapbox等）
 * - 缩放、平移、旋转
 * - 标记和几何图形
 * - 地理编码和反地理编码
 * - GPS定位支持
 */
class QtLocationMapView : public QWidget
{
    Q_OBJECT

public:
    explicit QtLocationMapView(QWidget *parent = nullptr);
    ~QtLocationMapView();

    // 地图控制
    void setCenter(double latitude, double longitude);
    void setZoomLevel(double zoom);
    void setMapType(const QString &mapType);

    // 获取地图状态
    QPair<double, double> getCenter() const;
    double getZoomLevel() const;
    QString getMapType() const;

    // 标记管理
    void addMarker(double latitude, double longitude, const QString &title = "", const QString &description = "");
    void removeMarker(int markerId);
    void clearMarkers();

    // 几何图形
    void addCircle(double latitude, double longitude, double radius, const QColor &color = Qt::red);
    void addPolygon(const QList<QPair<double, double>> &coordinates, const QColor &color = Qt::blue);
    void addPolyline(const QList<QPair<double, double>> &coordinates, const QColor &color = Qt::green);

signals:
    // 地图事件信号
    void mapClicked(double latitude, double longitude);
    void mapDoubleClicked(double latitude, double longitude);
    void centerChanged(double latitude, double longitude);
    void zoomChanged(double zoom);
    void mapTypeChanged(const QString &mapType);

    // 标记事件信号
    void markerClicked(int markerId, double latitude, double longitude);

public slots:
    // 地图操作槽函数
    void zoomIn();
    void zoomOut();
    void resetView();
    void fitToMarkers();

    // 地图类型切换
    void setOpenStreetMapType();
    void setSatelliteMapType();
    void setHybridMapType();

private slots:
    void onMapReady();
    void onZoomSliderChanged(int value);
    void onMapTypeComboChanged(const QString &text);
    void onCenterCoordinateChanged();
    void onQmlCenterChanged(double latitude, double longitude);
    void onQmlZoomChanged(double zoom);

private:
    // UI组件
    void setupUI();
    void setupMapWidget();
    void setupControlPanel();
    void connectSignals();

    // 地图初始化
    void initializeMap();
    void loadMapProviders();
    void setupMapProperties();
    void createFallbackMap();

    // QML地图相关
    QQuickWidget *m_mapWidget;
    QQmlEngine *m_qmlEngine;

    // 控制面板
    QWidget *m_controlPanel;
    QVBoxLayout *m_controlLayout;

    // 地图控制组件
    QGroupBox *m_mapControlGroup;
    QComboBox *m_mapTypeCombo;
    QSlider *m_zoomSlider;
    QSpinBox *m_zoomSpinBox;
    QPushButton *m_zoomInBtn;
    QPushButton *m_zoomOutBtn;
    QPushButton *m_resetViewBtn;

    // 坐标显示组件
    QGroupBox *m_coordinateGroup;
    QLineEdit *m_latitudeEdit;
    QLineEdit *m_longitudeEdit;
    QPushButton *m_gotoCoordinateBtn;

    // 标记管理组件
    QGroupBox *m_markerGroup;
    QPushButton *m_addMarkerBtn;
    QPushButton *m_clearMarkersBtn;
    QTextEdit *m_markerListText;

    // 地图状态
    QPair<double, double> m_currentCenter;
    double m_currentZoom;
    QString m_currentMapType;

    // 标记管理
    QList<int> m_markerIds;
    int m_nextMarkerId;

    // 地图提供商（简化版本不使用）
    void *m_serviceProvider;
    QStringList m_availableMapTypes;

    // 布局
    QSplitter *m_mainSplitter;

    // 常量
    static const double DEFAULT_LATITUDE;
    static const double DEFAULT_LONGITUDE;
    static const double DEFAULT_ZOOM;
    static const double MIN_ZOOM;
    static const double MAX_ZOOM;
};

#endif // QTLOCATIONMAPVIEW_H
