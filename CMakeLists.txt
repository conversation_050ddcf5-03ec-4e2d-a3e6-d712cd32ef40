cmake_minimum_required(VERSION 3.20)

project(LiteAPPStar VERSION 0.4.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Visual Studio 2022 特定配置
if(MSVC)
    # 设置编译器标志
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /utf-8 /FS")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /MDd")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /MD")

    # 设置警告级别
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")

    # 禁用一些不必要的警告
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4251 /wd4275 /wd4996")

    # 抑制Microsoft STL弃用警告
    add_definitions(-D_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING)
    add_definitions(-D_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS)
endif()

# 设置Qt5相关配置
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置Qt5路径（针对Visual Studio 2022使用MSVC 2017_64版本）
if(MSVC)
    set(CMAKE_PREFIX_PATH "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64")
    message(STATUS "Using Qt5 MSVC 2017 64-bit version: ${CMAKE_PREFIX_PATH}")
endif()

# 查找Qt5组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Gui Network Quick QuickWidgets Qml Svg Sql)

# 查找QGIS组件（可选）
option(ENABLE_QGIS "Enable QGIS support" ON)

if(ENABLE_QGIS)
    # 设置QGIS路径
    if(WIN32)
        set(QGIS_PREFIX_PATH "C:/OSGeo4W64" CACHE PATH "QGIS installation path")
        set(CMAKE_PREFIX_PATH ${CMAKE_PREFIX_PATH} ${QGIS_PREFIX_PATH})

        # 查找QGIS库
        find_path(QGIS_INCLUDE_DIR qgis.h
            PATHS ${QGIS_PREFIX_PATH}/include
            PATH_SUFFIXES qgis
        )

        find_library(QGIS_CORE_LIBRARY
            NAMES qgis_core
            PATHS ${QGIS_PREFIX_PATH}/lib
        )

        find_library(QGIS_GUI_LIBRARY
            NAMES qgis_gui
            PATHS ${QGIS_PREFIX_PATH}/lib
        )

        if(QGIS_INCLUDE_DIR AND QGIS_CORE_LIBRARY AND QGIS_GUI_LIBRARY)
            set(QGIS_FOUND TRUE)
            message(STATUS "QGIS found: ${QGIS_PREFIX_PATH}")
            add_definitions(-DQGIS_ENABLED)
        else()
            set(QGIS_FOUND FALSE)
            message(STATUS "QGIS not found, disabling QGIS support")
        endif()
    else()
        # Linux/macOS QGIS查找
        find_package(PkgConfig QUIET)
        if(PKG_CONFIG_FOUND)
            pkg_check_modules(QGIS QUIET qgis)
            if(QGIS_FOUND)
                add_definitions(-DQGIS_ENABLED)
                message(STATUS "QGIS found via pkg-config")
            endif()
        endif()
    endif()
else()
    set(QGIS_FOUND FALSE)
    message(STATUS "QGIS support disabled")
endif()

# 设置包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_BINARY_DIR})

# 收集源文件
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.c"
)

file(GLOB_RECURSE HEADERS
    "include/*.h"
    "include/*.hpp"
    "src/*.h"
)

# 收集UI文件
file(GLOB_RECURSE UI_FILES
    "include/*.ui"
    "src/*.ui"
)

# 收集资源文件
file(GLOB_RECURSE RESOURCES
    "resources/*.qrc"
)

# 创建可执行文件
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# 链接Qt5库
target_link_libraries(${PROJECT_NAME}
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui
    Qt5::Network
    Qt5::Quick
    Qt5::QuickWidgets
    Qt5::Qml
    Qt5::Svg
    Qt5::Sql
)

# 链接QGIS库（如果找到）
if(QGIS_FOUND)
    target_include_directories(${PROJECT_NAME} PRIVATE ${QGIS_INCLUDE_DIR})
    target_link_libraries(${PROJECT_NAME}
        ${QGIS_CORE_LIBRARY}
        ${QGIS_GUI_LIBRARY}
    )
    message(STATUS "Linking QGIS libraries: ${QGIS_CORE_LIBRARY}, ${QGIS_GUI_LIBRARY}")
endif()

# 确保Qt5库路径正确
if(MSVC)
    # 设置运行时库路径
    set_target_properties(${PROJECT_NAME} PROPERTIES
        VS_DEBUGGER_ENVIRONMENT "PATH=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin;%PATH%"
    )
endif()

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/bin/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/bin/Release
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定配置
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # 设置Windows图标
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/resources/app.ico")
        set(WINDOWS_RESOURCE_FILE "${CMAKE_CURRENT_SOURCE_DIR}/resources/app.rc")
        target_sources(${PROJECT_NAME} PRIVATE ${WINDOWS_RESOURCE_FILE})
    endif()
endif()

# macOS特定配置
if(APPLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_INFO_PLIST ${CMAKE_CURRENT_SOURCE_DIR}/resources/Info.plist
    )
endif()

# 安装配置
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# CPack配置（用于打包）
set(CPACK_PACKAGE_NAME "LiteAPPStar")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "跨平台桌面应用程序")
set(CPACK_PACKAGE_VENDOR "LiteAPPStar Team")

if(WIN32)
    set(CPACK_GENERATOR "NSIS")
elseif(APPLE)
    set(CPACK_GENERATOR "DragNDrop")
else()
    set(CPACK_GENERATOR "DEB;RPM")
endif()

include(CPack)
