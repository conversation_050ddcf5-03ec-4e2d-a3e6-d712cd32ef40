#ifndef LOCALTILEMANAGER_H
#define LOCALTILEMANAGER_H

#include <QObject>
#include <QString>
#include <QPixmap>
#include <QDir>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QTimer>

/**
 * @brief 本地瓦片管理器
 *
 * 负责下载和管理本地瓦片缓存
 */
class LocalTileManager : public QObject
{
    Q_OBJECT

public:
    explicit LocalTileManager(QObject *parent = nullptr);
    ~LocalTileManager();

    /**
     * @brief 设置瓦片缓存目录
     * @param cacheDir 缓存目录路径
     */
    void setCacheDirectory(const QString &cacheDir);

    /**
     * @brief 获取瓦片（优先从本地加载，不存在则下载）
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @return 瓦片图像，如果不存在返回空图像
     */
    QPixmap getTile(int x, int y, int z);

    /**
     * @brief 检查瓦片是否存在于本地
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @return true表示存在，false表示不存在
     */
    bool tileExists(int x, int y, int z) const;

    /**
     * @brief 下载指定区域的瓦片
     * @param minX 最小X坐标
     * @param maxX 最大X坐标
     * @param minY 最小Y坐标
     * @param maxY 最大Y坐标
     * @param minZ 最小缩放级别
     * @param maxZ 最大缩放级别
     */
    void downloadTiles(int minX, int maxX, int minY, int maxY, int minZ, int maxZ);

    /**
     * @brief 下载中国区域的瓦片（1-4级）
     */
    void downloadChinaTiles();

    /**
     * @brief 获取瓦片文件路径
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @return 瓦片文件路径
     */
    QString getTilePath(int x, int y, int z) const;

signals:
    /**
     * @brief 瓦片下载完成信号
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @param success 是否成功
     */
    void tileDownloaded(int x, int y, int z, bool success);

    /**
     * @brief 下载进度信号
     * @param current 当前下载数量
     * @param total 总下载数量
     */
    void downloadProgress(int current, int total);

public:
    struct DownloadTask
    {
        int x, y, z;
        QString url;
        QString filePath;
    };

private slots:
    void onTileDownloadFinished();
    void onTileDownloadFinished(QNetworkReply *reply, const DownloadTask &task);

private:
    /**
     * @brief 获取瓦片URL
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @return 瓦片URL
     */
    QString getTileUrl(int x, int y, int z) const;

    /**
     * @brief 创建瓦片目录
     * @param z 缩放级别
     * @param x 瓦片X坐标
     */
    void createTileDirectory(int z, int x);

    /**
     * @brief 开始下一个下载任务
     */
    void startNextDownload();

    /**
     * @brief 测试网络连接
     */
    void testNetworkConnection();

    /**
     * @brief 创建测试瓦片
     */
    bool createTestTile(int x, int y, int z, const QString &filePath);

    /**
     * @brief 创建占位瓦片
     */
    QPixmap createPlaceholderTile(int x, int y, int z);

    /**
     * @brief 从较低级别的瓦片中放大获取瓦片
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @param z 缩放级别
     * @return 放大后的瓦片，如果无法获取则返回空QPixmap
     */
    QPixmap getScaledTileFromLowerLevel(int x, int y, int z);

private:
    QString m_cacheDir;                      ///< 缓存目录
    QNetworkAccessManager *m_networkManager; ///< 网络管理器

    QList<DownloadTask> m_downloadQueue; ///< 下载队列
    int m_currentDownloads;              ///< 当前下载数量
    int m_maxConcurrentDownloads;        ///< 最大并发下载数
    int m_totalTasks;                    ///< 总任务数
    int m_completedTasks;                ///< 已完成任务数

    QTimer *m_downloadTimer; ///< 下载定时器

    // 本地瓦片支持
    bool m_useLocalTiles;   ///< 是否使用本地瓦片
    QString m_localTileDir; ///< 本地瓦片目录
};

#endif // LOCALTILEMANAGER_H
