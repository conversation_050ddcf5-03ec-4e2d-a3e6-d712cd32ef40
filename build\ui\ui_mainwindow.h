/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionNew;
    QAction *actionOpen;
    QAction *actionSave;
    QAction *actionSaveAs;
    QAction *actionExit;
    QAction *actionShowSpectrum;
    QAction *actionShowWaterfall;
    QAction *actionShowDataTable;
    QAction *actionShowQgisMap;
    QAction *actionAbout;
    QAction *actionAboutQt;
    QAction *actionShowSignalAnalysis;
    QAction *actionShowComprehensiveView;
    QWidget *centralwidget;
    QMenuBar *menubar;
    QMenu *menuFile;
    QMenu *menuView;
    QMenu *menuHelp;
    QStatusBar *statusbar;
    QToolBar *toolBar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1400, 900);
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/app.ico"), QSize(), QIcon::Normal, QIcon::Off);
        MainWindow->setWindowIcon(icon);
        actionNew = new QAction(MainWindow);
        actionNew->setObjectName(QString::fromUtf8("actionNew"));
        actionOpen = new QAction(MainWindow);
        actionOpen->setObjectName(QString::fromUtf8("actionOpen"));
        actionSave = new QAction(MainWindow);
        actionSave->setObjectName(QString::fromUtf8("actionSave"));
        actionSaveAs = new QAction(MainWindow);
        actionSaveAs->setObjectName(QString::fromUtf8("actionSaveAs"));
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName(QString::fromUtf8("actionExit"));
        actionShowSpectrum = new QAction(MainWindow);
        actionShowSpectrum->setObjectName(QString::fromUtf8("actionShowSpectrum"));
        actionShowSpectrum->setCheckable(true);
        actionShowSpectrum->setChecked(true);
        actionShowWaterfall = new QAction(MainWindow);
        actionShowWaterfall->setObjectName(QString::fromUtf8("actionShowWaterfall"));
        actionShowWaterfall->setCheckable(true);
        actionShowWaterfall->setChecked(true);
        actionShowDataTable = new QAction(MainWindow);
        actionShowDataTable->setObjectName(QString::fromUtf8("actionShowDataTable"));
        actionShowDataTable->setCheckable(true);
        actionShowDataTable->setChecked(true);
        actionShowQgisMap = new QAction(MainWindow);
        actionShowQgisMap->setObjectName(QString::fromUtf8("actionShowQgisMap"));
        actionAbout = new QAction(MainWindow);
        actionAbout->setObjectName(QString::fromUtf8("actionAbout"));
        actionAboutQt = new QAction(MainWindow);
        actionAboutQt->setObjectName(QString::fromUtf8("actionAboutQt"));
        actionShowSignalAnalysis = new QAction(MainWindow);
        actionShowSignalAnalysis->setObjectName(QString::fromUtf8("actionShowSignalAnalysis"));
        actionShowComprehensiveView = new QAction(MainWindow);
        actionShowComprehensiveView->setObjectName(QString::fromUtf8("actionShowComprehensiveView"));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName(QString::fromUtf8("menubar"));
        menubar->setGeometry(QRect(0, 0, 1400, 22));
        menuFile = new QMenu(menubar);
        menuFile->setObjectName(QString::fromUtf8("menuFile"));
        menuView = new QMenu(menubar);
        menuView->setObjectName(QString::fromUtf8("menuView"));
        menuHelp = new QMenu(menubar);
        menuHelp->setObjectName(QString::fromUtf8("menuHelp"));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        MainWindow->setStatusBar(statusbar);
        toolBar = new QToolBar(MainWindow);
        toolBar->setObjectName(QString::fromUtf8("toolBar"));
        MainWindow->addToolBar(Qt::TopToolBarArea, toolBar);

        menubar->addAction(menuFile->menuAction());
        menubar->addAction(actionShowSignalAnalysis);
        menubar->addAction(actionShowQgisMap);
        menubar->addAction(actionShowComprehensiveView);
        menubar->addAction(menuView->menuAction());
        menubar->addAction(menuHelp->menuAction());
        menuFile->addAction(actionNew);
        menuFile->addAction(actionOpen);
        menuFile->addAction(actionSave);
        menuFile->addAction(actionSaveAs);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuView->addAction(actionShowSpectrum);
        menuView->addAction(actionShowWaterfall);
        menuView->addAction(actionShowDataTable);
        menuHelp->addAction(actionAbout);
        menuHelp->addAction(actionAboutQt);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "LiteAPPStar - \350\275\273\351\207\217\347\272\247\344\277\241\345\217\267\345\210\206\346\236\220\345\267\245\345\205\267", nullptr));
        actionNew->setText(QCoreApplication::translate("MainWindow", "\346\226\260\345\273\272(&N)", nullptr));
#if QT_CONFIG(shortcut)
        actionNew->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+N", nullptr));
#endif // QT_CONFIG(shortcut)
        actionOpen->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200(&O)", nullptr));
#if QT_CONFIG(shortcut)
        actionOpen->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+O", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSave->setText(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230(&S)", nullptr));
#if QT_CONFIG(shortcut)
        actionSave->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSaveAs->setText(QCoreApplication::translate("MainWindow", "\345\217\246\345\255\230\344\270\272(&A)", nullptr));
#if QT_CONFIG(shortcut)
        actionSaveAs->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Shift+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionExit->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272(&X)", nullptr));
#if QT_CONFIG(shortcut)
        actionExit->setShortcut(QCoreApplication::translate("MainWindow", "Alt+F4", nullptr));
#endif // QT_CONFIG(shortcut)
        actionShowSpectrum->setText(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\351\242\221\350\260\261\345\233\276", nullptr));
        actionShowWaterfall->setText(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\347\200\221\345\270\203\345\233\276", nullptr));
        actionShowDataTable->setText(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\346\225\260\346\215\256\350\241\250\346\240\274", nullptr));
        actionShowQgisMap->setText(QCoreApplication::translate("MainWindow", "\345\234\260\347\220\206\344\277\241\346\201\257(&G)", nullptr));
#if QT_CONFIG(tooltip)
        actionShowQgisMap->setToolTip(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272QGIS\344\270\223\344\270\232GIS\345\234\260\345\233\276\347\273\204\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionShowQgisMap->setShortcut(QCoreApplication::translate("MainWindow", "F9", nullptr));
#endif // QT_CONFIG(shortcut)
        actionAbout->setText(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216", nullptr));
        actionAboutQt->setText(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216Qt", nullptr));
        actionShowSignalAnalysis->setText(QCoreApplication::translate("MainWindow", "\345\210\206\346\236\220(&A)", nullptr));
#if QT_CONFIG(tooltip)
        actionShowSignalAnalysis->setToolTip(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\344\277\241\345\217\267\345\210\206\346\236\220\347\225\214\351\235\242", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionShowSignalAnalysis->setShortcut(QCoreApplication::translate("MainWindow", "F8", nullptr));
#endif // QT_CONFIG(shortcut)
        actionShowComprehensiveView->setText(QCoreApplication::translate("MainWindow", "\347\273\274\345\220\210\351\230\205\350\247\210(&C)", nullptr));
#if QT_CONFIG(tooltip)
        actionShowComprehensiveView->setToolTip(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\347\273\274\345\220\210\351\230\205\350\247\210\347\225\214\351\235\242 - \346\225\260\346\215\256\346\237\245\350\257\242\344\270\216\345\261\225\347\244\272", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionShowComprehensiveView->setShortcut(QCoreApplication::translate("MainWindow", "F10", nullptr));
#endif // QT_CONFIG(shortcut)
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266(&F)", nullptr));
        menuView->setTitle(QCoreApplication::translate("MainWindow", "\350\247\206\345\233\276(&V)", nullptr));
        menuHelp->setTitle(QCoreApplication::translate("MainWindow", "\345\270\256\345\212\251(&H)", nullptr));
        toolBar->setWindowTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267\346\240\217", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
