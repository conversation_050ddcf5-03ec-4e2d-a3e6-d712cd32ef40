#include "qtlocationmapview.h"
#include <QDebug>
#include <QApplication>
#include <QDir>
#include <QStandardPaths>

// 常量定义
const double QtLocationMapView::DEFAULT_LATITUDE = 39.9042; // 北京天安门
const double QtLocationMapView::DEFAULT_LONGITUDE = 116.4074;
const double QtLocationMapView::DEFAULT_ZOOM = 10.0;
const double QtLocationMapView::MIN_ZOOM = 1.0;
const double QtLocationMapView::MAX_ZOOM = 20.0;

QtLocationMapView::QtLocationMapView(QWidget *parent)
    : QWidget(parent), m_mapWidget(nullptr), m_qmlEngine(nullptr), m_controlPanel(nullptr), m_controlLayout(nullptr), m_mapControlGroup(nullptr), m_mapTypeCombo(nullptr), m_zoomSlider(nullptr), m_zoomSpinBox(nullptr), m_zoomInBtn(nullptr), m_zoomOutBtn(nullptr), m_resetViewBtn(nullptr), m_coordinateGroup(nullptr), m_latitudeEdit(nullptr), m_longitudeEdit(nullptr), m_gotoCoordinateBtn(nullptr), m_markerGroup(nullptr), m_addMarkerBtn(nullptr), m_clearMarkersBtn(nullptr), m_markerListText(nullptr), m_currentCenter(DEFAULT_LATITUDE, DEFAULT_LONGITUDE), m_currentZoom(DEFAULT_ZOOM), m_currentMapType("osm"), m_nextMarkerId(1), m_serviceProvider(nullptr), m_mainSplitter(nullptr)
{
    qDebug() << "QtLocationMapView: 初始化开始";

    setupUI();
    initializeMap();
    connectSignals();

    qDebug() << "QtLocationMapView: 初始化完成";
}

QtLocationMapView::~QtLocationMapView()
{
    // 简化版本不需要清理
    qDebug() << "QtLocationMapView: 析构";
}

void QtLocationMapView::setupUI()
{
    // 创建主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);

    // 创建主布局
    QHBoxLayout *mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(m_mainSplitter);

    // 设置地图组件
    setupMapWidget();

    // 设置控制面板
    setupControlPanel();

    // 设置分割器比例
    m_mainSplitter->setSizes({800, 300});
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 0);
}

void QtLocationMapView::setupMapWidget()
{
    // 创建QML地图组件
    m_mapWidget = new QQuickWidget(this);
    m_mapWidget->setResizeMode(QQuickWidget::SizeRootObjectToView);
    m_mapWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 获取QML引擎
    m_qmlEngine = m_mapWidget->engine();

    // 添加到分割器
    m_mainSplitter->addWidget(m_mapWidget);

    qDebug() << "QtLocationMapView: 地图组件创建完成";
}

void QtLocationMapView::setupControlPanel()
{
    // 创建控制面板
    m_controlPanel = new QWidget(this);
    m_controlPanel->setFixedWidth(300);
    m_controlPanel->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);

    m_controlLayout = new QVBoxLayout(m_controlPanel);

    // 地图控制组
    m_mapControlGroup = new QGroupBox("地图控制", m_controlPanel);
    QVBoxLayout *mapControlLayout = new QVBoxLayout(m_mapControlGroup);

    // 地图类型选择
    mapControlLayout->addWidget(new QLabel("地图类型:"));
    m_mapTypeCombo = new QComboBox();
    m_mapTypeCombo->addItems({"OpenStreetMap", "卫星地图", "混合地图"});
    mapControlLayout->addWidget(m_mapTypeCombo);

    // 缩放控制
    mapControlLayout->addWidget(new QLabel("缩放级别:"));

    QHBoxLayout *zoomLayout = new QHBoxLayout();
    m_zoomSlider = new QSlider(Qt::Horizontal);
    m_zoomSlider->setRange(MIN_ZOOM * 10, MAX_ZOOM * 10);
    m_zoomSlider->setValue(DEFAULT_ZOOM * 10);
    m_zoomSpinBox = new QSpinBox();
    m_zoomSpinBox->setRange(MIN_ZOOM, MAX_ZOOM);
    m_zoomSpinBox->setValue(DEFAULT_ZOOM);

    zoomLayout->addWidget(m_zoomSlider);
    zoomLayout->addWidget(m_zoomSpinBox);
    mapControlLayout->addLayout(zoomLayout);

    // 缩放按钮
    QHBoxLayout *zoomBtnLayout = new QHBoxLayout();
    m_zoomInBtn = new QPushButton("放大");
    m_zoomOutBtn = new QPushButton("缩小");
    m_resetViewBtn = new QPushButton("重置视图");

    zoomBtnLayout->addWidget(m_zoomInBtn);
    zoomBtnLayout->addWidget(m_zoomOutBtn);
    zoomBtnLayout->addWidget(m_resetViewBtn);
    mapControlLayout->addLayout(zoomBtnLayout);

    m_controlLayout->addWidget(m_mapControlGroup);

    // 坐标控制组
    m_coordinateGroup = new QGroupBox("坐标控制", m_controlPanel);
    QVBoxLayout *coordLayout = new QVBoxLayout(m_coordinateGroup);

    coordLayout->addWidget(new QLabel("纬度:"));
    m_latitudeEdit = new QLineEdit(QString::number(DEFAULT_LATITUDE, 'f', 6));
    coordLayout->addWidget(m_latitudeEdit);

    coordLayout->addWidget(new QLabel("经度:"));
    m_longitudeEdit = new QLineEdit(QString::number(DEFAULT_LONGITUDE, 'f', 6));
    coordLayout->addWidget(m_longitudeEdit);

    m_gotoCoordinateBtn = new QPushButton("跳转到坐标");
    coordLayout->addWidget(m_gotoCoordinateBtn);

    m_controlLayout->addWidget(m_coordinateGroup);

    // 标记管理组
    m_markerGroup = new QGroupBox("标记管理", m_controlPanel);
    QVBoxLayout *markerLayout = new QVBoxLayout(m_markerGroup);

    QHBoxLayout *markerBtnLayout = new QHBoxLayout();
    m_addMarkerBtn = new QPushButton("添加标记");
    m_clearMarkersBtn = new QPushButton("清除标记");
    markerBtnLayout->addWidget(m_addMarkerBtn);
    markerBtnLayout->addWidget(m_clearMarkersBtn);
    markerLayout->addLayout(markerBtnLayout);

    m_markerListText = new QTextEdit();
    m_markerListText->setMaximumHeight(150);
    m_markerListText->setPlainText("标记列表:\n(点击地图添加标记)");
    markerLayout->addWidget(m_markerListText);

    m_controlLayout->addWidget(m_markerGroup);

    // 添加弹性空间
    m_controlLayout->addStretch();

    // 添加到分割器
    m_mainSplitter->addWidget(m_controlPanel);

    qDebug() << "QtLocationMapView: 控制面板创建完成";
}

void QtLocationMapView::connectSignals()
{
    // 缩放控制信号连接
    connect(m_zoomSlider, &QSlider::valueChanged, this, &QtLocationMapView::onZoomSliderChanged);
    connect(m_zoomInBtn, &QPushButton::clicked, this, &QtLocationMapView::zoomIn);
    connect(m_zoomOutBtn, &QPushButton::clicked, this, &QtLocationMapView::zoomOut);
    connect(m_resetViewBtn, &QPushButton::clicked, this, &QtLocationMapView::resetView);

    // 地图类型信号连接
    connect(m_mapTypeCombo, &QComboBox::currentTextChanged, this, &QtLocationMapView::onMapTypeComboChanged);

    // 坐标控制信号连接
    connect(m_gotoCoordinateBtn, &QPushButton::clicked, this, &QtLocationMapView::onCenterCoordinateChanged);

    // 标记管理信号连接
    connect(m_addMarkerBtn, &QPushButton::clicked, [this]()
            { addMarker(m_currentCenter.first, m_currentCenter.second, "新标记", "用户添加的标记"); });
    connect(m_clearMarkersBtn, &QPushButton::clicked, this, &QtLocationMapView::clearMarkers);

    qDebug() << "QtLocationMapView: 信号连接完成";
}

void QtLocationMapView::initializeMap()
{
    // 创建地图服务提供商
    loadMapProviders();

    // 设置地图属性
    setupMapProperties();

    qDebug() << "QtLocationMapView: 地图初始化完成";
}

void QtLocationMapView::loadMapProviders()
{
    // 简化版本：直接设置可用的地图类型
    m_availableMapTypes << "OpenStreetMap" << "卫星地图" << "混合地图";

    // 更新地图类型组合框
    if (m_mapTypeCombo)
    {
        m_mapTypeCombo->clear();
        m_mapTypeCombo->addItems(m_availableMapTypes);
        m_mapTypeCombo->setCurrentText("OpenStreetMap");
    }

    qDebug() << "QtLocationMapView: 地图提供商初始化完成（简化模式）";
}

void QtLocationMapView::setupMapProperties()
{
    // 设置QML引擎的导入路径
    m_qmlEngine->addImportPath("qrc:/");

    // 加载QML地图组件
    QString qmlPath = "qrc:/qml/MapComponent.qml";

    // 检查资源文件是否存在，如果不存在则使用本地文件
    QFile qmlFile(":/qml/MapComponent.qml");
    if (!qmlFile.exists())
    {
        qmlPath = QApplication::applicationDirPath() + "/resources/MapComponent.qml";
        QFile localFile(qmlPath);
        if (!localFile.exists())
        {
            qWarning() << "QtLocationMapView: QML地图文件不存在:" << qmlPath;
            // 使用简化版本
            createFallbackMap();
            return;
        }
        // 使用本地文件
        m_mapWidget->setSource(QUrl::fromLocalFile(qmlPath));
    }
    else
    {
        // 使用资源文件
        m_mapWidget->setSource(QUrl(qmlPath));
    }

    // 连接QML信号（简化版本暂时禁用以避免类型转换问题）
    /*QObject *rootObject = qobject_cast<QObject*>(m_mapWidget->rootObject());
    if (rootObject)
    {
        // 连接地图信号
        connect(rootObject, SIGNAL(mapClicked(real, real)),
                this, SIGNAL(mapClicked(double, double)));
        connect(rootObject, SIGNAL(mapDoubleClicked(real, real)),
                this, SIGNAL(mapDoubleClicked(double, double)));
        connect(rootObject, SIGNAL(centerChanged(real, real)),
                this, SLOT(onQmlCenterChanged(double, double)));
        connect(rootObject, SIGNAL(zoomChanged(real)),
                this, SLOT(onQmlZoomChanged(double)));

        qDebug() << "QtLocationMapView: QML信号连接完成";
    }
    else
    {
        qWarning() << "QtLocationMapView: 无法获取QML根对象";
        createFallbackMap();
    }*/

    // 简化版本：直接创建占位符地图
    qDebug() << "QtLocationMapView: 使用简化模式，跳过QML信号连接";
    createFallbackMap();

    qDebug() << "QtLocationMapView: QML地图组件加载完成";
}

void QtLocationMapView::createFallbackMap()
{
    // 创建简化的地图显示
    QString fallbackQml = R"(
        import QtQuick 2.12

        Rectangle {
            width: 800
            height: 600
            color: "#e8f4f8"

            // 网格背景
            Canvas {
                anchors.fill: parent
                onPaint: {
                    var ctx = getContext("2d")
                    ctx.strokeStyle = "#cccccc"
                    ctx.lineWidth = 1

                    // 绘制网格
                    for (var x = 0; x < width; x += 50) {
                        ctx.beginPath()
                        ctx.moveTo(x, 0)
                        ctx.lineTo(x, height)
                        ctx.stroke()
                    }

                    for (var y = 0; y < height; y += 50) {
                        ctx.beginPath()
                        ctx.moveTo(0, y)
                        ctx.lineTo(width, y)
                        ctx.stroke()
                    }
                }
            }

            Text {
                anchors.centerIn: parent
                text: "Qt Location 地图组件\n(简化模式)\n\n点击控制面板进行操作"
                font.pixelSize: 20
                color: "#333333"
                horizontalAlignment: Text.AlignHCenter
            }

            Rectangle {
                anchors.fill: parent
                color: "transparent"
                border.color: "#666666"
                border.width: 2
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    console.log("地图被点击")
                }
            }
        }
    )";

    m_mapWidget->setSource(QUrl("data:text/plain;charset=utf-8," + fallbackQml.toUtf8().toPercentEncoding()));
}

// 地图控制方法实现
void QtLocationMapView::setCenter(double latitude, double longitude)
{
    m_currentCenter = qMakePair(latitude, longitude);
    m_latitudeEdit->setText(QString::number(latitude, 'f', 6));
    m_longitudeEdit->setText(QString::number(longitude, 'f', 6));

    // 这里应该调用QML地图的setCenter方法
    // 由于我们使用简化版本，暂时只更新UI

    emit centerChanged(latitude, longitude);
    qDebug() << "QtLocationMapView: 地图中心设置为" << latitude << longitude;
}

void QtLocationMapView::setZoomLevel(double zoom)
{
    zoom = qBound(MIN_ZOOM, zoom, MAX_ZOOM);
    m_currentZoom = zoom;

    m_zoomSlider->setValue(zoom * 10);
    m_zoomSpinBox->setValue(zoom);

    // 这里应该调用QML地图的setZoomLevel方法

    emit zoomChanged(zoom);
    qDebug() << "QtLocationMapView: 缩放级别设置为" << zoom;
}

void QtLocationMapView::setMapType(const QString &mapType)
{
    m_currentMapType = mapType;

    // 更新组合框选择
    int index = m_mapTypeCombo->findText(mapType);
    if (index >= 0)
    {
        m_mapTypeCombo->setCurrentIndex(index);
    }

    // 这里应该调用QML地图的setMapType方法

    emit mapTypeChanged(mapType);
    qDebug() << "QtLocationMapView: 地图类型设置为" << mapType;
}

QPair<double, double> QtLocationMapView::getCenter() const
{
    return m_currentCenter;
}

double QtLocationMapView::getZoomLevel() const
{
    return m_currentZoom;
}

QString QtLocationMapView::getMapType() const
{
    return m_currentMapType;
}

// 标记管理方法
void QtLocationMapView::addMarker(double latitude, double longitude, const QString &title, const QString &description)
{
    int markerId = m_nextMarkerId++;
    m_markerIds.append(markerId);

    // 更新标记列表显示
    QString markerInfo = QString("标记 %1: %2, %3\n标题: %4\n描述: %5\n")
                             .arg(markerId)
                             .arg(latitude, 0, 'f', 6)
                             .arg(longitude, 0, 'f', 6)
                             .arg(title)
                             .arg(description);

    m_markerListText->append(markerInfo);

    // 这里应该在QML地图上添加标记

    qDebug() << "QtLocationMapView: 添加标记" << markerId << "在" << latitude << longitude;
}

void QtLocationMapView::removeMarker(int markerId)
{
    if (m_markerIds.contains(markerId))
    {
        m_markerIds.removeAll(markerId);

        // 这里应该从QML地图上移除标记

        qDebug() << "QtLocationMapView: 移除标记" << markerId;
    }
}

void QtLocationMapView::clearMarkers()
{
    m_markerIds.clear();
    m_markerListText->setPlainText("标记列表:\n(点击地图添加标记)");

    // 这里应该清除QML地图上的所有标记

    qDebug() << "QtLocationMapView: 清除所有标记";
}

// 几何图形方法
void QtLocationMapView::addCircle(double latitude, double longitude, double radius, const QColor &color)
{
    // 这里应该在QML地图上添加圆形
    qDebug() << "QtLocationMapView: 添加圆形在" << latitude << longitude << "半径" << radius;
}

void QtLocationMapView::addPolygon(const QList<QPair<double, double>> &coordinates, const QColor &color)
{
    // 这里应该在QML地图上添加多边形
    qDebug() << "QtLocationMapView: 添加多边形，顶点数:" << coordinates.size();
}

void QtLocationMapView::addPolyline(const QList<QPair<double, double>> &coordinates, const QColor &color)
{
    // 这里应该在QML地图上添加折线
    qDebug() << "QtLocationMapView: 添加折线，点数:" << coordinates.size();
}

// 公共槽函数
void QtLocationMapView::zoomIn()
{
    setZoomLevel(m_currentZoom + 1.0);
}

void QtLocationMapView::zoomOut()
{
    setZoomLevel(m_currentZoom - 1.0);
}

void QtLocationMapView::resetView()
{
    setCenter(DEFAULT_LATITUDE, DEFAULT_LONGITUDE);
    setZoomLevel(DEFAULT_ZOOM);
}

void QtLocationMapView::fitToMarkers()
{
    if (m_markerIds.isEmpty())
    {
        resetView();
        return;
    }

    // 这里应该计算所有标记的边界并调整视图
    qDebug() << "QtLocationMapView: 适应标记视图";
}

void QtLocationMapView::setOpenStreetMapType()
{
    setMapType("OpenStreetMap");
}

void QtLocationMapView::setSatelliteMapType()
{
    setMapType("卫星地图");
}

void QtLocationMapView::setHybridMapType()
{
    setMapType("混合地图");
}

// 私有槽函数
void QtLocationMapView::onMapReady()
{
    qDebug() << "QtLocationMapView: 地图准备就绪";
}

void QtLocationMapView::onZoomSliderChanged(int value)
{
    double zoom = value / 10.0;
    if (qAbs(zoom - m_currentZoom) > 0.01)
    {
        setZoomLevel(zoom);
    }
}

void QtLocationMapView::onMapTypeComboChanged(const QString &text)
{
    if (text != m_currentMapType)
    {
        setMapType(text);
    }
}

void QtLocationMapView::onCenterCoordinateChanged()
{
    bool latOk, lngOk;
    double lat = m_latitudeEdit->text().toDouble(&latOk);
    double lng = m_longitudeEdit->text().toDouble(&lngOk);

    if (latOk && lngOk && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180)
    {
        setCenter(lat, lng);
    }
    else
    {
        // 恢复原来的值
        m_latitudeEdit->setText(QString::number(m_currentCenter.first, 'f', 6));
        m_longitudeEdit->setText(QString::number(m_currentCenter.second, 'f', 6));
        qWarning() << "QtLocationMapView: 无效的坐标输入";
    }
}

void QtLocationMapView::onQmlCenterChanged(double latitude, double longitude)
{
    // 更新内部状态
    m_currentCenter = qMakePair(latitude, longitude);

    // 更新UI控件
    m_latitudeEdit->setText(QString::number(latitude, 'f', 6));
    m_longitudeEdit->setText(QString::number(longitude, 'f', 6));

    // 发射信号
    emit centerChanged(latitude, longitude);

    qDebug() << "QtLocationMapView: QML地图中心变化:" << latitude << longitude;
}

void QtLocationMapView::onQmlZoomChanged(double zoom)
{
    // 更新内部状态
    m_currentZoom = zoom;

    // 更新UI控件
    m_zoomSlider->setValue(zoom * 10);
    m_zoomSpinBox->setValue(zoom);

    // 发射信号
    emit zoomChanged(zoom);

    qDebug() << "QtLocationMapView: QML地图缩放变化:" << zoom;
}
