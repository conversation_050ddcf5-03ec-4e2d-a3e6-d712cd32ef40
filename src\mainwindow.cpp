#include "mainwindow.h"
#include "ui/ui_mainwindow.h"
#include "signalanalysiswidget.h"
// #include "geoinfowidget.h"  // 暂时禁用
// #include "qtgeoinfowidget.h"  // 暂时禁用
#include "qgismapwidget.h"
#include "comprehensiveviewwidget.h"
#include <QApplication>
#include <QCloseEvent>
#include <QStackedWidget>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      ui(new Ui::MainWindow),
      m_stackedWidget(nullptr),
      m_signalAnalysis(nullptr),
      // m_geoInfo(nullptr),  // 已移除
      // m_qtGeoInfo(nullptr),  // 已移除
      m_qgisMap(nullptr),
      m_comprehensiveView(nullptr)
{
    ui->setupUi(this);

    // 清除UI文件中可能设置的中央组件
    if (centralWidget())
    {
        centralWidget()->setParent(nullptr);
        setCentralWidget(nullptr);
    }

    initializeUI();
    connectSignals();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::initializeUI()
{
    // 设置窗口标题
    setWindowTitle("LiteAPPStar - 轻量级信号分析工具");

    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 设置最小窗口大小
    setMinimumSize(1200, 800);

    // 创建堆叠窗口管理器
    m_stackedWidget = new QStackedWidget(this);
    setCentralWidget(m_stackedWidget);

    // 创建地理信息组件（原版）- 已移除
    // m_geoInfo = new GeoInfoWidget(m_stackedWidget);
    // m_stackedWidget->addWidget(m_geoInfo);

    // 创建Qt Location地理信息组件 - 已移除
    // m_qtGeoInfo = new QtGeoInfoWidget(m_stackedWidget);
    // m_stackedWidget->addWidget(m_qtGeoInfo);

    // 创建QGIS地图组件
    m_qgisMap = new QgisMapWidget(m_stackedWidget);
    m_stackedWidget->addWidget(m_qgisMap);

    // 创建信号分析组件
    m_signalAnalysis = new SignalAnalysisWidget(m_stackedWidget);
    m_stackedWidget->addWidget(m_signalAnalysis);

    // 创建综合阅览组件
    m_comprehensiveView = new ComprehensiveViewWidget(m_stackedWidget);
    m_stackedWidget->addWidget(m_comprehensiveView);

    // 默认显示QGIS地图界面（现在集成了TileMapView瓦片地图）
    m_stackedWidget->setCurrentWidget(m_qgisMap);

    // 设置状态栏
    statusBar()->showMessage("QGIS专业地图界面已就绪（集成瓦片地图）", 2000);
}

void MainWindow::connectSignals()
{
    // 连接分析菜单信号
    connect(ui->actionShowSignalAnalysis, &QAction::triggered, this, &MainWindow::showSignalAnalysis);

    // 连接地理信息菜单信号
    connect(ui->actionShowQgisMap, &QAction::triggered, this, &MainWindow::showQgisMap);

    // 连接综合阅览菜单信号
    connect(ui->actionShowComprehensiveView, &QAction::triggered, this, &MainWindow::showComprehensiveView);

    // 连接地理信息组件信号 - 已移除
    /*if (m_geoInfo)
    {
        connect(m_geoInfo, &GeoInfoWidget::mouseCoordinateChanged,
                this, &MainWindow::onMouseCoordinateChanged);
    }*/

    // 连接Qt Location地理信息组件信号 - 已移除
    /*if (m_qtGeoInfo)
    {
        connect(m_qtGeoInfo, &QtGeoInfoWidget::coordinateChanged,
                this, &MainWindow::onMouseCoordinateChanged);
        connect(m_qtGeoInfo, &QtGeoInfoWidget::statusChanged,
                [this](const QString &status)
                { statusBar()->showMessage(status, 3000); });
    }*/

    // 连接QGIS地图组件信号
    if (m_qgisMap)
    {
        connect(m_qgisMap, &QgisMapWidget::mapClicked,
                this, &MainWindow::onMouseCoordinateChanged);
        connect(m_qgisMap, &QgisMapWidget::layerAdded,
                [this](const QString &layerId, const QString &name)
                { statusBar()->showMessage(QString("图层已添加: %1").arg(name), 3000); });
        connect(m_qgisMap, &QgisMapWidget::layerRemoved,
                [this](const QString &layerId)
                { statusBar()->showMessage(QString("图层已移除: %1").arg(layerId), 3000); });
    }

    // 连接其他菜单信号
    connect(ui->actionAbout, &QAction::triggered, this, &MainWindow::about);
    connect(ui->actionAboutQt, &QAction::triggered, this, &MainWindow::aboutQt);
}

// 移除了分析状态和数据更新的槽函数

void MainWindow::closeEvent(QCloseEvent *event)
{
    // 直接接受关闭事件，无需停止分析
    event->accept();
}

// 文件菜单槽函数
void MainWindow::newFile()
{
    // 新建文件功能（暂时为空）
}

void MainWindow::open()
{
    // 打开文件功能（暂时为空）
}

void MainWindow::save()
{
    // 保存文件功能（暂时为空）
}

void MainWindow::saveAs()
{
    // 另存为功能（暂时为空）
}

void MainWindow::exitApp()
{
    close();
}

// 分析菜单槽函数

void MainWindow::showSignalAnalysis()
{
    // 切换到信号分析界面
    if (m_stackedWidget && m_signalAnalysis)
    {
        m_stackedWidget->setCurrentWidget(m_signalAnalysis);
        statusBar()->showMessage("已切换到信号分析界面", 2000);
    }
}

// 地理信息菜单槽函数

void MainWindow::showQgisMap()
{
    // 切换到QGIS地图界面
    if (m_stackedWidget && m_qgisMap)
    {
        m_stackedWidget->setCurrentWidget(m_qgisMap);
        statusBar()->showMessage("已切换到QGIS专业地图视图", 2000);
    }
}

void MainWindow::showComprehensiveView()
{
    // 切换到综合阅览界面
    if (m_stackedWidget && m_comprehensiveView)
    {
        m_stackedWidget->setCurrentWidget(m_comprehensiveView);
        statusBar()->showMessage("已切换到综合阅览界面", 2000);
    }
}

void MainWindow::onMouseCoordinateChanged(double lat, double lng)
{
    // 在状态栏显示鼠标坐标
    QString coordText = QString("坐标: %1°N, %2°E").arg(lat, 0, 'f', 6).arg(lng, 0, 'f', 6);
    statusBar()->showMessage(coordText);
}

// 视图菜单槽函数
void MainWindow::toggleSpectrum(bool visible)
{
    // 切换频谱图显示（暂时为空）
}

void MainWindow::toggleWaterfall(bool visible)
{
    // 切换瀑布图显示（暂时为空）
}

void MainWindow::toggleDataTable(bool visible)
{
    // 切换数据表格显示（暂时为空）
}

// 帮助菜单槽函数
void MainWindow::about()
{
    // 关于对话框（暂时为空）
}

void MainWindow::aboutQt()
{
    QApplication::aboutQt();
}
