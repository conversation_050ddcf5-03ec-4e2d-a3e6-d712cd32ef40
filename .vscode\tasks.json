{"version": "2.0.0", "tasks": [{"label": "CMake Configure (VS2022)", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-G", "Visual Studio 17 2022", "-A", "x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Configure CMake project for Visual Studio 2022"}, {"label": "CMake Configure (MinGW)", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-G", "MinGW Makefiles", "-DCMAKE_BUILD_TYPE=Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Configure CMake project for MinGW"}, {"label": "CMake Build (VS2022)", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"], "dependsOn": "CMake Configure (VS2022)", "detail": "Build the project using CMake with Visual Studio 2022"}, {"label": "CMake Build (MinGW)", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "dependsOn": "CMake Configure (MinGW)", "detail": "Build the project using CMake with MinGW"}, {"label": "qmake Generate", "type": "shell", "command": "qmake", "args": ["LiteAPPStar.pro", "-o", "<PERSON><PERSON><PERSON>"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Generate Makefile using qmake"}, {"label": "qmake Build", "type": "shell", "command": "make", "args": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "dependsOn": "qmake Generate", "detail": "Build the project using qmake and make", "windows": {"command": "mingw32-make"}}, {"label": "Clean CMake Build", "type": "shell", "command": "cmake", "args": ["--build", "build", "--target", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Clean CMake build files"}, {"label": "Clean qmake Build", "type": "shell", "command": "make", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Clean qmake build files", "windows": {"command": "mingw32-make"}}, {"label": "Run Application", "type": "shell", "command": "${workspaceFolder}/build/bin/Debug/LiteAPPStar.exe", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "dependsOn": "CMake Build (VS2022)", "detail": "Run the built application", "windows": {"command": "${workspaceFolder}/build/bin/Debug/LiteAPPStar.exe"}}]}