/* LiteAPPStar 深色主题样式表 */

/* 主窗口样式 */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #3c3c3c;
    border-bottom: 1px solid #555555;
    padding: 2px;
    color: #ffffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #4a4a4a;
}

QMenuBar::item:pressed {
    background-color: #555555;
}

/* 菜单样式 */
QMenu {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 2px;
    color: #ffffff;
}

QMenu::item {
    padding: 6px 20px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #4a4a4a;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 2px 0px;
}

/* 工具栏样式 */
QToolBar {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    padding: 2px;
    spacing: 2px;
}

QToolBar::handle {
    background-color: #555555;
    width: 8px;
    margin: 2px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px;
    margin: 1px;
    color: #ffffff;
}

QToolButton:hover {
    background-color: #4a4a4a;
    border-color: #666666;
}

QToolButton:pressed {
    background-color: #555555;
    border-color: #777777;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #3c3c3c;
    border-top: 1px solid #555555;
    padding: 2px;
    color: #ffffff;
}

/* 文本编辑器样式 */
QTextEdit {
    background-color: #1e1e1e;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 10pt;
    line-height: 1.4;
    color: #ffffff;
    selection-background-color: #4a90e2;
}

QTextEdit:focus {
    border-color: #4a90e2;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

QSplitter::handle:pressed {
    background-color: #4a90e2;
}

/* 按钮样式 */
QPushButton {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 6px 12px;
    font-size: 9pt;
    color: #ffffff;
}

QPushButton:hover {
    background-color: #4a4a4a;
    border-color: #666666;
}

QPushButton:pressed {
    background-color: #555555;
    border-color: #777777;
}

QPushButton:default {
    border-color: #4a90e2;
    font-weight: bold;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #2b2b2b;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #2b2b2b;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}
