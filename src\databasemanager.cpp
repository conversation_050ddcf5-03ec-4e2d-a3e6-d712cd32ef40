#include "databasemanager.h"
#include <QSqlDriver>
#include <QSqlField>
#include <QCoreApplication>
#include <QElapsedTimer>
#include <QRandomGenerator>
#include <QUuid>

// 静态成员初始化
DatabaseManager *DatabaseManager::s_instance = nullptr;
QMutex DatabaseManager::s_mutex;

//==============================================================================
// QueryResult 实现
//==============================================================================

class QueryResult::QueryResultPrivate
{
public:
    QSqlQuery query;
    QString errorString;
    bool valid;

    QueryResultPrivate() : valid(false) {}
    QueryResultPrivate(const QSqlQuery &q) : query(q)
    {
        // 检查查询是否执行成功（不是isValid，而是检查lastError）
        QSqlError error = q.lastError();
        valid = !error.isValid(); // 如果没有错误，则查询有效

        if (!valid)
        {
            errorString = error.text();
            qWarning() << "QueryResult: 查询有错误:" << errorString;
        }
        else
        {
            qDebug() << "QueryResult: 查询执行成功，准备读取结果";
        }
    }
};

QueryResult::QueryResult() : d(std::make_unique<QueryResultPrivate>()) {}

QueryResult::QueryResult(const QSqlQuery &query)
    : d(std::make_unique<QueryResultPrivate>(query)) {}

QueryResult::~QueryResult() = default;

// 移动构造函数
QueryResult::QueryResult(QueryResult &&other) noexcept
    : d(std::move(other.d))
{
}

// 移动赋值操作符
QueryResult &QueryResult::operator=(QueryResult &&other) noexcept
{
    if (this != &other)
    {
        d = std::move(other.d);
    }
    return *this;
}

bool QueryResult::isValid() const { return d->valid; }
bool QueryResult::next() { return d->valid ? d->query.next() : false; }
bool QueryResult::previous() { return d->valid ? d->query.previous() : false; }
bool QueryResult::first() { return d->valid ? d->query.first() : false; }
bool QueryResult::last() { return d->valid ? d->query.last() : false; }
void QueryResult::beforeFirst()
{
    if (d->valid)
        d->query.seek(QSql::BeforeFirstRow);
}
void QueryResult::afterLast()
{
    if (d->valid)
        d->query.seek(QSql::AfterLastRow);
}

QVariant QueryResult::value(int index) const
{
    return d->valid ? d->query.value(index) : QVariant();
}

QVariant QueryResult::value(const QString &name) const
{
    return d->valid ? d->query.value(name) : QVariant();
}

QVariantMap QueryResult::record() const
{
    QVariantMap result;
    if (!d->valid)
        return result;

    QSqlRecord rec = d->query.record();
    for (int i = 0; i < rec.count(); ++i)
    {
        result[rec.fieldName(i)] = rec.value(i);
    }
    return result;
}

QVariantList QueryResult::recordList() const
{
    QVariantList result;
    if (!d->valid)
        return result;

    // 保存当前位置
    int currentPos = d->query.at();

    // 移动到开始
    d->query.first();
    d->query.previous(); // 移动到第一条记录之前

    while (d->query.next())
    {
        QVariantMap record;
        QSqlRecord rec = d->query.record();
        for (int i = 0; i < rec.count(); ++i)
        {
            record[rec.fieldName(i)] = rec.value(i);
        }
        result.append(record);
    }

    // 恢复位置
    if (currentPos >= 0)
    {
        d->query.seek(currentPos);
    }

    return result;
}

int QueryResult::size() const
{
    return d->valid ? d->query.size() : -1;
}

int QueryResult::numRowsAffected() const
{
    return d->valid ? d->query.numRowsAffected() : -1;
}

QString QueryResult::lastError() const
{
    return d->errorString;
}

bool QueryResult::hasError() const
{
    return !d->errorString.isEmpty();
}

QStringList QueryResult::fieldNames() const
{
    QStringList names;
    if (!d->valid)
        return names;

    QSqlRecord rec = d->query.record();
    for (int i = 0; i < rec.count(); ++i)
    {
        names << rec.fieldName(i);
    }
    return names;
}

int QueryResult::fieldIndex(const QString &name) const
{
    return d->valid ? d->query.record().indexOf(name) : -1;
}

//==============================================================================
// DatabaseConnection 实现
//==============================================================================

class DatabaseConnection::DatabaseConnectionPrivate
{
public:
    QString connectionName;
    DatabaseConfig config;
    QSqlDatabase database;
    QString lastErrorString;
    QDateTime lastUsedTime;
    bool inTransaction;

    DatabaseConnectionPrivate(const QString &name, const DatabaseConfig &cfg)
        : connectionName(name), config(cfg), inTransaction(false)
    {
        lastUsedTime = QDateTime::currentDateTime();
    }
};

DatabaseConnection::DatabaseConnection(const QString &connectionName, const DatabaseConfig &config)
    : d(std::make_unique<DatabaseConnectionPrivate>(connectionName, config))
{
    d->database = QSqlDatabase::addDatabase(config.type, connectionName);
    d->database.setHostName(config.host);
    d->database.setPort(config.port);
    d->database.setDatabaseName(config.databaseName);
    d->database.setUserName(config.username);
    d->database.setPassword(config.password);
}

DatabaseConnection::~DatabaseConnection()
{
    close();
    if (QSqlDatabase::contains(d->connectionName))
    {
        QSqlDatabase::removeDatabase(d->connectionName);
    }
}

bool DatabaseConnection::open()
{
    if (d->database.isOpen())
    {
        return true;
    }

    // 设置连接选项 - 使用较短的超时时间避免阻塞
    QString options = "MYSQL_OPT_RECONNECT=1;";
    options += "MYSQL_OPT_CONNECT_TIMEOUT=3;"; // 减少到3秒
    options += "MYSQL_OPT_READ_TIMEOUT=5;";    // 读取超时5秒
    options += "MYSQL_OPT_WRITE_TIMEOUT=5;";   // 写入超时5秒
    options += "CLIENT_SSL=0;";                // 禁用SSL
    d->database.setConnectOptions(options);

    bool success = d->database.open();
    if (!success)
    {
        d->lastErrorString = d->database.lastError().text();
        qWarning() << "DatabaseConnection: 连接失败:" << d->lastErrorString;
    }
    else
    {
        d->lastErrorString.clear();
        updateLastUsed();
        qDebug() << "DatabaseConnection: 连接成功:" << d->connectionName;
    }

    return success;
}

void DatabaseConnection::close()
{
    if (d->inTransaction)
    {
        rollbackTransaction();
    }

    if (d->database.isOpen())
    {
        d->database.close();
        qDebug() << "DatabaseConnection: 连接已关闭:" << d->connectionName;
    }
}

bool DatabaseConnection::isOpen() const
{
    return d->database.isOpen();
}

bool DatabaseConnection::isValid() const
{
    return d->database.isValid() && d->database.isOpen();
}

QueryResult DatabaseConnection::executeQuery(const QString &sql, const QVariantList &params)
{
    if (!isValid())
    {
        return QueryResult();
    }

    updateLastUsed();

    qDebug() << "DatabaseConnection::executeQuery 开始";
    qDebug() << "  - SQL:" << sql;
    qDebug() << "  - 参数:" << params;

    QSqlQuery query(d->database);

    qDebug() << "  - 准备SQL语句";
    bool prepared = query.prepare(sql);
    qDebug() << "  - prepare() 返回:" << prepared;

    if (!prepared)
    {
        d->lastErrorString = query.lastError().text();
        qWarning() << "DatabaseConnection: SQL准备失败:" << d->lastErrorString;
        return QueryResult(query);
    }

    // 绑定参数
    qDebug() << "  - 绑定参数，参数数量:" << params.size();
    for (int i = 0; i < params.size(); ++i)
    {
        query.bindValue(i, params.at(i));
        qDebug() << "    - 参数" << i << ":" << params.at(i);
    }

    QElapsedTimer timer;
    timer.start();

    qDebug() << "  - 执行查询";
    bool success = query.exec();
    int executionTime = timer.elapsed();

    qDebug() << "  - query.exec() 返回:" << success;
    qDebug() << "  - 执行耗时:" << executionTime << "ms";

    if (!success)
    {
        d->lastErrorString = query.lastError().text();
        qWarning() << "DatabaseConnection: 查询执行失败:" << d->lastErrorString;
        qWarning() << "SQL:" << sql;
        qWarning() << "参数:" << params;
        qWarning() << "错误类型:" << query.lastError().type();
        qWarning() << "数据库错误:" << query.lastError().databaseText();
        qWarning() << "驱动错误:" << query.lastError().driverText();
    }
    else
    {
        d->lastErrorString.clear();
        qDebug() << "DatabaseConnection: 查询执行成功，耗时:" << executionTime << "ms";

        // 对于SELECT查询，检查结果集状态
        if (sql.trimmed().toUpper().startsWith("SELECT"))
        {
            qDebug() << "  - 这是SELECT查询，检查结果集";
            qDebug() << "  - query.isActive():" << query.isActive();
            qDebug() << "  - query.isSelect():" << query.isSelect();
            qDebug() << "  - query.size():" << query.size();
            qDebug() << "  - query.at():" << query.at();
        }
    }

    qDebug() << "  - 创建QueryResult并返回";
    return QueryResult(query);
}

bool DatabaseConnection::executeNonQuery(const QString &sql, const QVariantList &params)
{
    QueryResult result = executeQuery(sql, params);
    return result.isValid() && !result.hasError();
}

bool DatabaseConnection::beginTransaction()
{
    if (!isValid())
    {
        d->lastErrorString = "数据库连接无效";
        return false;
    }

    if (d->inTransaction)
    {
        d->lastErrorString = "事务已经开始";
        return false;
    }

    bool success = d->database.transaction();
    if (success)
    {
        d->inTransaction = true;
        d->lastErrorString.clear();
        qDebug() << "DatabaseConnection: 事务开始:" << d->connectionName;
    }
    else
    {
        d->lastErrorString = d->database.lastError().text();
        qWarning() << "DatabaseConnection: 事务开始失败:" << d->lastErrorString;
    }

    return success;
}

bool DatabaseConnection::commitTransaction()
{
    if (!d->inTransaction)
    {
        d->lastErrorString = "没有活动的事务";
        return false;
    }

    bool success = d->database.commit();
    if (success)
    {
        d->inTransaction = false;
        d->lastErrorString.clear();
        qDebug() << "DatabaseConnection: 事务提交成功:" << d->connectionName;
    }
    else
    {
        d->lastErrorString = d->database.lastError().text();
        qWarning() << "DatabaseConnection: 事务提交失败:" << d->lastErrorString;
    }

    return success;
}

bool DatabaseConnection::rollbackTransaction()
{
    if (!d->inTransaction)
    {
        d->lastErrorString = "没有活动的事务";
        return false;
    }

    bool success = d->database.rollback();
    d->inTransaction = false; // 无论成功与否都重置状态

    if (success)
    {
        d->lastErrorString.clear();
        qDebug() << "DatabaseConnection: 事务回滚成功:" << d->connectionName;
    }
    else
    {
        d->lastErrorString = d->database.lastError().text();
        qWarning() << "DatabaseConnection: 事务回滚失败:" << d->lastErrorString;
    }

    return success;
}

bool DatabaseConnection::isInTransaction() const
{
    return d->inTransaction;
}

QString DatabaseConnection::connectionName() const
{
    return d->connectionName;
}

QString DatabaseConnection::lastError() const
{
    return d->lastErrorString;
}

QDateTime DatabaseConnection::lastUsed() const
{
    return d->lastUsedTime;
}

void DatabaseConnection::updateLastUsed()
{
    d->lastUsedTime = QDateTime::currentDateTime();
}

bool DatabaseConnection::testConnection()
{
    if (!isValid())
    {
        return false;
    }

    QSqlQuery query("SELECT 1", d->database);
    return query.exec();
}

//==============================================================================
// ConnectionPool 实现
//==============================================================================

class ConnectionPool::ConnectionPoolPrivate
{
public:
    EnvironmentConfig config;
    QQueue<std::shared_ptr<DatabaseConnection>> idleConnections;
    QList<std::shared_ptr<DatabaseConnection>> activeConnections;
    QMutex poolMutex;
    QTimer *cleanupTimer;
    QTimer *healthCheckTimer;
    int connectionCounter;
    QString lastErrorString;

    ConnectionPoolPrivate(const EnvironmentConfig &cfg)
        : config(cfg), connectionCounter(0) {}
};

ConnectionPool::ConnectionPool(const EnvironmentConfig &config, QObject *parent)
    : QObject(parent), d(std::make_unique<ConnectionPoolPrivate>(config))
{
    d->cleanupTimer = new QTimer(this);
    d->cleanupTimer->setInterval(60000); // 1分钟清理一次
    connect(d->cleanupTimer, &QTimer::timeout, this, &ConnectionPool::cleanupIdleConnections);

    d->healthCheckTimer = new QTimer(this);
    d->healthCheckTimer->setInterval(300000); // 5分钟检查一次健康状态
    connect(d->healthCheckTimer, &QTimer::timeout, this, &ConnectionPool::checkConnectionHealth);
}

ConnectionPool::~ConnectionPool()
{
    cleanup();
}

void ConnectionPool::initialize()
{
    QMutexLocker locker(&d->poolMutex);

    qDebug() << "ConnectionPool: 初始化连接池，最小连接数:" << d->config.connectionPool.minConnections;

    // 尝试创建最小连接数，但不强制要求全部成功
    int successfulConnections = 0;
    int maxAttempts = d->config.connectionPool.minConnections;

    for (int i = 0; i < maxAttempts; ++i)
    {
        qDebug() << "ConnectionPool: 尝试创建连接" << (i + 1) << "/" << maxAttempts;

        if (createConnectionWithTimeout())
        {
            successfulConnections++;
            qDebug() << "ConnectionPool: 连接创建成功，当前成功连接数:" << successfulConnections;
        }
        else
        {
            qWarning() << "ConnectionPool: 连接创建失败，跳过此连接";
            // 不要因为单个连接失败就停止整个初始化过程
        }

        // 如果已经有至少一个连接成功，可以继续
        if (successfulConnections > 0 && i >= 0)
        {
            qDebug() << "ConnectionPool: 至少有一个连接成功，继续初始化";
        }
    }

    // 启动定时器
    d->cleanupTimer->start();
    d->healthCheckTimer->start();

    qDebug() << "ConnectionPool: 连接池初始化完成，成功连接数:" << successfulConnections;
    //          << "，总连接数:" << totalConnections();

    if (successfulConnections == 0)
    {
        qWarning() << "ConnectionPool: 警告 - 没有成功创建任何数据库连接！";
        emit poolError("连接池初始化失败：无法创建任何数据库连接");
    }
}

void ConnectionPool::cleanup()
{
    QMutexLocker locker(&d->poolMutex);

    qDebug() << "ConnectionPool: 清理连接池";

    // 停止定时器
    d->cleanupTimer->stop();
    d->healthCheckTimer->stop();

    // 清理所有连接
    d->idleConnections.clear();
    d->activeConnections.clear();

    qDebug() << "ConnectionPool: 连接池清理完成";
}

std::shared_ptr<DatabaseConnection> ConnectionPool::getConnection()
{
    QMutexLocker locker(&d->poolMutex);

    // 尝试从空闲连接中获取
    if (!d->idleConnections.isEmpty())
    {
        auto connection = d->idleConnections.dequeue();

        // 测试连接是否有效
        if (connection->testConnection())
        {
            d->activeConnections.append(connection);
            qDebug() << "ConnectionPool: 从池中获取连接:" << connection->connectionName();
            return connection;
        }
        else
        {
            qWarning() << "ConnectionPool: 连接无效，移除:" << connection->connectionName();
            // 连接无效，继续尝试创建新连接
        }
    }

    // 如果没有空闲连接或连接无效，创建新连接
    if (totalConnections() < d->config.connectionPool.maxConnections)
    {
        createConnection();
        if (!d->idleConnections.isEmpty())
        {
            auto connection = d->idleConnections.dequeue();
            d->activeConnections.append(connection);
            qDebug() << "ConnectionPool: 创建新连接:" << connection->connectionName();
            return connection;
        }
    }

    // 连接池已满，返回空指针
    qWarning() << "ConnectionPool: 连接池已满，无法获取连接";
    emit poolError("连接池已满，无法获取连接");
    return nullptr;
}

void ConnectionPool::returnConnection(std::shared_ptr<DatabaseConnection> connection)
{
    if (!connection)
    {
        return;
    }

    QMutexLocker locker(&d->poolMutex);

    // 从活动连接中移除
    d->activeConnections.removeAll(connection);

    // 如果连接仍然有效，放回空闲池
    if (connection->isValid() && !connection->isInTransaction())
    {
        d->idleConnections.enqueue(connection);
        qDebug() << "ConnectionPool: 归还连接到池:" << connection->connectionName();
    }
    else
    {
        qDebug() << "ConnectionPool: 连接无效或在事务中，不归还:" << connection->connectionName();
        emit connectionDestroyed(connection->connectionName());
    }
}

void ConnectionPool::createConnection()
{
    QString connectionName = generateConnectionName();
    auto connection = std::make_shared<DatabaseConnection>(connectionName, d->config.database);

    if (connection->open())
    {
        d->idleConnections.enqueue(connection);
        emit connectionCreated(connectionName);
        qDebug() << "ConnectionPool: 创建连接成功:" << connectionName;
    }
    else
    {
        QString error = QString("创建数据库连接失败: %1").arg(connection->lastError());
        d->lastErrorString = error;
        emit poolError(error);
        qWarning() << "ConnectionPool:" << error;
    }
}

bool ConnectionPool::createConnectionWithTimeout()
{
    qDebug() << "ConnectionPool: 开始创建连接（带超时控制）";

    QString connectionName = generateConnectionName();
    auto connection = std::make_shared<DatabaseConnection>(connectionName, d->config.database);

    // 使用QTimer来实现超时控制
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);

    bool connectionResult = false;
    bool timeoutOccurred = false;

    // 设置超时时间为5秒
    timeoutTimer.start(5000);

    // 连接超时信号
    QObject::connect(&timeoutTimer, &QTimer::timeout, [&timeoutOccurred]()
                     {
        timeoutOccurred = true;
        qWarning() << "ConnectionPool: 连接创建超时"; });

    // 尝试打开连接
    qDebug() << "ConnectionPool: 尝试打开连接:" << connectionName;
    connectionResult = connection->open();

    // 停止超时定时器
    timeoutTimer.stop();

    if (timeoutOccurred)
    {
        qWarning() << "ConnectionPool: 连接创建超时，放弃此连接";
        return false;
    }

    if (connectionResult)
    {
        d->idleConnections.enqueue(connection);
        emit connectionCreated(connectionName);
        qDebug() << "ConnectionPool: 创建连接成功:" << connectionName;
        return true;
    }
    else
    {
        QString error = QString("创建数据库连接失败: %1").arg(connection->lastError());
        d->lastErrorString = error;
        qWarning() << "ConnectionPool: 连接创建失败:" << error;
        return false;
    }
}

void ConnectionPool::removeConnection(std::shared_ptr<DatabaseConnection> connection)
{
    d->idleConnections.removeAll(connection);
    d->activeConnections.removeAll(connection);
    emit connectionDestroyed(connection->connectionName());
}

void ConnectionPool::createConnectionInternal()
{
    // 注意：此方法不使用锁，仅供内部调用

    QString connectionName = generateConnectionName();
    auto connection = std::make_shared<DatabaseConnection>(connectionName, d->config.database);

    if (connection->open())
    {
        d->idleConnections.enqueue(connection);
        emit connectionCreated(connectionName);
        qDebug() << "ConnectionPool: 内部创建连接成功:" << connectionName;
    }
    else
    {
        QString error = QString("内部创建数据库连接失败: %1").arg(connection->lastError());
        d->lastErrorString = error;
        qWarning() << "ConnectionPool:" << error;
    }
}

QString ConnectionPool::generateConnectionName()
{
    return QString("Pool_%1_%2").arg(QCoreApplication::applicationPid()).arg(++d->connectionCounter);
}

int ConnectionPool::activeConnections() const
{
    QMutexLocker locker(&d->poolMutex);
    return d->activeConnections.size();
}

int ConnectionPool::idleConnections() const
{
    QMutexLocker locker(&d->poolMutex);
    return d->idleConnections.size();
}

int ConnectionPool::totalConnections() const
{
    QMutexLocker locker(&d->poolMutex);
    return d->activeConnections.size() + d->idleConnections.size();
}

bool ConnectionPool::isHealthy() const
{
    return totalConnections() >= d->config.connectionPool.minConnections;
}

void ConnectionPool::updateConfig(const EnvironmentConfig &config)
{
    QMutexLocker locker(&d->poolMutex);
    d->config = config;

    // 调整连接池大小（使用内部方法避免死锁）
    resizeInternal(config.connectionPool.minConnections, config.connectionPool.maxConnections);
}

void ConnectionPool::resize(int minConnections, int maxConnections)
{
    QMutexLocker locker(&d->poolMutex);
    resizeInternal(minConnections, maxConnections);
}

void ConnectionPool::resizeInternal(int minConnections, int maxConnections)
{
    // 注意：此方法不使用锁，仅供内部调用

    d->config.connectionPool.minConnections = minConnections;
    d->config.connectionPool.maxConnections = maxConnections;

    // 计算当前连接数（不使用totalConnections()避免死锁）
    int currentTotal = d->activeConnections.size() + d->idleConnections.size();

    // 如果当前连接数少于最小值，创建更多连接
    while (currentTotal < minConnections)
    {
        createConnectionInternal();
        currentTotal = d->activeConnections.size() + d->idleConnections.size();
    }

    // 如果当前连接数超过最大值，移除多余的空闲连接
    while (currentTotal > maxConnections && !d->idleConnections.isEmpty())
    {
        auto connection = d->idleConnections.dequeue();
        emit connectionDestroyed(connection->connectionName());
        currentTotal = d->activeConnections.size() + d->idleConnections.size();
    }

    qDebug() << "ConnectionPool: 连接池大小调整完成，最小:" << minConnections
             << "，最大:" << maxConnections << "，当前:" << currentTotal;
}

void ConnectionPool::cleanupIdleConnections()
{
    QMutexLocker locker(&d->poolMutex);

    QDateTime now = QDateTime::currentDateTime();
    int idleTimeout = d->config.connectionPool.idleTimeout;

    // 移除超时的空闲连接
    auto it = d->idleConnections.begin();
    while (it != d->idleConnections.end())
    {
        auto connection = *it;
        if (connection->lastUsed().secsTo(now) > idleTimeout)
        {
            it = d->idleConnections.erase(it);
            emit connectionDestroyed(connection->connectionName());
            qDebug() << "ConnectionPool: 清理超时连接:" << connection->connectionName();
        }
        else
        {
            ++it;
        }
    }

    // 确保最小连接数
    while (totalConnections() < d->config.connectionPool.minConnections)
    {
        createConnection();
    }
}

void ConnectionPool::checkConnectionHealth()
{
    QMutexLocker locker(&d->poolMutex);

    // 检查空闲连接的健康状态
    auto it = d->idleConnections.begin();
    while (it != d->idleConnections.end())
    {
        auto connection = *it;
        if (!connection->testConnection())
        {
            it = d->idleConnections.erase(it);
            emit connectionDestroyed(connection->connectionName());
            qDebug() << "ConnectionPool: 移除不健康连接:" << connection->connectionName();
        }
        else
        {
            ++it;
        }
    }

    // 确保最小连接数
    while (totalConnections() < d->config.connectionPool.minConnections)
    {
        createConnection();
    }
}

//==============================================================================
// DatabaseManager 实现
//==============================================================================

class DatabaseManager::DatabaseManagerPrivate
{
public:
    std::unique_ptr<ConfigManager> configManager;
    std::unique_ptr<ConnectionPool> connectionPool;
    QString lastErrorString;
    bool initialized;
    bool connected;
    QTimer *reconnectTimer;

    DatabaseManagerPrivate() : initialized(false), connected(false), reconnectTimer(nullptr) {}
};

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent), d(std::make_unique<DatabaseManagerPrivate>())
{
    d->configManager = std::make_unique<ConfigManager>(this);

    // 连接配置管理器信号
    connect(d->configManager.get(), &ConfigManager::configChanged,
            this, &DatabaseManager::onConfigChanged);
    connect(d->configManager.get(), &ConfigManager::configError,
            this, &DatabaseManager::error);
}

DatabaseManager::~DatabaseManager()
{
    shutdown();
}

DatabaseManager *DatabaseManager::instance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance)
    {
        s_instance = new DatabaseManager();
    }
    return s_instance;
}

void DatabaseManager::destroyInstance()
{
    QMutexLocker locker(&s_mutex);
    if (s_instance)
    {
        delete s_instance;
        s_instance = nullptr;
    }
}

bool DatabaseManager::initialize(const QString &configPath)
{
    qDebug() << "DatabaseManager: 初始化数据库管理器";

    if (d->initialized)
    {
        qWarning() << "DatabaseManager: 已经初始化";
        return true;
    }

    // 加载配置
    if (!d->configManager->loadConfig(configPath))
    {
        setLastError("配置文件加载失败");
        return false;
    }

    // 初始化连接池
    if (!initializeConnectionPool())
    {
        setLastError("连接池初始化失败");
        return false;
    }

    // 设置自动重连
    setupAutoReconnect();

    d->initialized = true;
    d->connected = true;

    qDebug() << "DatabaseManager: 初始化完成";
    emit connected();

    return true;
}

bool DatabaseManager::initialize(const EnvironmentConfig &config)
{
    qDebug() << "DatabaseManager: 使用指定配置初始化";

    if (d->initialized)
    {
        qWarning() << "DatabaseManager: 已经初始化";
        return true;
    }

    // 创建连接池
    d->connectionPool = std::make_unique<ConnectionPool>(config, this);

    // 连接连接池信号
    connect(d->connectionPool.get(), &ConnectionPool::poolError,
            this, &DatabaseManager::onConnectionPoolError);

    // 初始化连接池
    d->connectionPool->initialize();

    // 设置自动重连
    setupAutoReconnect();

    d->initialized = true;
    d->connected = true;

    qDebug() << "DatabaseManager: 初始化完成";
    emit connected();

    return true;
}

void DatabaseManager::shutdown()
{
    if (!d->initialized)
    {
        return;
    }

    qDebug() << "DatabaseManager: 关闭数据库管理器";

    // 停止自动重连
    if (d->reconnectTimer)
    {
        d->reconnectTimer->stop();
        d->reconnectTimer->deleteLater();
        d->reconnectTimer = nullptr;
    }

    // 清理连接池
    if (d->connectionPool)
    {
        d->connectionPool->cleanup();
        d->connectionPool.reset();
    }

    d->initialized = false;
    d->connected = false;

    qDebug() << "DatabaseManager: 关闭完成";
    emit disconnected();
}

bool DatabaseManager::initializeConnectionPool()
{
    qDebug() << "DatabaseManager: 开始初始化连接池";

    EnvironmentConfig config = d->configManager->getCurrentConfig();

    // 创建连接池
    d->connectionPool = std::make_unique<ConnectionPool>(config, this);

    // 连接连接池信号
    connect(d->connectionPool.get(), &ConnectionPool::poolError,
            this, &DatabaseManager::onConnectionPoolError);

    // 初始化连接池（不阻塞）
    qDebug() << "DatabaseManager: 调用连接池初始化";
    d->connectionPool->initialize();

    // 检查连接池状态
    bool isHealthy = d->connectionPool->isHealthy();
    int totalConnections = d->connectionPool->totalConnections();

    qDebug() << "DatabaseManager: 连接池初始化完成";
    qDebug() << "  - 连接池健康状态:" << (isHealthy ? "健康" : "不健康");
    qDebug() << "  - 总连接数:" << totalConnections;

    if (!isHealthy && totalConnections == 0)
    {
        qWarning() << "DatabaseManager: 连接池初始化失败，但继续运行（离线模式）";
        setLastError("连接池初始化失败：无法创建任何数据库连接");
        // 不返回false，允许程序继续运行
        return true; // 改为返回true，允许离线模式
    }

    if (totalConnections > 0)
    {
        qDebug() << "DatabaseManager: 连接池初始化成功，至少有" << totalConnections << "个连接可用";
        return true;
    }

    qDebug() << "DatabaseManager: 连接池状态不确定，但允许继续运行";
    return true; // 总是返回true，允许程序继续
}

void DatabaseManager::setupAutoReconnect()
{
    EnvironmentConfig config = d->configManager->getCurrentConfig();

    if (!config.options.autoReconnect)
    {
        return;
    }

    d->reconnectTimer = new QTimer(this);
    d->reconnectTimer->setInterval(config.connectionPool.retryDelay);
    d->reconnectTimer->setSingleShot(true);

    connect(d->reconnectTimer, &QTimer::timeout, this, &DatabaseManager::reconnect);
}

bool DatabaseManager::switchEnvironment(const QString &environment)
{
    if (!d->initialized)
    {
        setLastError("数据库管理器未初始化");
        return false;
    }

    qDebug() << "DatabaseManager: 切换环境到:" << environment;

    if (!d->configManager->setCurrentEnvironment(environment))
    {
        setLastError(QString("切换环境失败: %1").arg(environment));
        return false;
    }

    // 更新连接池配置
    EnvironmentConfig config = d->configManager->getCurrentConfig();
    d->connectionPool->updateConfig(config);

    emit environmentChanged(environment);
    return true;
}

QString DatabaseManager::currentEnvironment() const
{
    return d->configManager ? d->configManager->getCurrentEnvironment() : QString();
}

QStringList DatabaseManager::availableEnvironments() const
{
    return d->configManager ? d->configManager->getAvailableEnvironments() : QStringList();
}

bool DatabaseManager::isConnected() const
{
    return d->connected && d->connectionPool && d->connectionPool->isHealthy();
}

bool DatabaseManager::testConnection()
{
    if (!d->connectionPool)
    {
        return false;
    }

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        return false;
    }

    bool result = connection->testConnection();
    d->connectionPool->returnConnection(connection);

    return result;
}

void DatabaseManager::reconnect()
{
    if (!d->initialized)
    {
        return;
    }

    qDebug() << "DatabaseManager: 尝试重新连接";

    // 清理现有连接池
    if (d->connectionPool)
    {
        d->connectionPool->cleanup();
    }

    // 重新初始化连接池
    if (initializeConnectionPool())
    {
        d->connected = true;
        qDebug() << "DatabaseManager: 重新连接成功";
        emit connected();
    }
    else
    {
        d->connected = false;
        qWarning() << "DatabaseManager: 重新连接失败";

        // 启动重连定时器
        if (d->reconnectTimer)
        {
            d->reconnectTimer->start();
        }
    }
}

// CRUD操作实现
QueryResult DatabaseManager::select(const QString &table,
                                    const QStringList &columns,
                                    const QString &where,
                                    const QVariantList &params,
                                    const QString &orderBy,
                                    int limit,
                                    int offset)
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return QueryResult();
    }

    QString sql = buildSelectSql(table, columns, where, orderBy, limit, offset);
    return executeQuery(sql, params);
}

QueryResult DatabaseManager::executeQuery(const QString &sql, const QVariantList &params)
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return QueryResult();
    }

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        setLastError("无法获取数据库连接");
        return QueryResult();
    }

    QElapsedTimer timer;
    timer.start();

    QueryResult result = connection->executeQuery(sql, params);

    int executionTime = timer.elapsed();
    logQuery(sql, params, executionTime);
    emit queryExecuted(sql, executionTime);

    if (result.hasError())
    {
        setLastError(result.lastError());
    }
    else
    {
        clearError();
    }

    d->connectionPool->returnConnection(connection);
    return std::move(result);
}

bool DatabaseManager::insert(const QString &table, const QVariantMap &data)
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return false;
    }

    if (data.isEmpty())
    {
        setLastError("插入数据不能为空");
        return false;
    }

    QString sql = buildInsertSql(table, data);
    QVariantList params = data.values();

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        setLastError("无法获取数据库连接");
        return false;
    }

    QElapsedTimer timer;
    timer.start();

    bool success = connection->executeNonQuery(sql, params);

    int executionTime = timer.elapsed();
    logQuery(sql, params, executionTime);
    emit queryExecuted(sql, executionTime);

    if (!success)
    {
        setLastError(connection->lastError());
    }
    else
    {
        clearError();
    }

    d->connectionPool->returnConnection(connection);
    return success;
}

bool DatabaseManager::update(const QString &table, const QVariantMap &data,
                             const QString &where, const QVariantList &params)
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return false;
    }

    if (data.isEmpty())
    {
        setLastError("更新数据不能为空");
        return false;
    }

    if (where.isEmpty())
    {
        setLastError("更新操作必须指定WHERE条件");
        return false;
    }

    QString sql = buildUpdateSql(table, data, where);
    QVariantList allParams = data.values();
    allParams.append(params);

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        setLastError("无法获取数据库连接");
        return false;
    }

    QElapsedTimer timer;
    timer.start();

    bool success = connection->executeNonQuery(sql, allParams);

    int executionTime = timer.elapsed();
    logQuery(sql, allParams, executionTime);
    emit queryExecuted(sql, executionTime);

    if (!success)
    {
        setLastError(connection->lastError());
    }
    else
    {
        clearError();
    }

    d->connectionPool->returnConnection(connection);
    return success;
}

bool DatabaseManager::remove(const QString &table, const QString &where, const QVariantList &params)
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return false;
    }

    if (where.isEmpty())
    {
        setLastError("删除操作必须指定WHERE条件");
        return false;
    }

    QString sql = buildDeleteSql(table, where);

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        setLastError("无法获取数据库连接");
        return false;
    }

    QElapsedTimer timer;
    timer.start();

    bool success = connection->executeNonQuery(sql, params);

    int executionTime = timer.elapsed();
    logQuery(sql, params, executionTime);
    emit queryExecuted(sql, executionTime);

    if (!success)
    {
        setLastError(connection->lastError());
    }
    else
    {
        clearError();
    }

    d->connectionPool->returnConnection(connection);
    return success;
}

// 事务管理
bool DatabaseManager::beginTransaction()
{
    if (!isConnected())
    {
        setLastError("数据库未连接");
        return false;
    }

    auto connection = d->connectionPool->getConnection();
    if (!connection)
    {
        setLastError("无法获取数据库连接");
        return false;
    }

    bool success = connection->beginTransaction();
    if (!success)
    {
        setLastError(connection->lastError());
        d->connectionPool->returnConnection(connection);
    }
    // 注意：事务开始后不立即归还连接，需要在提交或回滚后归还

    return success;
}

bool DatabaseManager::commitTransaction()
{
    // 这里需要获取当前事务的连接，简化实现
    // 在实际应用中，应该维护事务连接的映射
    setLastError("事务管理需要在连接级别实现");
    return false;
}

bool DatabaseManager::rollbackTransaction()
{
    // 这里需要获取当前事务的连接，简化实现
    setLastError("事务管理需要在连接级别实现");
    return false;
}

bool DatabaseManager::isInTransaction() const
{
    // 简化实现，实际应该检查当前连接的事务状态
    return false;
}

// 工具方法
int DatabaseManager::count(const QString &table, const QString &where, const QVariantList &params)
{
    qDebug() << "DatabaseManager::count 开始执行";
    qDebug() << "  - 表名:" << table;
    qDebug() << "  - WHERE条件:" << (where.isEmpty() ? "无条件" : where);
    qDebug() << "  - 参数:" << params;

    if (!isConnected())
    {
        qWarning() << "DatabaseManager::count - 数据库未连接";
        setLastError("数据库未连接");
        return -1;
    }

    QString sql = QString("SELECT COUNT(*) FROM %1").arg(table);
    if (!where.isEmpty())
    {
        sql += QString(" WHERE %1").arg(where);
    }

    qDebug() << "  - 执行SQL:" << sql;

    QueryResult result = executeQuery(sql, params);

    qDebug() << "  - executeQuery 返回，检查结果";
    qDebug() << "  - result.isValid():" << result.isValid();
    qDebug() << "  - result.hasError():" << result.hasError();
    qDebug() << "  - result.lastError():" << result.lastError();

    if (!result.isValid())
    {
        qWarning() << "DatabaseManager::count - 查询结果无效:" << result.lastError();
        setLastError(result.lastError());
        return -1;
    }

    if (result.hasError())
    {
        qWarning() << "DatabaseManager::count - 查询有错误:" << result.lastError();
        setLastError(result.lastError());
        return -1;
    }

    qDebug() << "  - 尝试调用 result.next()";
    bool hasNext = result.next();
    qDebug() << "  - result.next() 返回:" << hasNext;

    if (!hasNext)
    {
        qWarning() << "DatabaseManager::count - 查询结果为空，无法获取下一行";
        setLastError("查询结果为空，无法获取下一行");
        return -1;
    }

    QVariant countValue = result.value(0);
    qDebug() << "  - result.value(0) 返回:" << countValue;
    qDebug() << "  - countValue.isValid():" << countValue.isValid();
    qDebug() << "  - countValue.type():" << countValue.type();

    int count = countValue.toInt();
    qDebug() << "  - 转换为int后的值:" << count;
    qDebug() << "  - 查询成功，记录数:" << count;

    return count;
}

bool DatabaseManager::tableExists(const QString &tableName)
{
    QString sql = "SHOW TABLES LIKE ?";
    QVariantList params;
    params << tableName;

    QueryResult result = executeQuery(sql, params);
    return result.isValid() && result.next();
}

QStringList DatabaseManager::tableNames()
{
    QStringList tables;
    QueryResult result = executeQuery("SHOW TABLES");

    while (result.next())
    {
        tables << result.value(0).toString();
    }

    return tables;
}

// SQL构建方法
QString DatabaseManager::buildSelectSql(const QString &table, const QStringList &columns,
                                        const QString &where, const QString &orderBy,
                                        int limit, int offset) const
{
    QString sql = "SELECT ";

    if (columns.isEmpty())
    {
        sql += "*";
    }
    else
    {
        sql += columns.join(", ");
    }

    sql += QString(" FROM %1").arg(table);

    if (!where.isEmpty())
    {
        sql += QString(" WHERE %1").arg(where);
    }

    if (!orderBy.isEmpty())
    {
        sql += QString(" ORDER BY %1").arg(orderBy);
    }

    if (limit > 0)
    {
        sql += QString(" LIMIT %1").arg(limit);
        if (offset > 0)
        {
            sql += QString(" OFFSET %1").arg(offset);
        }
    }

    return sql;
}

QString DatabaseManager::buildInsertSql(const QString &table, const QVariantMap &data) const
{
    QStringList columns = data.keys();
    QStringList placeholders;

    for (int i = 0; i < columns.size(); ++i)
    {
        placeholders << "?";
    }

    return QString("INSERT INTO %1 (%2) VALUES (%3)")
        .arg(table)
        .arg(columns.join(", "))
        .arg(placeholders.join(", "));
}

QString DatabaseManager::buildUpdateSql(const QString &table, const QVariantMap &data, const QString &where) const
{
    QStringList setParts;
    QStringList columns = data.keys();

    for (const QString &column : columns)
    {
        setParts << QString("%1 = ?").arg(column);
    }

    return QString("UPDATE %1 SET %2 WHERE %3")
        .arg(table)
        .arg(setParts.join(", "))
        .arg(where);
}

QString DatabaseManager::buildDeleteSql(const QString &table, const QString &where) const
{
    return QString("DELETE FROM %1 WHERE %2").arg(table).arg(where);
}

// 错误处理和日志
void DatabaseManager::setLastError(const QString &error)
{
    d->lastErrorString = error;
    qWarning() << "DatabaseManager Error:" << error;
}

void DatabaseManager::clearError()
{
    d->lastErrorString.clear();
}

QString DatabaseManager::lastError() const
{
    return d->lastErrorString;
}

bool DatabaseManager::hasError() const
{
    return !d->lastErrorString.isEmpty();
}

void DatabaseManager::logQuery(const QString &sql, const QVariantList &params, int executionTime) const
{
    EnvironmentConfig config = d->configManager ? d->configManager->getCurrentConfig() : EnvironmentConfig();

    if (!config.options.enableLogging)
    {
        return;
    }

    QString logLevel = config.options.logLevel;

    if (logLevel == "debug")
    {
        qDebug() << "DatabaseManager SQL:" << sql;
        if (!params.isEmpty())
        {
            qDebug() << "DatabaseManager Params:" << params;
        }
        qDebug() << "DatabaseManager Execution Time:" << executionTime << "ms";
    }
    else if (logLevel == "info" && executionTime > 1000)
    {
        qInfo() << "DatabaseManager Slow Query:" << sql << "(" << executionTime << "ms)";
    }
}

// 配置和状态
EnvironmentConfig DatabaseManager::currentConfig() const
{
    return d->configManager ? d->configManager->getCurrentConfig() : EnvironmentConfig();
}

ConnectionPool *DatabaseManager::connectionPool() const
{
    return d->connectionPool.get();
}

// 信号槽
void DatabaseManager::onConfigChanged(const QString &environment)
{
    qDebug() << "DatabaseManager: 配置已更改，环境:" << environment;

    if (d->connectionPool)
    {
        EnvironmentConfig config = d->configManager->getCurrentConfig();
        d->connectionPool->updateConfig(config);
    }
}

void DatabaseManager::onConnectionPoolError(const QString &error)
{
    setLastError(QString("连接池错误: %1").arg(error));
    emit this->error(d->lastErrorString);

    // 如果启用了自动重连，尝试重连
    EnvironmentConfig config = currentConfig();
    if (config.options.autoReconnect && d->reconnectTimer)
    {
        d->reconnectTimer->start();
    }
}
