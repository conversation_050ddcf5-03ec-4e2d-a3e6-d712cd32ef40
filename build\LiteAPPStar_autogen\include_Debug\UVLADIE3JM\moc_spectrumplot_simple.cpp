/****************************************************************************
** Meta object code from reading C++ file 'spectrumplot_simple.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/spectrumplot_simple.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'spectrumplot_simple.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SpectrumPlotSimple_t {
    QByteArrayData data[15];
    char stringdata0[171];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SpectrumPlotSimple_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SpectrumPlotSimple_t qt_meta_stringdata_SpectrumPlotSimple = {
    {
QT_MOC_LITERAL(0, 0, 18), // "SpectrumPlotSimple"
QT_MOC_LITERAL(1, 19, 21), // "frequencyRangeChanged"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 9), // "startFreq"
QT_MOC_LITERAL(4, 52, 7), // "endFreq"
QT_MOC_LITERAL(5, 60, 17), // "dataPointSelected"
QT_MOC_LITERAL(6, 78, 9), // "frequency"
QT_MOC_LITERAL(7, 88, 9), // "amplitude"
QT_MOC_LITERAL(8, 98, 9), // "autoScale"
QT_MOC_LITERAL(9, 108, 9), // "resetZoom"
QT_MOC_LITERAL(10, 118, 9), // "clearHold"
QT_MOC_LITERAL(11, 128, 10), // "setMaxHold"
QT_MOC_LITERAL(12, 139, 10), // "setMinHold"
QT_MOC_LITERAL(13, 150, 10), // "setAvgHold"
QT_MOC_LITERAL(14, 161, 9) // "setNoHold"

    },
    "SpectrumPlotSimple\0frequencyRangeChanged\0"
    "\0startFreq\0endFreq\0dataPointSelected\0"
    "frequency\0amplitude\0autoScale\0resetZoom\0"
    "clearHold\0setMaxHold\0setMinHold\0"
    "setAvgHold\0setNoHold"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SpectrumPlotSimple[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   59,    2, 0x06 /* Public */,
       5,    2,   64,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,   69,    2, 0x08 /* Private */,
       9,    0,   70,    2, 0x08 /* Private */,
      10,    0,   71,    2, 0x08 /* Private */,
      11,    0,   72,    2, 0x08 /* Private */,
      12,    0,   73,    2, 0x08 /* Private */,
      13,    0,   74,    2, 0x08 /* Private */,
      14,    0,   75,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    6,    7,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void SpectrumPlotSimple::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SpectrumPlotSimple *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->frequencyRangeChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->dataPointSelected((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 2: _t->autoScale(); break;
        case 3: _t->resetZoom(); break;
        case 4: _t->clearHold(); break;
        case 5: _t->setMaxHold(); break;
        case 6: _t->setMinHold(); break;
        case 7: _t->setAvgHold(); break;
        case 8: _t->setNoHold(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SpectrumPlotSimple::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpectrumPlotSimple::frequencyRangeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SpectrumPlotSimple::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpectrumPlotSimple::dataPointSelected)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SpectrumPlotSimple::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_SpectrumPlotSimple.data,
    qt_meta_data_SpectrumPlotSimple,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SpectrumPlotSimple::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SpectrumPlotSimple::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SpectrumPlotSimple.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int SpectrumPlotSimple::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void SpectrumPlotSimple::frequencyRangeChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void SpectrumPlotSimple::dataPointSelected(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
