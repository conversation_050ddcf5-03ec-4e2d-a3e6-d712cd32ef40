/********************************************************************************
** Form generated from reading UI file 'comprehensive_view.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_COMPREHENSIVE_VIEW_H
#define UI_COMPREHENSIVE_VIEW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDateTimeEdit>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTableView>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ComprehensiveViewWidget
{
public:
    QVBoxLayout *mainLayout;
    QSplitter *mainSplitter;
    QGroupBox *queryGroupBox;
    QVBoxLayout *queryMainLayout;
    QWidget *queryTitleBar;
    QHBoxLayout *queryTitleLayout;
    QLabel *queryTitleLabel;
    QSpacerItem *queryTitleSpacer;
    QWidget *queryContentArea;
    QVBoxLayout *queryContentLayout;
    QHBoxLayout *firstRowLayout;
    QLabel *timeLabel;
    QDateTimeEdit *startTimeEdit;
    QLabel *toLabel;
    QDateTimeEdit *endTimeEdit;
    QLabel *commLabel;
    QPushButton *commBBtn;
    QPushButton *commGBtn;
    QPushButton *commIBtn;
    QPushButton *commTBtn;
    QLabel *beamLabel;
    QLineEdit *beamEdit;
    QPushButton *mapSelectorBtn;
    QSpacerItem *firstRowSpacer;
    QHBoxLayout *secondRowLayout;
    QLabel *terminalTypeLabel;
    QPushButton *aircraftBtn;
    QPushButton *shipBtn;
    QPushButton *vehicleBtn;
    QLabel *imsxLabel;
    QLineEdit *targetIdEdit;
    QLabel *imexLabel;
    QLineEdit *imexEdit;
    QLabel *numberLabel;
    QLineEdit *numberEdit;
    QLabel *importanceLabel;
    QComboBox *statusCombo;
    QSpacerItem *secondRowSpacer;
    QHBoxLayout *thirdRowLayout;
    QLabel *businessLabel;
    QPushButton *voiceBtn;
    QPushButton *ipBtn;
    QPushButton *smsBtn;
    QLabel *callTypeLabel;
    QComboBox *callTypeCombo;
    QLabel *phoneLabel;
    QLineEdit *phoneEdit;
    QLabel *ipLabel;
    QLineEdit *ipAddressEdit;
    QSpacerItem *thirdRowSpacer;
    QHBoxLayout *buttonRowLayout;
    QPushButton *queryButton;
    QPushButton *resetButton;
    QPushButton *refreshButton;
    QPushButton *columnSettingsButton;
    QPushButton *exportButton;
    QSpacerItem *buttonRowSpacer;
    QGroupBox *resultGroupBox;
    QVBoxLayout *resultMainLayout;
    QWidget *resultContentArea;
    QVBoxLayout *resultLayout;
    QProgressBar *progressBar;
    QTableView *tableView;
    QHBoxLayout *paginationLayout;
    QPushButton *firstPageButton;
    QPushButton *previousPageButton;
    QPushButton *nextPageButton;
    QPushButton *lastPageButton;
    QFrame *separator1;
    QLabel *goToPageLabel;
    QLineEdit *pageNumberEdit;
    QPushButton *goToPageButton;
    QLabel *pageInfoLabel;
    QLabel *recordCountLabel;
    QLabel *pageSizeLabel;
    QComboBox *pageSizeCombo;
    QSpacerItem *paginationSpacer;

    void setupUi(QWidget *ComprehensiveViewWidget)
    {
        if (ComprehensiveViewWidget->objectName().isEmpty())
            ComprehensiveViewWidget->setObjectName(QString::fromUtf8("ComprehensiveViewWidget"));
        ComprehensiveViewWidget->resize(1200, 800);
        mainLayout = new QVBoxLayout(ComprehensiveViewWidget);
        mainLayout->setSpacing(0);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        mainLayout->setContentsMargins(0, 0, 0, 0);
        mainSplitter = new QSplitter(ComprehensiveViewWidget);
        mainSplitter->setObjectName(QString::fromUtf8("mainSplitter"));
        mainSplitter->setOrientation(Qt::Vertical);
        mainSplitter->setChildrenCollapsible(false);
        mainSplitter->setHandleWidth(1);
        queryGroupBox = new QGroupBox(mainSplitter);
        queryGroupBox->setObjectName(QString::fromUtf8("queryGroupBox"));
        queryGroupBox->setMaximumSize(QSize(16777215, 180));
        queryMainLayout = new QVBoxLayout(queryGroupBox);
        queryMainLayout->setSpacing(0);
        queryMainLayout->setObjectName(QString::fromUtf8("queryMainLayout"));
        queryMainLayout->setContentsMargins(0, 0, 0, 0);
        queryTitleBar = new QWidget(queryGroupBox);
        queryTitleBar->setObjectName(QString::fromUtf8("queryTitleBar"));
        queryTitleBar->setMinimumSize(QSize(0, 35));
        queryTitleBar->setMaximumSize(QSize(16777215, 35));
        queryTitleLayout = new QHBoxLayout(queryTitleBar);
        queryTitleLayout->setObjectName(QString::fromUtf8("queryTitleLayout"));
        queryTitleLayout->setContentsMargins(15, 0, 15, 0);
        queryTitleLabel = new QLabel(queryTitleBar);
        queryTitleLabel->setObjectName(QString::fromUtf8("queryTitleLabel"));

        queryTitleLayout->addWidget(queryTitleLabel);

        queryTitleSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        queryTitleLayout->addItem(queryTitleSpacer);


        queryMainLayout->addWidget(queryTitleBar);

        queryContentArea = new QWidget(queryGroupBox);
        queryContentArea->setObjectName(QString::fromUtf8("queryContentArea"));
        queryContentLayout = new QVBoxLayout(queryContentArea);
        queryContentLayout->setSpacing(10);
        queryContentLayout->setObjectName(QString::fromUtf8("queryContentLayout"));
        queryContentLayout->setContentsMargins(12, 10, 12, 10);
        firstRowLayout = new QHBoxLayout();
        firstRowLayout->setSpacing(10);
        firstRowLayout->setObjectName(QString::fromUtf8("firstRowLayout"));
        timeLabel = new QLabel(queryContentArea);
        timeLabel->setObjectName(QString::fromUtf8("timeLabel"));

        firstRowLayout->addWidget(timeLabel);

        startTimeEdit = new QDateTimeEdit(queryContentArea);
        startTimeEdit->setObjectName(QString::fromUtf8("startTimeEdit"));
        startTimeEdit->setCalendarPopup(true);

        firstRowLayout->addWidget(startTimeEdit);

        toLabel = new QLabel(queryContentArea);
        toLabel->setObjectName(QString::fromUtf8("toLabel"));
        toLabel->setAlignment(Qt::AlignCenter);

        firstRowLayout->addWidget(toLabel);

        endTimeEdit = new QDateTimeEdit(queryContentArea);
        endTimeEdit->setObjectName(QString::fromUtf8("endTimeEdit"));
        endTimeEdit->setCalendarPopup(true);

        firstRowLayout->addWidget(endTimeEdit);

        commLabel = new QLabel(queryContentArea);
        commLabel->setObjectName(QString::fromUtf8("commLabel"));

        firstRowLayout->addWidget(commLabel);

        commBBtn = new QPushButton(queryContentArea);
        commBBtn->setObjectName(QString::fromUtf8("commBBtn"));
        commBBtn->setCheckable(true);

        firstRowLayout->addWidget(commBBtn);

        commGBtn = new QPushButton(queryContentArea);
        commGBtn->setObjectName(QString::fromUtf8("commGBtn"));
        commGBtn->setCheckable(true);

        firstRowLayout->addWidget(commGBtn);

        commIBtn = new QPushButton(queryContentArea);
        commIBtn->setObjectName(QString::fromUtf8("commIBtn"));
        commIBtn->setCheckable(true);

        firstRowLayout->addWidget(commIBtn);

        commTBtn = new QPushButton(queryContentArea);
        commTBtn->setObjectName(QString::fromUtf8("commTBtn"));
        commTBtn->setCheckable(true);

        firstRowLayout->addWidget(commTBtn);

        beamLabel = new QLabel(queryContentArea);
        beamLabel->setObjectName(QString::fromUtf8("beamLabel"));

        firstRowLayout->addWidget(beamLabel);

        beamEdit = new QLineEdit(queryContentArea);
        beamEdit->setObjectName(QString::fromUtf8("beamEdit"));
        beamEdit->setMinimumSize(QSize(100, 0));
        beamEdit->setMaximumSize(QSize(140, 16777215));

        firstRowLayout->addWidget(beamEdit);

        mapSelectorBtn = new QPushButton(queryContentArea);
        mapSelectorBtn->setObjectName(QString::fromUtf8("mapSelectorBtn"));
        mapSelectorBtn->setMinimumSize(QSize(85, 0));
        mapSelectorBtn->setMaximumSize(QSize(110, 16777215));

        firstRowLayout->addWidget(mapSelectorBtn);

        firstRowSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        firstRowLayout->addItem(firstRowSpacer);


        queryContentLayout->addLayout(firstRowLayout);

        secondRowLayout = new QHBoxLayout();
        secondRowLayout->setSpacing(10);
        secondRowLayout->setObjectName(QString::fromUtf8("secondRowLayout"));
        terminalTypeLabel = new QLabel(queryContentArea);
        terminalTypeLabel->setObjectName(QString::fromUtf8("terminalTypeLabel"));

        secondRowLayout->addWidget(terminalTypeLabel);

        aircraftBtn = new QPushButton(queryContentArea);
        aircraftBtn->setObjectName(QString::fromUtf8("aircraftBtn"));
        aircraftBtn->setCheckable(true);

        secondRowLayout->addWidget(aircraftBtn);

        shipBtn = new QPushButton(queryContentArea);
        shipBtn->setObjectName(QString::fromUtf8("shipBtn"));
        shipBtn->setCheckable(true);

        secondRowLayout->addWidget(shipBtn);

        vehicleBtn = new QPushButton(queryContentArea);
        vehicleBtn->setObjectName(QString::fromUtf8("vehicleBtn"));
        vehicleBtn->setCheckable(true);

        secondRowLayout->addWidget(vehicleBtn);

        imsxLabel = new QLabel(queryContentArea);
        imsxLabel->setObjectName(QString::fromUtf8("imsxLabel"));

        secondRowLayout->addWidget(imsxLabel);

        targetIdEdit = new QLineEdit(queryContentArea);
        targetIdEdit->setObjectName(QString::fromUtf8("targetIdEdit"));
        targetIdEdit->setMinimumSize(QSize(80, 0));
        targetIdEdit->setMaximumSize(QSize(100, 16777215));

        secondRowLayout->addWidget(targetIdEdit);

        imexLabel = new QLabel(queryContentArea);
        imexLabel->setObjectName(QString::fromUtf8("imexLabel"));

        secondRowLayout->addWidget(imexLabel);

        imexEdit = new QLineEdit(queryContentArea);
        imexEdit->setObjectName(QString::fromUtf8("imexEdit"));
        imexEdit->setMinimumSize(QSize(80, 0));
        imexEdit->setMaximumSize(QSize(100, 16777215));

        secondRowLayout->addWidget(imexEdit);

        numberLabel = new QLabel(queryContentArea);
        numberLabel->setObjectName(QString::fromUtf8("numberLabel"));

        secondRowLayout->addWidget(numberLabel);

        numberEdit = new QLineEdit(queryContentArea);
        numberEdit->setObjectName(QString::fromUtf8("numberEdit"));
        numberEdit->setMinimumSize(QSize(90, 0));
        numberEdit->setMaximumSize(QSize(110, 16777215));

        secondRowLayout->addWidget(numberEdit);

        importanceLabel = new QLabel(queryContentArea);
        importanceLabel->setObjectName(QString::fromUtf8("importanceLabel"));

        secondRowLayout->addWidget(importanceLabel);

        statusCombo = new QComboBox(queryContentArea);
        statusCombo->addItem(QString());
        statusCombo->addItem(QString());
        statusCombo->addItem(QString());
        statusCombo->addItem(QString());
        statusCombo->setObjectName(QString::fromUtf8("statusCombo"));
        statusCombo->setMinimumSize(QSize(90, 0));
        statusCombo->setMaximumSize(QSize(110, 16777215));

        secondRowLayout->addWidget(statusCombo);

        secondRowSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        secondRowLayout->addItem(secondRowSpacer);


        queryContentLayout->addLayout(secondRowLayout);

        thirdRowLayout = new QHBoxLayout();
        thirdRowLayout->setSpacing(10);
        thirdRowLayout->setObjectName(QString::fromUtf8("thirdRowLayout"));
        businessLabel = new QLabel(queryContentArea);
        businessLabel->setObjectName(QString::fromUtf8("businessLabel"));

        thirdRowLayout->addWidget(businessLabel);

        voiceBtn = new QPushButton(queryContentArea);
        voiceBtn->setObjectName(QString::fromUtf8("voiceBtn"));
        voiceBtn->setCheckable(true);

        thirdRowLayout->addWidget(voiceBtn);

        ipBtn = new QPushButton(queryContentArea);
        ipBtn->setObjectName(QString::fromUtf8("ipBtn"));
        ipBtn->setCheckable(true);

        thirdRowLayout->addWidget(ipBtn);

        smsBtn = new QPushButton(queryContentArea);
        smsBtn->setObjectName(QString::fromUtf8("smsBtn"));
        smsBtn->setCheckable(true);

        thirdRowLayout->addWidget(smsBtn);

        callTypeLabel = new QLabel(queryContentArea);
        callTypeLabel->setObjectName(QString::fromUtf8("callTypeLabel"));

        thirdRowLayout->addWidget(callTypeLabel);

        callTypeCombo = new QComboBox(queryContentArea);
        callTypeCombo->addItem(QString());
        callTypeCombo->addItem(QString());
        callTypeCombo->addItem(QString());
        callTypeCombo->addItem(QString());
        callTypeCombo->setObjectName(QString::fromUtf8("callTypeCombo"));
        callTypeCombo->setMinimumSize(QSize(75, 0));
        callTypeCombo->setMaximumSize(QSize(95, 16777215));

        thirdRowLayout->addWidget(callTypeCombo);

        phoneLabel = new QLabel(queryContentArea);
        phoneLabel->setObjectName(QString::fromUtf8("phoneLabel"));

        thirdRowLayout->addWidget(phoneLabel);

        phoneEdit = new QLineEdit(queryContentArea);
        phoneEdit->setObjectName(QString::fromUtf8("phoneEdit"));
        phoneEdit->setMinimumSize(QSize(90, 0));
        phoneEdit->setMaximumSize(QSize(110, 16777215));

        thirdRowLayout->addWidget(phoneEdit);

        ipLabel = new QLabel(queryContentArea);
        ipLabel->setObjectName(QString::fromUtf8("ipLabel"));

        thirdRowLayout->addWidget(ipLabel);

        ipAddressEdit = new QLineEdit(queryContentArea);
        ipAddressEdit->setObjectName(QString::fromUtf8("ipAddressEdit"));
        ipAddressEdit->setMinimumSize(QSize(110, 0));
        ipAddressEdit->setMaximumSize(QSize(140, 16777215));

        thirdRowLayout->addWidget(ipAddressEdit);

        thirdRowSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        thirdRowLayout->addItem(thirdRowSpacer);


        queryContentLayout->addLayout(thirdRowLayout);

        buttonRowLayout = new QHBoxLayout();
        buttonRowLayout->setSpacing(8);
        buttonRowLayout->setObjectName(QString::fromUtf8("buttonRowLayout"));
        queryButton = new QPushButton(queryContentArea);
        queryButton->setObjectName(QString::fromUtf8("queryButton"));

        buttonRowLayout->addWidget(queryButton);

        resetButton = new QPushButton(queryContentArea);
        resetButton->setObjectName(QString::fromUtf8("resetButton"));

        buttonRowLayout->addWidget(resetButton);

        refreshButton = new QPushButton(queryContentArea);
        refreshButton->setObjectName(QString::fromUtf8("refreshButton"));

        buttonRowLayout->addWidget(refreshButton);

        columnSettingsButton = new QPushButton(queryContentArea);
        columnSettingsButton->setObjectName(QString::fromUtf8("columnSettingsButton"));

        buttonRowLayout->addWidget(columnSettingsButton);

        exportButton = new QPushButton(queryContentArea);
        exportButton->setObjectName(QString::fromUtf8("exportButton"));

        buttonRowLayout->addWidget(exportButton);

        buttonRowSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonRowLayout->addItem(buttonRowSpacer);


        queryContentLayout->addLayout(buttonRowLayout);


        queryMainLayout->addWidget(queryContentArea);

        mainSplitter->addWidget(queryGroupBox);
        resultGroupBox = new QGroupBox(mainSplitter);
        resultGroupBox->setObjectName(QString::fromUtf8("resultGroupBox"));
        resultMainLayout = new QVBoxLayout(resultGroupBox);
        resultMainLayout->setSpacing(0);
        resultMainLayout->setObjectName(QString::fromUtf8("resultMainLayout"));
        resultMainLayout->setContentsMargins(0, 0, 0, 0);
        resultContentArea = new QWidget(resultGroupBox);
        resultContentArea->setObjectName(QString::fromUtf8("resultContentArea"));
        resultLayout = new QVBoxLayout(resultContentArea);
        resultLayout->setSpacing(10);
        resultLayout->setObjectName(QString::fromUtf8("resultLayout"));
        resultLayout->setContentsMargins(15, 15, 15, 15);
        progressBar = new QProgressBar(resultContentArea);
        progressBar->setObjectName(QString::fromUtf8("progressBar"));
        progressBar->setVisible(false);
        progressBar->setMaximumSize(QSize(16777215, 20));

        resultLayout->addWidget(progressBar);

        tableView = new QTableView(resultContentArea);
        tableView->setObjectName(QString::fromUtf8("tableView"));

        resultLayout->addWidget(tableView);

        paginationLayout = new QHBoxLayout();
        paginationLayout->setObjectName(QString::fromUtf8("paginationLayout"));
        firstPageButton = new QPushButton(resultContentArea);
        firstPageButton->setObjectName(QString::fromUtf8("firstPageButton"));

        paginationLayout->addWidget(firstPageButton);

        previousPageButton = new QPushButton(resultContentArea);
        previousPageButton->setObjectName(QString::fromUtf8("previousPageButton"));

        paginationLayout->addWidget(previousPageButton);

        nextPageButton = new QPushButton(resultContentArea);
        nextPageButton->setObjectName(QString::fromUtf8("nextPageButton"));

        paginationLayout->addWidget(nextPageButton);

        lastPageButton = new QPushButton(resultContentArea);
        lastPageButton->setObjectName(QString::fromUtf8("lastPageButton"));

        paginationLayout->addWidget(lastPageButton);

        separator1 = new QFrame(resultContentArea);
        separator1->setObjectName(QString::fromUtf8("separator1"));
        separator1->setMaximumSize(QSize(1, 20));
        separator1->setFrameShape(QFrame::VLine);

        paginationLayout->addWidget(separator1);

        goToPageLabel = new QLabel(resultContentArea);
        goToPageLabel->setObjectName(QString::fromUtf8("goToPageLabel"));

        paginationLayout->addWidget(goToPageLabel);

        pageNumberEdit = new QLineEdit(resultContentArea);
        pageNumberEdit->setObjectName(QString::fromUtf8("pageNumberEdit"));
        pageNumberEdit->setMaximumSize(QSize(50, 16777215));

        paginationLayout->addWidget(pageNumberEdit);

        goToPageButton = new QPushButton(resultContentArea);
        goToPageButton->setObjectName(QString::fromUtf8("goToPageButton"));

        paginationLayout->addWidget(goToPageButton);

        pageInfoLabel = new QLabel(resultContentArea);
        pageInfoLabel->setObjectName(QString::fromUtf8("pageInfoLabel"));

        paginationLayout->addWidget(pageInfoLabel);

        recordCountLabel = new QLabel(resultContentArea);
        recordCountLabel->setObjectName(QString::fromUtf8("recordCountLabel"));

        paginationLayout->addWidget(recordCountLabel);

        pageSizeLabel = new QLabel(resultContentArea);
        pageSizeLabel->setObjectName(QString::fromUtf8("pageSizeLabel"));

        paginationLayout->addWidget(pageSizeLabel);

        pageSizeCombo = new QComboBox(resultContentArea);
        pageSizeCombo->addItem(QString());
        pageSizeCombo->addItem(QString());
        pageSizeCombo->addItem(QString());
        pageSizeCombo->addItem(QString());
        pageSizeCombo->setObjectName(QString::fromUtf8("pageSizeCombo"));
        pageSizeCombo->setMaximumSize(QSize(80, 16777215));

        paginationLayout->addWidget(pageSizeCombo);

        paginationSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        paginationLayout->addItem(paginationSpacer);


        resultLayout->addLayout(paginationLayout);


        resultMainLayout->addWidget(resultContentArea);

        mainSplitter->addWidget(resultGroupBox);

        mainLayout->addWidget(mainSplitter);


        retranslateUi(ComprehensiveViewWidget);

        QMetaObject::connectSlotsByName(ComprehensiveViewWidget);
    } // setupUi

    void retranslateUi(QWidget *ComprehensiveViewWidget)
    {
        ComprehensiveViewWidget->setWindowTitle(QCoreApplication::translate("ComprehensiveViewWidget", "\347\273\274\345\220\210\351\230\205\350\247\210 - \346\225\260\346\215\256\346\237\245\350\257\242\344\270\216\345\261\225\347\244\272", nullptr));
        queryGroupBox->setTitle(QString());
        queryGroupBox->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QGroupBox {\n"
"    border: none;\n"
"    background-color: #f8f9fa;\n"
"    margin: 0px;\n"
"    padding: 0px;\n"
"}", nullptr));
        queryTitleBar->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QWidget {\n"
"    background-color: #e9ecef;\n"
"    border-bottom: 1px solid #dee2e6;\n"
"}", nullptr));
        queryTitleLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\237\245\350\257\242\346\235\241\344\273\266\350\256\276\347\275\256", nullptr));
        queryTitleLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel {\n"
"    font-weight: bold;\n"
"    font-size: 13px;\n"
"    color: #495057;\n"
"    background: transparent;\n"
"}", nullptr));
        queryContentArea->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QWidget { background-color: #ffffff; }", nullptr));
        timeLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\227\266\351\227\264\350\214\203\345\233\264:", nullptr));
        timeLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }", nullptr));
        startTimeEdit->setDisplayFormat(QCoreApplication::translate("ComprehensiveViewWidget", "yyyy-MM-dd hh:mm:ss", nullptr));
        toLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\207\263", nullptr));
        toLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "font-weight: bold; color: #7f8c8d; font-size: 11px;", nullptr));
        endTimeEdit->setDisplayFormat(QCoreApplication::translate("ComprehensiveViewWidget", "yyyy-MM-dd hh:mm:ss", nullptr));
        commLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\200\232\350\256\257\344\275\223\345\210\266:", nullptr));
        commLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }", nullptr));
        commBBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "B", nullptr));
        commBBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }\n"
"QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #229954; }", nullptr));
        commGBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "G", nullptr));
        commGBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }\n"
"QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #229954; }", nullptr));
        commIBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "I", nullptr));
        commIBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }\n"
"QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #229954; }", nullptr));
        commTBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "T", nullptr));
        commTBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }\n"
"QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #229954; }", nullptr));
        beamLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\263\242\346\235\237\350\214\203\345\233\264:", nullptr));
        beamLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }", nullptr));
        beamEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245\346\263\242\346\235\237\350\214\203\345\233\264", nullptr));
        beamEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        mapSelectorBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\234\260\345\233\276\351\200\211\346\213\251\345\231\250", nullptr));
        mapSelectorBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background: #E3F2FD; color: #1976D2; border: 1px solid #BBDEFB; border-radius: 4px; padding: 4px 8px; font-size: 11px; font-weight: bold; }\n"
"QPushButton:hover { background: #BBDEFB; }\n"
"QPushButton:pressed { background: #90CAF9; }", nullptr));
        terminalTypeLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\347\273\210\347\253\257\347\261\273\345\236\213:", nullptr));
        terminalTypeLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }", nullptr));
        aircraftBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\243\236\346\234\272", nullptr));
        aircraftBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }\n"
"QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #2980b9; }", nullptr));
        shipBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\210\271\350\275\275", nullptr));
        shipBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }\n"
"QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #2980b9; }", nullptr));
        vehicleBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\275\246\350\275\275", nullptr));
        vehicleBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }\n"
"QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #2980b9; }", nullptr));
        imsxLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "IMSX:", nullptr));
        imsxLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }", nullptr));
        targetIdEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245IMSX", nullptr));
        targetIdEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        imexLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "IMEX:", nullptr));
        imexLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }", nullptr));
        imexEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245IMEX", nullptr));
        imexEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        numberLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\217\267\347\240\201:", nullptr));
        numberLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }", nullptr));
        numberEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245\345\217\267\347\240\201", nullptr));
        numberEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        importanceLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\207\215\350\246\201\346\200\247\347\272\247\345\210\253:", nullptr));
        importanceLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 80px; }", nullptr));
        statusCombo->setItemText(0, QCoreApplication::translate("ComprehensiveViewWidget", "\345\205\250\351\203\250", nullptr));
        statusCombo->setItemText(1, QCoreApplication::translate("ComprehensiveViewWidget", "\347\272\247\345\210\2531", nullptr));
        statusCombo->setItemText(2, QCoreApplication::translate("ComprehensiveViewWidget", "\347\272\247\345\210\2532", nullptr));
        statusCombo->setItemText(3, QCoreApplication::translate("ComprehensiveViewWidget", "\347\272\247\345\210\2533", nullptr));

        statusCombo->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QComboBox {\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 4px;\n"
"    padding: 4px 6px;\n"
"    font-size: 10px;\n"
"    background-color: white;\n"
"    font-weight: normal;\n"
"}\n"
"QComboBox:hover {\n"
"    border-color: #adb5bd;\n"
"}\n"
"QComboBox:focus {\n"
"    border-color: #007bff;\n"
"}\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    width: 16px;\n"
"}\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border: none;\n"
"    width: 0px;\n"
"    height: 0px;\n"
"    border-left: 3px solid transparent;\n"
"    border-right: 3px solid transparent;\n"
"    border-top: 3px solid #495057;\n"
"}", nullptr));
        businessLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\344\270\232\345\212\241\347\261\273\345\236\213:", nullptr));
        businessLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }", nullptr));
        voiceBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\257\235\351\237\263", nullptr));
        voiceBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }\n"
"QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #d35400; }", nullptr));
        ipBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "IP", nullptr));
        ipBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }\n"
"QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #d35400; }", nullptr));
        smsBtn->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\347\237\255\346\266\210\346\201\257", nullptr));
        smsBtn->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }\n"
"QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }\n"
"QPushButton:hover { background: #d5dbdb; }\n"
"QPushButton:checked:hover { background: #d35400; }", nullptr));
        callTypeLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\221\274\345\217\253\347\261\273\345\236\213:", nullptr));
        callTypeLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }", nullptr));
        callTypeCombo->setItemText(0, QCoreApplication::translate("ComprehensiveViewWidget", "\345\205\250\351\203\250", nullptr));
        callTypeCombo->setItemText(1, QCoreApplication::translate("ComprehensiveViewWidget", "\345\221\274\345\205\245", nullptr));
        callTypeCombo->setItemText(2, QCoreApplication::translate("ComprehensiveViewWidget", "\345\221\274\345\207\272", nullptr));
        callTypeCombo->setItemText(3, QCoreApplication::translate("ComprehensiveViewWidget", "\346\234\252\347\237\245", nullptr));

        callTypeCombo->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QComboBox {\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 4px;\n"
"    padding: 4px 6px;\n"
"    font-size: 10px;\n"
"    background-color: white;\n"
"    font-weight: normal;\n"
"}\n"
"QComboBox:hover {\n"
"    border-color: #adb5bd;\n"
"}\n"
"QComboBox:focus {\n"
"    border-color: #007bff;\n"
"}\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    width: 16px;\n"
"}\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border: none;\n"
"    width: 0px;\n"
"    height: 0px;\n"
"    border-left: 3px solid transparent;\n"
"    border-right: 3px solid transparent;\n"
"    border-top: 3px solid #495057;\n"
"}", nullptr));
        phoneLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\217\267\347\240\201:", nullptr));
        phoneLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }", nullptr));
        phoneEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245\345\217\267\347\240\201", nullptr));
        phoneEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        ipLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "IP\345\234\260\345\235\200:", nullptr));
        ipLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }", nullptr));
        ipAddressEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\276\223\345\205\245IP\345\234\260\345\235\200", nullptr));
        ipAddressEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }\n"
"QLineEdit::placeholder { color: #6c757d; font-size: 10px; }", nullptr));
        queryButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\237\245\350\257\242", nullptr));
        queryButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background-color: #28a745; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }\n"
"QPushButton:hover { background-color: #218838; }", nullptr));
        resetButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\207\215\347\275\256", nullptr));
        resetButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background-color: #fd7e14; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }\n"
"QPushButton:hover { background-color: #e8690b; }", nullptr));
        refreshButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\210\267\346\226\260", nullptr));
        refreshButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background-color: #007bff; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }\n"
"QPushButton:hover { background-color: #0056b3; }", nullptr));
        columnSettingsButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\210\227\350\256\276\347\275\256", nullptr));
        columnSettingsButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background-color: #6f42c1; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }\n"
"QPushButton:hover { background-color: #5a32a3; }", nullptr));
        exportButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\257\274\345\207\272", nullptr));
        exportButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton { background-color: #6c757d; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }\n"
"QPushButton:hover { background-color: #545b62; }", nullptr));
        resultGroupBox->setTitle(QString());
        resultGroupBox->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QGroupBox {\n"
"    border: none;\n"
"    background-color: #ffffff;\n"
"    margin: 0px;\n"
"    padding: 0px;\n"
"}", nullptr));
        resultContentArea->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QWidget { background-color: #ffffff; }", nullptr));
        tableView->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QTableView {\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 8px;\n"
"    background-color: white;\n"
"    gridline-color: #f1f3f4;\n"
"    font-size: 11px;\n"
"    selection-background-color: #e3f2fd;\n"
"    alternate-background-color: #fafafa;\n"
"    outline: none;\n"
"}\n"
"QTableView::item {\n"
"    padding: 10px 8px;\n"
"    border-bottom: 1px solid #f1f3f4;\n"
"    border-right: 1px solid #f1f3f4;\n"
"}\n"
"QTableView::item:selected {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4fc3f7, stop:1 #29b6f6);\n"
"    color: white;\n"
"    border: none;\n"
"}\n"
"QTableView::item:hover {\n"
"    background-color: #f5f5f5;\n"
"    border: 1px solid #e0e0e0;\n"
"}\n"
"QTableView::item:alternate {\n"
"    background-color: #fafafa;\n"
"}\n"
"QHeaderView::section {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f5f5f5);\n"
"    border: 1px solid #e0e0e0;\n"
"    border-left: none;\n"
"    padding: 10px 8px;\n"
"    font-weight: 600;\n"
"    fon"
                        "t-size: 11px;\n"
"    color: #424242;\n"
"    text-align: left;\n"
"}\n"
"QHeaderView::section:first {\n"
"    border-left: 1px solid #e0e0e0;\n"
"    border-top-left-radius: 8px;\n"
"}\n"
"QHeaderView::section:last {\n"
"    border-top-right-radius: 8px;\n"
"}\n"
"QHeaderView::section:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f5f5f5, stop:1 #eeeeee);\n"
"}\n"
"/* \345\236\202\347\233\264\346\273\232\345\212\250\346\235\241\346\240\267\345\274\217 */\n"
"QScrollBar:vertical {\n"
"    background-color: #f5f5f5;\n"
"    width: 12px;\n"
"    border-radius: 6px;\n"
"    margin: 0px;\n"
"}\n"
"QScrollBar::handle:vertical {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #d0d0d0, stop:1 #c0c0c0);\n"
"    border-radius: 6px;\n"
"    min-height: 20px;\n"
"    margin: 2px;\n"
"}\n"
"QScrollBar::handle:vertical:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #a0a0a0, stop:1 #909090);\n"
"}\n"
"QScrollBar::handle:vertical:pressed {\n"
"    ba"
                        "ckground: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #808080, stop:1 #707070);\n"
"}\n"
"QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {\n"
"    height: 0px;\n"
"}\n"
"QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {\n"
"    background: transparent;\n"
"}\n"
"/* \346\260\264\345\271\263\346\273\232\345\212\250\346\235\241\346\240\267\345\274\217 */\n"
"QScrollBar:horizontal {\n"
"    background-color: #f5f5f5;\n"
"    height: 12px;\n"
"    border-radius: 6px;\n"
"    margin: 0px;\n"
"}\n"
"QScrollBar::handle:horizontal {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d0d0d0, stop:1 #c0c0c0);\n"
"    border-radius: 6px;\n"
"    min-width: 20px;\n"
"    margin: 2px;\n"
"}\n"
"QScrollBar::handle:horizontal:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a0a0a0, stop:1 #909090);\n"
"}\n"
"QScrollBar::handle:horizontal:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #808080, stop:1 #707070);\n"
"}\n"
"QScr"
                        "ollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {\n"
"    width: 0px;\n"
"}\n"
"QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {\n"
"    background: transparent;\n"
"}", nullptr));
        firstPageButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\246\226\351\241\265", nullptr));
        firstPageButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    font-size: 10px;\n"
"    font-weight: 500;\n"
"    color: #495057;\n"
"    box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);\n"
"    border-color: #adb5bd;\n"
"    transform: translateY(-1px);\n"
"}\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);\n"
"    transform: translateY(0px);\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #f8f9fa;\n"
"    color: #6c757d;\n"
"    border-color: #e9ecef;\n"
"}", nullptr));
        previousPageButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\344\270\212\344\270\200\351\241\265", nullptr));
        previousPageButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    font-size: 10px;\n"
"    font-weight: 500;\n"
"    color: #495057;\n"
"    box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);\n"
"    border-color: #adb5bd;\n"
"    transform: translateY(-1px);\n"
"}\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);\n"
"    transform: translateY(0px);\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #f8f9fa;\n"
"    color: #6c757d;\n"
"    border-color: #e9ecef;\n"
"}", nullptr));
        nextPageButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\344\270\213\344\270\200\351\241\265", nullptr));
        nextPageButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    font-size: 10px;\n"
"    font-weight: 500;\n"
"    color: #495057;\n"
"    box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);\n"
"    border-color: #adb5bd;\n"
"    transform: translateY(-1px);\n"
"}\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);\n"
"    transform: translateY(0px);\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #f8f9fa;\n"
"    color: #6c757d;\n"
"    border-color: #e9ecef;\n"
"}", nullptr));
        lastPageButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\234\253\351\241\265", nullptr));
        lastPageButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    font-size: 10px;\n"
"    font-weight: 500;\n"
"    color: #495057;\n"
"    box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);\n"
"    border-color: #adb5bd;\n"
"    transform: translateY(-1px);\n"
"}\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);\n"
"    transform: translateY(0px);\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #f8f9fa;\n"
"    color: #6c757d;\n"
"    border-color: #e9ecef;\n"
"}", nullptr));
        separator1->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QFrame { background-color: #dee2e6; margin: 0px 10px; }", nullptr));
        goToPageLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\267\263\350\275\254\345\210\260:", nullptr));
        goToPageLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; color: #495057; margin-left: 10px; }", nullptr));
        pageNumberEdit->setPlaceholderText(QCoreApplication::translate("ComprehensiveViewWidget", "\351\241\265\347\240\201", nullptr));
        pageNumberEdit->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLineEdit {\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 8px;\n"
"    font-size: 11px;\n"
"    background-color: white;\n"
"    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QLineEdit:focus {\n"
"    border-color: #007bff;\n"
"    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1), 0 0 0 2px rgba(0,123,255,0.25);\n"
"}\n"
"QLineEdit::placeholder {\n"
"    color: #6c757d;\n"
"    font-size: 10px;\n"
"}", nullptr));
        goToPageButton->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\350\267\263\350\275\254", nullptr));
        goToPageButton->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4dabf7, stop:1 #007bff);\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    font-size: 10px;\n"
"    font-weight: 600;\n"
"    box-shadow: 0 2px 4px rgba(0,123,255,0.3);\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #339af0, stop:1 #0056b3);\n"
"    transform: translateY(-1px);\n"
"    box-shadow: 0 3px 6px rgba(0,123,255,0.4);\n"
"}\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1c7ed6, stop:1 #004085);\n"
"    transform: translateY(0px);\n"
"    box-shadow: 0 1px 2px rgba(0,123,255,0.2);\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #6c757d;\n"
"    box-shadow: none;\n"
"}", nullptr));
        pageInfoLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\347\254\254 1 \351\241\265\357\274\214\345\205\261 1 \351\241\265", nullptr));
        pageInfoLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel {\n"
"    font-size: 11px;\n"
"    color: #495057;\n"
"    margin: 0px 15px;\n"
"    padding: 4px 8px;\n"
"    background-color: #f8f9fa;\n"
"    border-radius: 4px;\n"
"    font-weight: 500;\n"
"}", nullptr));
        recordCountLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\345\205\261 0 \346\235\241\350\256\260\345\275\225", nullptr));
        recordCountLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel {\n"
"    font-size: 11px;\n"
"    color: #28a745;\n"
"    margin: 0px 15px;\n"
"    padding: 4px 8px;\n"
"    background-color: #d4edda;\n"
"    border-radius: 4px;\n"
"    font-weight: 600;\n"
"}", nullptr));
        pageSizeLabel->setText(QCoreApplication::translate("ComprehensiveViewWidget", "\346\257\217\351\241\265\346\230\276\347\244\272:", nullptr));
        pageSizeLabel->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QLabel { font-size: 12px; color: #495057; margin-left: 10px; }", nullptr));
        pageSizeCombo->setItemText(0, QCoreApplication::translate("ComprehensiveViewWidget", "10", nullptr));
        pageSizeCombo->setItemText(1, QCoreApplication::translate("ComprehensiveViewWidget", "20", nullptr));
        pageSizeCombo->setItemText(2, QCoreApplication::translate("ComprehensiveViewWidget", "50", nullptr));
        pageSizeCombo->setItemText(3, QCoreApplication::translate("ComprehensiveViewWidget", "100", nullptr));

        pageSizeCombo->setStyleSheet(QCoreApplication::translate("ComprehensiveViewWidget", "QComboBox {\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 6px 8px;\n"
"    font-size: 10px;\n"
"    background-color: white;\n"
"    font-weight: 500;\n"
"    box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n"
"}\n"
"QComboBox:hover {\n"
"    border-color: #adb5bd;\n"
"}\n"
"QComboBox:focus {\n"
"    border-color: #007bff;\n"
"    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n"
"}\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    width: 20px;\n"
"}\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border: none;\n"
"    width: 0px;\n"
"    height: 0px;\n"
"    border-left: 4px solid transparent;\n"
"    border-right: 4px solid transparent;\n"
"    border-top: 4px solid #495057;\n"
"}", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ComprehensiveViewWidget: public Ui_ComprehensiveViewWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_COMPREHENSIVE_VIEW_H
