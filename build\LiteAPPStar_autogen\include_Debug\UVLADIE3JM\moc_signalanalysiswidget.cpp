/****************************************************************************
** Meta object code from reading C++ file 'signalanalysiswidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/signalanalysiswidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'signalanalysiswidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SignalAnalysisWidget_t {
    QByteArrayData data[28];
    char stringdata0[456];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SignalAnalysisWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SignalAnalysisWidget_t qt_meta_stringdata_SignalAnalysisWidget = {
    {
QT_MOC_LITERAL(0, 0, 20), // "SignalAnalysisWidget"
QT_MOC_LITERAL(1, 21, 20), // "analysisStateChanged"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 7), // "running"
QT_MOC_LITERAL(4, 51, 21), // "frequencyRangeChanged"
QT_MOC_LITERAL(5, 73, 10), // "centerFreq"
QT_MOC_LITERAL(6, 84, 9), // "bandwidth"
QT_MOC_LITERAL(7, 94, 11), // "dataUpdated"
QT_MOC_LITERAL(8, 106, 10), // "updateData"
QT_MOC_LITERAL(9, 117, 28), // "onCenterFrequencyTextChanged"
QT_MOC_LITERAL(10, 146, 28), // "onCenterFrequencyUnitChanged"
QT_MOC_LITERAL(11, 175, 17), // "onSpanTextChanged"
QT_MOC_LITERAL(12, 193, 17), // "onSpanUnitChanged"
QT_MOC_LITERAL(13, 211, 31), // "onSpectrumFrequencyRangeChanged"
QT_MOC_LITERAL(14, 243, 9), // "startFreq"
QT_MOC_LITERAL(15, 253, 7), // "endFreq"
QT_MOC_LITERAL(16, 261, 32), // "onWaterfallFrequencyRangeChanged"
QT_MOC_LITERAL(17, 294, 11), // "onPresetISM"
QT_MOC_LITERAL(18, 306, 12), // "onPresetWiFi"
QT_MOC_LITERAL(19, 319, 10), // "onPresetFM"
QT_MOC_LITERAL(20, 330, 20), // "onDynamicDataToggled"
QT_MOC_LITERAL(21, 351, 7), // "enabled"
QT_MOC_LITERAL(22, 359, 20), // "onTableColumnResized"
QT_MOC_LITERAL(23, 380, 12), // "logicalIndex"
QT_MOC_LITERAL(24, 393, 7), // "oldSize"
QT_MOC_LITERAL(25, 401, 7), // "newSize"
QT_MOC_LITERAL(26, 409, 23), // "adjustTableColumnWidths"
QT_MOC_LITERAL(27, 433, 22) // "resetLayoutProportions"

    },
    "SignalAnalysisWidget\0analysisStateChanged\0"
    "\0running\0frequencyRangeChanged\0"
    "centerFreq\0bandwidth\0dataUpdated\0"
    "updateData\0onCenterFrequencyTextChanged\0"
    "onCenterFrequencyUnitChanged\0"
    "onSpanTextChanged\0onSpanUnitChanged\0"
    "onSpectrumFrequencyRangeChanged\0"
    "startFreq\0endFreq\0onWaterfallFrequencyRangeChanged\0"
    "onPresetISM\0onPresetWiFi\0onPresetFM\0"
    "onDynamicDataToggled\0enabled\0"
    "onTableColumnResized\0logicalIndex\0"
    "oldSize\0newSize\0adjustTableColumnWidths\0"
    "resetLayoutProportions"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SignalAnalysisWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      17,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   99,    2, 0x06 /* Public */,
       4,    2,  102,    2, 0x06 /* Public */,
       7,    0,  107,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,  108,    2, 0x08 /* Private */,
       9,    0,  109,    2, 0x08 /* Private */,
      10,    0,  110,    2, 0x08 /* Private */,
      11,    0,  111,    2, 0x08 /* Private */,
      12,    0,  112,    2, 0x08 /* Private */,
      13,    2,  113,    2, 0x08 /* Private */,
      16,    2,  118,    2, 0x08 /* Private */,
      17,    0,  123,    2, 0x08 /* Private */,
      18,    0,  124,    2, 0x08 /* Private */,
      19,    0,  125,    2, 0x08 /* Private */,
      20,    1,  126,    2, 0x08 /* Private */,
      22,    3,  129,    2, 0x08 /* Private */,
      26,    0,  136,    2, 0x08 /* Private */,
      27,    0,  137,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    5,    6,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,   14,   15,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,   14,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   21,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,   23,   24,   25,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void SignalAnalysisWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SignalAnalysisWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->analysisStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->frequencyRangeChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 2: _t->dataUpdated(); break;
        case 3: _t->updateData(); break;
        case 4: _t->onCenterFrequencyTextChanged(); break;
        case 5: _t->onCenterFrequencyUnitChanged(); break;
        case 6: _t->onSpanTextChanged(); break;
        case 7: _t->onSpanUnitChanged(); break;
        case 8: _t->onSpectrumFrequencyRangeChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 9: _t->onWaterfallFrequencyRangeChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 10: _t->onPresetISM(); break;
        case 11: _t->onPresetWiFi(); break;
        case 12: _t->onPresetFM(); break;
        case 13: _t->onDynamicDataToggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 14: _t->onTableColumnResized((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 15: _t->adjustTableColumnWidths(); break;
        case 16: _t->resetLayoutProportions(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SignalAnalysisWidget::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SignalAnalysisWidget::analysisStateChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SignalAnalysisWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SignalAnalysisWidget::frequencyRangeChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SignalAnalysisWidget::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SignalAnalysisWidget::dataUpdated)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SignalAnalysisWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_SignalAnalysisWidget.data,
    qt_meta_data_SignalAnalysisWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SignalAnalysisWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SignalAnalysisWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SignalAnalysisWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int SignalAnalysisWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 17)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 17;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 17)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 17;
    }
    return _id;
}

// SIGNAL 0
void SignalAnalysisWidget::analysisStateChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void SignalAnalysisWidget::frequencyRangeChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void SignalAnalysisWidget::dataUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
