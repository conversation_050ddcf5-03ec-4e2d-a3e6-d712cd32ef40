#include <windows.h>

IDI_ICON1 ICON DISCARDABLE "icons/app_icon.png"

VS_VERSION_INFO VERSIONINFO
FILEVERSION 1,0,0,0
PRODUCTVERSION 1,0,0,0
FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
FILEFLAGS 0x1L
#else
FILEFLAGS 0x0L
#endif
FILEOS 0x40004L
FILETYPE 0x1L
FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "CompanyName", "LiteAPPStar Team"
            VALUE "FileDescription", "跨平台桌面应用程序"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "LiteAPPStar"
            VALUE "LegalCopyright", "Copyright (c) 2024 LiteAPPStar Team"
            VALUE "OriginalFilename", "LiteAPPStar.exe"
            VALUE "ProductName", "LiteAPPStar"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END
