/**
 * LiteAPPStar 视口边界修正测试示例
 * 
 * 本文件展示如何测试修正后的视口边界限制功能
 */

#include "qgismapwidget.h"
#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QSpinBox>
#include <QDebug>

class ViewportBoundaryTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    ViewportBoundaryTestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        connectSignals();
    }

private slots:
    void testNorthPole()
    {
        qDebug() << "=== 测试北极区域 ===";
        m_mapWidget->setCenter(89.0, 0.0);
        updateStatusLabel();
    }
    
    void testSouthPole()
    {
        qDebug() << "=== 测试南极区域 ===";
        m_mapWidget->setCenter(-89.0, 0.0);
        updateStatusLabel();
    }
    
    void testHighLatitude()
    {
        qDebug() << "=== 测试高纬度区域 ===";
        m_mapWidget->setCenter(85.0, 116.4074);
        updateStatusLabel();
    }
    
    void testLowLatitude()
    {
        qDebug() << "=== 测试低纬度区域 ===";
        m_mapWidget->setCenter(-85.0, 116.4074);
        updateStatusLabel();
    }
    
    void onZoomChanged(int zoom)
    {
        m_zoomLabel->setText(QString("缩放级别: %1").arg(zoom));
        updateStatusLabel();
        
        // 显示当前缩放级别下的视口信息
        if (m_mapWidget && m_mapWidget->getTileMapView())
        {
            auto center = m_mapWidget->getTileMapView()->getCenter();
            qDebug() << "缩放级别变更到" << zoom << "当前中心:" << center.first << "," << center.second;
        }
    }
    
    void onCenterChanged(double lat, double lng)
    {
        m_centerLabel->setText(QString("中心: %1, %2").arg(lat, 0, 'f', 4).arg(lng, 0, 'f', 4));
        
        // 检查是否接近边界
        if (qAbs(lat) > 85.0)
        {
            m_statusLabel->setText("状态: 接近极地边界");
            m_statusLabel->setStyleSheet("color: orange;");
        }
        else if (qAbs(lat) > 80.0)
        {
            m_statusLabel->setText("状态: 高纬度区域");
            m_statusLabel->setStyleSheet("color: blue;");
        }
        else
        {
            m_statusLabel->setText("状态: 正常区域");
            m_statusLabel->setStyleSheet("color: green;");
        }
    }
    
    void runBoundaryTest()
    {
        qDebug() << "=== 运行完整边界测试 ===";
        m_mapWidget->testViewportBoundaries();
    }
    
    void runConstraintTest()
    {
        qDebug() << "=== 运行约束测试 ===";
        m_mapWidget->testBoundaryConstraints();
    }
    
    void onZoomSliderChanged(int value)
    {
        if (m_mapWidget && m_mapWidget->getTileMapView())
        {
            m_mapWidget->getTileMapView()->setZoomLevel(value);
        }
    }

private:
    void setupUI()
    {
        auto *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        auto *mainLayout = new QVBoxLayout(centralWidget);
        
        // 创建地图组件
        m_mapWidget = new QgisMapWidget(this);
        mainLayout->addWidget(m_mapWidget);
        
        // 创建控制面板
        auto *controlPanel = new QWidget(this);
        controlPanel->setMaximumHeight(150);
        controlPanel->setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;");
        mainLayout->addWidget(controlPanel);
        
        auto *controlLayout = new QVBoxLayout(controlPanel);
        
        // 状态显示
        auto *statusLayout = new QHBoxLayout();
        m_centerLabel = new QLabel("中心: 0.0, 0.0", this);
        m_zoomLabel = new QLabel("缩放级别: 4", this);
        m_statusLabel = new QLabel("状态: 正常区域", this);
        m_statusLabel->setStyleSheet("color: green;");
        
        statusLayout->addWidget(m_centerLabel);
        statusLayout->addWidget(m_zoomLabel);
        statusLayout->addWidget(m_statusLabel);
        statusLayout->addStretch();
        
        controlLayout->addLayout(statusLayout);
        
        // 缩放控制
        auto *zoomLayout = new QHBoxLayout();
        zoomLayout->addWidget(new QLabel("缩放:", this));
        
        m_zoomSlider = new QSlider(Qt::Horizontal, this);
        m_zoomSlider->setRange(4, 18);
        m_zoomSlider->setValue(4);
        zoomLayout->addWidget(m_zoomSlider);
        
        auto *zoomSpinBox = new QSpinBox(this);
        zoomSpinBox->setRange(4, 18);
        zoomSpinBox->setValue(4);
        zoomLayout->addWidget(zoomSpinBox);
        
        controlLayout->addLayout(zoomLayout);
        
        // 测试按钮
        auto *buttonLayout = new QHBoxLayout();
        
        auto *northPoleBtn = new QPushButton("北极测试", this);
        auto *southPoleBtn = new QPushButton("南极测试", this);
        auto *highLatBtn = new QPushButton("高纬度测试", this);
        auto *lowLatBtn = new QPushButton("低纬度测试", this);
        auto *boundaryTestBtn = new QPushButton("边界测试", this);
        auto *constraintTestBtn = new QPushButton("约束测试", this);
        
        buttonLayout->addWidget(northPoleBtn);
        buttonLayout->addWidget(southPoleBtn);
        buttonLayout->addWidget(highLatBtn);
        buttonLayout->addWidget(lowLatBtn);
        buttonLayout->addWidget(boundaryTestBtn);
        buttonLayout->addWidget(constraintTestBtn);
        
        controlLayout->addLayout(buttonLayout);
        
        // 连接信号
        connect(northPoleBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::testNorthPole);
        connect(southPoleBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::testSouthPole);
        connect(highLatBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::testHighLatitude);
        connect(lowLatBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::testLowLatitude);
        connect(boundaryTestBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::runBoundaryTest);
        connect(constraintTestBtn, &QPushButton::clicked, this, &ViewportBoundaryTestWindow::runConstraintTest);
        
        connect(m_zoomSlider, &QSlider::valueChanged, this, &ViewportBoundaryTestWindow::onZoomSliderChanged);
        connect(m_zoomSlider, &QSlider::valueChanged, zoomSpinBox, &QSpinBox::setValue);
        connect(zoomSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), m_zoomSlider, &QSlider::setValue);
        
        setWindowTitle("LiteAPPStar 视口边界修正测试");
        resize(1200, 900);
    }
    
    void connectSignals()
    {
        // 连接地图组件的信号
        if (m_mapWidget && m_mapWidget->getTileMapView())
        {
            connect(m_mapWidget->getTileMapView(), &TileMapView::centerChanged,
                    this, &ViewportBoundaryTestWindow::onCenterChanged);
            connect(m_mapWidget->getTileMapView(), &TileMapView::zoomChanged,
                    this, &ViewportBoundaryTestWindow::onZoomChanged);
        }
    }
    
    void updateStatusLabel()
    {
        if (m_mapWidget && m_mapWidget->getTileMapView())
        {
            auto center = m_mapWidget->getTileMapView()->getCenter();
            int zoom = m_mapWidget->getTileMapView()->getZoomLevel();
            
            qDebug() << "当前状态 - 中心:" << center.first << "," << center.second 
                     << "缩放:" << zoom;
        }
    }

private:
    QgisMapWidget *m_mapWidget;
    QLabel *m_centerLabel;
    QLabel *m_zoomLabel;
    QLabel *m_statusLabel;
    QSlider *m_zoomSlider;
};

// 主函数
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ViewportBoundaryTestWindow window;
    window.show();
    
    return app.exec();
}

#include "VIEWPORT_BOUNDARY_TEST.moc"

/**
 * 测试说明：
 * 
 * 1. 编译运行此测试程序
 * 2. 使用各种测试按钮测试边界限制
 * 3. 拖拽地图到极地区域，观察是否出现空白
 * 4. 调整缩放级别，测试不同缩放下的边界
 * 5. 观察控制台输出的调试信息
 * 
 * 预期效果：
 * - 任何情况下都不会出现空白边界
 * - 拖拽到边界时有自然的阻力感
 * - 缩放后自动调整中心点避免空白
 * - 状态标签正确显示当前区域类型
 * 
 * 修正前后对比：
 * - 修正前：会出现灰色空白区域
 * - 修正后：始终显示有效的地图内容
 */
