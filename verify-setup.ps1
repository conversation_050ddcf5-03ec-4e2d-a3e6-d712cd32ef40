# LiteAPPStar 环境验证脚本
Write-Host "=== LiteAPPStar 环境验证 ===" -ForegroundColor Cyan

$allGood = $true

# 1. 检查Qt安装
Write-Host "`n1. 检查Qt安装..." -ForegroundColor Yellow
$QT_DIR = "D:\Qt\Qt5.14.2\5.14.2\msvc2017_64"
if (Test-Path "$QT_DIR\bin\qmake.exe") {
    Write-Host "   ✅ Qt5.14.2 已安装" -ForegroundColor Green
} else {
    Write-Host "   ❌ Qt5.14.2 未找到" -ForegroundColor Red
    $allGood = $false
}

# 2. 检查CMake
Write-Host "`n2. 检查CMake..." -ForegroundColor Yellow
try {
    $cmakeVersion = & cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ CMake 已安装" -ForegroundColor Green
    } else {
        Write-Host "   ❌ CMake 未安装或不在PATH中" -ForegroundColor Red
        $allGood = $false
    }
} catch {
    Write-Host "   ❌ CMake 未安装或不在PATH中" -ForegroundColor Red
    $allGood = $false
}

# 3. 检查Visual Studio
Write-Host "`n3. 检查Visual Studio..." -ForegroundColor Yellow
$vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC"
if (Test-Path $vsPath) {
    Write-Host "   ✅ Visual Studio 2022 已安装" -ForegroundColor Green
} else {
    $vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC"
    if (Test-Path $vsPath) {
        Write-Host "   ✅ Visual Studio 2022 Professional 已安装" -ForegroundColor Green
    } else {
        $vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC"
        if (Test-Path $vsPath) {
            Write-Host "   ✅ Visual Studio 2022 Community 已安装" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Visual Studio 2022 未找到" -ForegroundColor Red
            $allGood = $false
        }
    }
}

# 4. 检查构建文件
Write-Host "`n4. 检查构建状态..." -ForegroundColor Yellow
if (Test-Path "build\bin\Debug\LiteAPPStar.exe") {
    Write-Host "   ✅ 可执行文件已生成" -ForegroundColor Green
    $fileInfo = Get-Item "build\bin\Debug\LiteAPPStar.exe"
    Write-Host "   📁 文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
    Write-Host "   📅 修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "   ❌ 可执行文件未找到，需要编译" -ForegroundColor Red
    $allGood = $false
}

# 5. 检查VSCode配置
Write-Host "`n5. 检查VSCode配置..." -ForegroundColor Yellow
if (Test-Path ".vscode\launch.json") {
    Write-Host "   ✅ 调试配置文件存在" -ForegroundColor Green
} else {
    Write-Host "   ❌ 调试配置文件缺失" -ForegroundColor Red
    $allGood = $false
}

if (Test-Path ".vscode\tasks.json") {
    Write-Host "   ✅ 构建任务配置存在" -ForegroundColor Green
} else {
    Write-Host "   ❌ 构建任务配置缺失" -ForegroundColor Red
    $allGood = $false
}

# 总结
Write-Host "`n=== 验证结果 ===" -ForegroundColor Cyan
if ($allGood) {
    Write-Host "🎉 所有检查通过！可以使用F5进行调试。" -ForegroundColor Green
    Write-Host "`n📋 下一步操作：" -ForegroundColor Yellow
    Write-Host "   1. 在VSCode中打开项目" -ForegroundColor White
    Write-Host "   2. 按F5开始调试" -ForegroundColor White
    Write-Host "   3. 选择 'Debug LiteAPPStar (CMake - VS2022)' 配置" -ForegroundColor White
} else {
    Write-Host "⚠️  发现问题，请解决后重新验证。" -ForegroundColor Red
    Write-Host "`n🔧 建议操作：" -ForegroundColor Yellow
    Write-Host "   1. 安装缺失的组件" -ForegroundColor White
    Write-Host "   2. 运行 .\quick-build.ps1 编译项目" -ForegroundColor White
    Write-Host "   3. 重新运行此验证脚本" -ForegroundColor White
}

Read-Host "`nPress Enter to exit"
