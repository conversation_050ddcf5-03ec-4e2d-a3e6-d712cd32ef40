/****************************************************************************
** Meta object code from reading C++ file 'geoinfowidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/geoinfowidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'geoinfowidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_GeoInfoWidget_t {
    QByteArrayData data[26];
    char stringdata0[376];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_GeoInfoWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_GeoInfoWidget_t qt_meta_stringdata_GeoInfoWidget = {
    {
QT_MOC_LITERAL(0, 0, 13), // "GeoInfoWidget"
QT_MOC_LITERAL(1, 14, 17), // "coordinateChanged"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 3), // "lat"
QT_MOC_LITERAL(4, 37, 3), // "lng"
QT_MOC_LITERAL(5, 41, 22), // "mouseCoordinateChanged"
QT_MOC_LITERAL(6, 64, 16), // "zoomLevelChanged"
QT_MOC_LITERAL(7, 81, 4), // "zoom"
QT_MOC_LITERAL(8, 86, 13), // "statusChanged"
QT_MOC_LITERAL(9, 100, 6), // "status"
QT_MOC_LITERAL(10, 107, 16), // "jumpToCoordinate"
QT_MOC_LITERAL(11, 124, 8), // "latitude"
QT_MOC_LITERAL(12, 133, 9), // "longitude"
QT_MOC_LITERAL(13, 143, 18), // "getCurrentLocation"
QT_MOC_LITERAL(14, 162, 14), // "switchMapLayer"
QT_MOC_LITERAL(15, 177, 9), // "layerType"
QT_MOC_LITERAL(16, 187, 12), // "setZoomLevel"
QT_MOC_LITERAL(17, 200, 9), // "zoomLevel"
QT_MOC_LITERAL(18, 210, 22), // "onCoordinateInputEnter"
QT_MOC_LITERAL(19, 233, 23), // "onLocationButtonClicked"
QT_MOC_LITERAL(20, 257, 14), // "onLayerChanged"
QT_MOC_LITERAL(21, 272, 19), // "updateMousePosition"
QT_MOC_LITERAL(22, 292, 22), // "onMapCoordinateClicked"
QT_MOC_LITERAL(23, 315, 18), // "onMapCenterChanged"
QT_MOC_LITERAL(24, 334, 16), // "onMapZoomChanged"
QT_MOC_LITERAL(25, 351, 24) // "onMouseCoordinateChanged"

    },
    "GeoInfoWidget\0coordinateChanged\0\0lat\0"
    "lng\0mouseCoordinateChanged\0zoomLevelChanged\0"
    "zoom\0statusChanged\0status\0jumpToCoordinate\0"
    "latitude\0longitude\0getCurrentLocation\0"
    "switchMapLayer\0layerType\0setZoomLevel\0"
    "zoomLevel\0onCoordinateInputEnter\0"
    "onLocationButtonClicked\0onLayerChanged\0"
    "updateMousePosition\0onMapCoordinateClicked\0"
    "onMapCenterChanged\0onMapZoomChanged\0"
    "onMouseCoordinateChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_GeoInfoWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   94,    2, 0x06 /* Public */,
       5,    2,   99,    2, 0x06 /* Public */,
       6,    1,  104,    2, 0x06 /* Public */,
       8,    1,  107,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    2,  110,    2, 0x0a /* Public */,
      13,    0,  115,    2, 0x0a /* Public */,
      14,    1,  116,    2, 0x0a /* Public */,
      16,    1,  119,    2, 0x0a /* Public */,
      18,    0,  122,    2, 0x08 /* Private */,
      19,    0,  123,    2, 0x08 /* Private */,
      20,    0,  124,    2, 0x08 /* Private */,
      21,    0,  125,    2, 0x08 /* Private */,
      22,    2,  126,    2, 0x08 /* Private */,
      23,    2,  131,    2, 0x08 /* Private */,
      24,    1,  136,    2, 0x08 /* Private */,
      25,    2,  139,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void, QMetaType::QString,    9,

 // slots: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,   11,   12,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::Int,   17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,

       0        // eod
};

void GeoInfoWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<GeoInfoWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->coordinateChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->mouseCoordinateChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 2: _t->zoomLevelChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 3: _t->statusChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->jumpToCoordinate((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 5: _t->getCurrentLocation(); break;
        case 6: _t->switchMapLayer((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->setZoomLevel((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 8: _t->onCoordinateInputEnter(); break;
        case 9: _t->onLocationButtonClicked(); break;
        case 10: _t->onLayerChanged(); break;
        case 11: _t->updateMousePosition(); break;
        case 12: _t->onMapCoordinateClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 13: _t->onMapCenterChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 14: _t->onMapZoomChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 15: _t->onMouseCoordinateChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (GeoInfoWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeoInfoWidget::coordinateChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (GeoInfoWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeoInfoWidget::mouseCoordinateChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (GeoInfoWidget::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeoInfoWidget::zoomLevelChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (GeoInfoWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GeoInfoWidget::statusChanged)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject GeoInfoWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_GeoInfoWidget.data,
    qt_meta_data_GeoInfoWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *GeoInfoWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *GeoInfoWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_GeoInfoWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int GeoInfoWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void GeoInfoWidget::coordinateChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void GeoInfoWidget::mouseCoordinateChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void GeoInfoWidget::zoomLevelChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void GeoInfoWidget::statusChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
