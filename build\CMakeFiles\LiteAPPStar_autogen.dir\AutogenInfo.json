{"BUILD_DIR": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen", "CMAKE_BINARY_DIR": "E:/code/LiteAPPStar/build", "CMAKE_CURRENT_BINARY_DIR": "E:/code/LiteAPPStar/build", "CMAKE_CURRENT_SOURCE_DIR": "E:/code/LiteAPPStar", "CMAKE_EXECUTABLE": "D:/Program Files/cmake-3.28.1-windows-x86_64/bin/cmake.exe", "CMAKE_LIST_FILES": ["E:/code/LiteAPPStar/CMakeLists.txt", "E:/code/LiteAPPStar/build/CMakeFiles/3.28.1/CMakeSystem.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake", "E:/code/LiteAPPStar/build/CMakeFiles/3.28.1/CMakeCXXCompiler.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/MSVC-CXX.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/MSVC.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-MSVC-CXX.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/Platform/Windows-MSVC.cmake", "E:/code/LiteAPPStar/build/CMakeFiles/3.28.1/CMakeRCCompiler.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeRCInformation.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeParseArguments.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CMakeParseArguments.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QuickWidgets/Qt5QuickWidgetsConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5QuickWidgets/Qt5QuickWidgetsConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Svg/Qt5SvgConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Svg/Qt5SvgConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfigVersion.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5SqlConfig.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QODBCDriverPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QPSQLDriverPlugin.cmake", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql/Qt5Sql_QSQLiteDriverPlugin.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CPack.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Modules/CPackComponent.cmake", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Templates/CPackConfig.cmake.in", "D:/Program Files/cmake-3.28.1-windows-x86_64/share/cmake-3.28/Templates/CPackConfig.cmake.in", "E:/code/LiteAPPStar/resources/resources.qrc"], "CMAKE_SOURCE_DIR": "E:/code/LiteAPPStar", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["E:/code/LiteAPPStar/include/comprehensiveviewwidget.h", "MU", "6YEA5652QU/moc_comprehensiveviewwidget.cpp", null], ["E:/code/LiteAPPStar/include/configmanager.h", "MU", "6YEA5652QU/moc_configmanager.cpp", null], ["E:/code/LiteAPPStar/include/databasemanager.h", "MU", "6YEA5652QU/moc_databasemanager.cpp", null], ["E:/code/LiteAPPStar/include/geographicdatapanel.h", "MU", "6YEA5652QU/moc_geographicdatapanel.cpp", null], ["E:/code/LiteAPPStar/include/mainwindow.h", "MU", "6YEA5652QU/moc_mainwindow.cpp", null], ["E:/code/LiteAPPStar/src/geoinfowidget.h", "MU", "UVLADIE3JM/moc_geoinfowidget.cpp", null], ["E:/code/LiteAPPStar/src/localtilemanager.h", "MU", "UVLADIE3JM/moc_localtilemanager.cpp", null], ["E:/code/LiteAPPStar/src/qgismapwidget.h", "MU", "UVLADIE3JM/moc_qgismapwidget.cpp", null], ["E:/code/LiteAPPStar/src/qtgeoinfowidget.h", "MU", "UVLADIE3JM/moc_qtgeoinfowidget.cpp", null], ["E:/code/LiteAPPStar/src/qtlocationmapview.h", "MU", "UVLADIE3JM/moc_qtlocationmapview.cpp", null], ["E:/code/LiteAPPStar/src/signalanalysiswidget.h", "MU", "UVLADIE3JM/moc_signalanalysiswidget.cpp", null], ["E:/code/LiteAPPStar/src/spectrumplot_simple.h", "MU", "UVLADIE3JM/moc_spectrumplot_simple.cpp", null], ["E:/code/LiteAPPStar/src/tilemapview.h", "MU", "UVLADIE3JM/moc_tilemapview.cpp", null], ["E:/code/LiteAPPStar/src/waterfallplot_simple.h", "MU", "UVLADIE3JM/moc_waterfallplot_simple.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include", "INCLUDE_DIR_Debug": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/include_Release", "MOC_COMPILATION_FILE": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "E:/code/LiteAPPStar/build/LiteAPPStar_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_QMLMODELS_LIB", "QT_QML_LIB", "QT_QUICKWIDGETS_LIB", "QT_QUICK_LIB", "QT_SQL_LIB", "QT_SVG_LIB", "QT_WIDGETS_LIB", "WIN32", "_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS", "_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QMLMODELS_LIB", "QT_QML_LIB", "QT_QUICKWIDGETS_LIB", "QT_QUICK_LIB", "QT_SQL_LIB", "QT_SVG_LIB", "QT_WIDGETS_LIB", "WIN32", "_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS", "_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QMLMODELS_LIB", "QT_QML_LIB", "QT_QUICKWIDGETS_LIB", "QT_QUICK_LIB", "QT_SQL_LIB", "QT_SVG_LIB", "QT_WIDGETS_LIB", "WIN32", "_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS", "_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_QMLMODELS_LIB", "QT_QML_LIB", "QT_QUICKWIDGETS_LIB", "QT_QUICK_LIB", "QT_SQL_LIB", "QT_SVG_LIB", "QT_WIDGETS_LIB", "WIN32", "_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS", "_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["E:/code/LiteAPPStar/include", "E:/code/LiteAPPStar/build", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"], "MOC_INCLUDES_MinSizeRel": ["E:/code/LiteAPPStar/include", "E:/code/LiteAPPStar/build", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"], "MOC_INCLUDES_RelWithDebInfo": ["E:/code/LiteAPPStar/include", "E:/code/LiteAPPStar/build", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"], "MOC_INCLUDES_Release": ["E:/code/LiteAPPStar/include", "E:/code/LiteAPPStar/build", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg", "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 6, "PARSE_CACHE_FILE": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 14, "SETTINGS_FILE": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["E:/code/LiteAPPStar/src/comprehensiveviewwidget.cpp", "MU", null], ["E:/code/LiteAPPStar/src/configmanager.cpp", "MU", null], ["E:/code/LiteAPPStar/src/databasemanager.cpp", "MU", null], ["E:/code/LiteAPPStar/src/geographicdatapanel.cpp", "MU", null], ["E:/code/LiteAPPStar/src/geoinfowidget.cpp", "MU", null], ["E:/code/LiteAPPStar/src/localtilemanager.cpp", "MU", null], ["E:/code/LiteAPPStar/src/main.cpp", "MU", null], ["E:/code/LiteAPPStar/src/mainwindow.cpp", "MU", null], ["E:/code/LiteAPPStar/src/qgismapwidget.cpp", "MU", null], ["E:/code/LiteAPPStar/src/qtgeoinfowidget.cpp", "MU", null], ["E:/code/LiteAPPStar/src/qtlocationmapview.cpp", "MU", null], ["E:/code/LiteAPPStar/src/signalanalysiswidget.cpp", "MU", null], ["E:/code/LiteAPPStar/src/spectrumplot_simple.cpp", "MU", null], ["E:/code/LiteAPPStar/src/tilemapview.cpp", "MU", null], ["E:/code/LiteAPPStar/src/waterfallplot_simple.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}