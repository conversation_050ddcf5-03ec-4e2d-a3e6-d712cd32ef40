/****************************************************************************
** Meta object code from reading C++ file 'comprehensiveviewwidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../include/comprehensiveviewwidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'comprehensiveviewwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ComprehensiveViewWidget_t {
    QByteArrayData data[22];
    char stringdata0[375];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ComprehensiveViewWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ComprehensiveViewWidget_t qt_meta_stringdata_ComprehensiveViewWidget = {
    {
QT_MOC_LITERAL(0, 0, 23), // "ComprehensiveViewWidget"
QT_MOC_LITERAL(1, 24, 20), // "onQueryButtonClicked"
QT_MOC_LITERAL(2, 45, 0), // ""
QT_MOC_LITERAL(3, 46, 20), // "onResetButtonClicked"
QT_MOC_LITERAL(4, 67, 22), // "onRefreshButtonClicked"
QT_MOC_LITERAL(5, 90, 21), // "onExportButtonClicked"
QT_MOC_LITERAL(6, 112, 18), // "onFirstPageClicked"
QT_MOC_LITERAL(7, 131, 21), // "onPreviousPageClicked"
QT_MOC_LITERAL(8, 153, 17), // "onNextPageClicked"
QT_MOC_LITERAL(9, 171, 17), // "onLastPageClicked"
QT_MOC_LITERAL(10, 189, 17), // "onPageSizeChanged"
QT_MOC_LITERAL(11, 207, 8), // "pageSize"
QT_MOC_LITERAL(12, 216, 17), // "onGoToPageClicked"
QT_MOC_LITERAL(13, 234, 15), // "onHeaderClicked"
QT_MOC_LITERAL(14, 250, 12), // "logicalIndex"
QT_MOC_LITERAL(15, 263, 18), // "onTableContextMenu"
QT_MOC_LITERAL(16, 282, 3), // "pos"
QT_MOC_LITERAL(17, 286, 25), // "onColumnVisibilityChanged"
QT_MOC_LITERAL(18, 312, 20), // "onColumnOrderChanged"
QT_MOC_LITERAL(19, 333, 19), // "onDatabaseConnected"
QT_MOC_LITERAL(20, 353, 15), // "onDatabaseError"
QT_MOC_LITERAL(21, 369, 5) // "error"

    },
    "ComprehensiveViewWidget\0onQueryButtonClicked\0"
    "\0onResetButtonClicked\0onRefreshButtonClicked\0"
    "onExportButtonClicked\0onFirstPageClicked\0"
    "onPreviousPageClicked\0onNextPageClicked\0"
    "onLastPageClicked\0onPageSizeChanged\0"
    "pageSize\0onGoToPageClicked\0onHeaderClicked\0"
    "logicalIndex\0onTableContextMenu\0pos\0"
    "onColumnVisibilityChanged\0"
    "onColumnOrderChanged\0onDatabaseConnected\0"
    "onDatabaseError\0error"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ComprehensiveViewWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   94,    2, 0x08 /* Private */,
       3,    0,   95,    2, 0x08 /* Private */,
       4,    0,   96,    2, 0x08 /* Private */,
       5,    0,   97,    2, 0x08 /* Private */,
       6,    0,   98,    2, 0x08 /* Private */,
       7,    0,   99,    2, 0x08 /* Private */,
       8,    0,  100,    2, 0x08 /* Private */,
       9,    0,  101,    2, 0x08 /* Private */,
      10,    1,  102,    2, 0x08 /* Private */,
      12,    0,  105,    2, 0x08 /* Private */,
      13,    1,  106,    2, 0x08 /* Private */,
      15,    1,  109,    2, 0x08 /* Private */,
      17,    0,  112,    2, 0x08 /* Private */,
      18,    0,  113,    2, 0x08 /* Private */,
      19,    0,  114,    2, 0x08 /* Private */,
      20,    1,  115,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   14,
    QMetaType::Void, QMetaType::QPoint,   16,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   21,

       0        // eod
};

void ComprehensiveViewWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ComprehensiveViewWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onQueryButtonClicked(); break;
        case 1: _t->onResetButtonClicked(); break;
        case 2: _t->onRefreshButtonClicked(); break;
        case 3: _t->onExportButtonClicked(); break;
        case 4: _t->onFirstPageClicked(); break;
        case 5: _t->onPreviousPageClicked(); break;
        case 6: _t->onNextPageClicked(); break;
        case 7: _t->onLastPageClicked(); break;
        case 8: _t->onPageSizeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 9: _t->onGoToPageClicked(); break;
        case 10: _t->onHeaderClicked((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->onTableContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 12: _t->onColumnVisibilityChanged(); break;
        case 13: _t->onColumnOrderChanged(); break;
        case 14: _t->onDatabaseConnected(); break;
        case 15: _t->onDatabaseError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ComprehensiveViewWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ComprehensiveViewWidget.data,
    qt_meta_data_ComprehensiveViewWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ComprehensiveViewWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ComprehensiveViewWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ComprehensiveViewWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ComprehensiveViewWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
