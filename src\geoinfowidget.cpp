#include "geoinfowidget.h"
#include <QApplication>
#include <QMessageBox>
#include <QDebug>
#include <QUrl>
#include <QtMath>

// 常量定义
const double GeoInfoWidget::DEFAULT_LAT = 39.9042; // 北京天安门
const double GeoInfoWidget::DEFAULT_LNG = 116.4074;
const int GeoInfoWidget::DEFAULT_ZOOM = 4;

GeoInfoWidget::GeoInfoWidget(QWidget *parent)
    : QWidget(parent),
      m_mainLayout(nullptr),
      // m_splitter(nullptr),  // 已移除分割器
      m_mapContainer(nullptr),
      m_mapView(nullptr),
      m_controlPanel(nullptr),
      m_locationBtn(nullptr),
      m_layerCombo(nullptr),
      m_latInput(nullptr),
      m_lngInput(nullptr),
      m_jumpBtn(nullptr),
      m_infoPanel(nullptr),
      m_coordSystemLabel(nullptr),
      m_currentCoordLabel(nullptr),
      m_zoomLevelLabel(nullptr),
      m_scaleLabel(nullptr),
      m_mouseCoordLabel(nullptr),
      m_updateTimer(nullptr),
      m_currentLat(DEFAULT_LAT),
      m_currentLng(DEFAULT_LNG),
      m_currentZoom(DEFAULT_ZOOM),
      m_currentLayer("roadmap"),
      m_mapControlPanel(nullptr),
      m_mapInfoPanel(nullptr),
      m_isOnlineMode(true),
      m_lastError(""),
      m_statusLabel(nullptr),
      m_zoomControlWidget(nullptr)
{
    initializeUI();
    connectSignals();
    initializeMap();
}

GeoInfoWidget::~GeoInfoWidget()
{
    if (m_updateTimer)
    {
        m_updateTimer->stop();
        delete m_updateTimer;
    }
}

void GeoInfoWidget::initializeUI()
{
    // 创建主布局 - 直接使用地图容器
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 移除分割器，直接创建地图区域
    // m_splitter = new QSplitter(Qt::Horizontal, this);
    // m_mainLayout->addWidget(m_splitter);

    // 创建地图区域
    createMapArea();

    // 直接将地图容器添加到主布局，占据整个窗口
    m_mainLayout->addWidget(m_mapContainer);

    // 设置样式
    setStyleSheet(R"(
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 12px;
            background-color: rgba(64, 64, 64, 0.3);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #4CAF50;
            font-size: 13px;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 5px;
            padding: 8px 15px;
            min-height: 25px;
            font-weight: bold;
            color: #ffffff;
        }
        QPushButton:hover {
            background-color: #505050;
            border-color: #707070;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QLineEdit {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 5px;
            padding: 8px;
            min-height: 25px;
            font-family: 'Consolas', 'Monaco', monospace;
            color: #ffffff;
        }
        QLineEdit:focus {
            border-color: #4CAF50;
            background-color: #454545;
        }
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 5px;
            padding: 8px;
            min-height: 25px;
            color: #ffffff;
        }
        QComboBox:hover {
            border-color: #707070;
        }
        QComboBox::drop-down {
            border: none;
            width: 25px;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 6px solid #ffffff;
            margin-right: 8px;
        }
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #606060;
            selection-background-color: #4CAF50;
            color: #ffffff;
        }
        QLabel {
            color: #ffffff;
            padding: 3px;
            font-size: 12px;
        }
        QTextEdit {
            background-color: #353535;
            border: 2px solid #555555;
            border-radius: 8px;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            padding: 10px;
        }
    )");
}

void GeoInfoWidget::createMapArea()
{
    // 创建地图容器
    m_mapContainer = new QWidget();
    m_mapContainer->setMinimumSize(600, 400);

    // 创建布局
    QVBoxLayout *mapLayout = new QVBoxLayout(m_mapContainer);
    mapLayout->setContentsMargins(0, 0, 0, 0);
    mapLayout->setSpacing(0);

    // 创建瓦片地图视图
    m_mapView = new TileMapView(m_mapContainer);
    m_mapView->setMinimumSize(600, 400);
    m_mapView->show();
    qDebug() << "GeoInfoWidget: TileMapView创建完成，大小:" << m_mapView->size();

    // 创建地图控制按钮容器
    m_zoomControlWidget = new QWidget(m_mapContainer);
    m_zoomControlWidget->setFixedSize(120, 80);
    m_zoomControlWidget->setStyleSheet(
        "QWidget { background-color: rgba(0, 0, 0, 0.7); border-radius: 8px; }"
        "QPushButton { background-color: rgba(255, 255, 255, 0.9); border: 1px solid #ccc; "
        "border-radius: 4px; font-weight: bold; color: #333; min-width: 30px; min-height: 30px; }"
        "QPushButton:hover { background-color: rgba(255, 255, 255, 1.0); }"
        "QPushButton:pressed { background-color: rgba(200, 200, 200, 1.0); }");

    // 创建放大缩小按钮
    QVBoxLayout *controlBtnLayout = new QVBoxLayout(m_zoomControlWidget);
    controlBtnLayout->setContentsMargins(10, 10, 10, 10);
    controlBtnLayout->setSpacing(5);

    QPushButton *zoomInBtn = new QPushButton("+", m_zoomControlWidget);
    zoomInBtn->setToolTip("放大地图");
    zoomInBtn->setFixedSize(35, 35);
    controlBtnLayout->addWidget(zoomInBtn);

    QPushButton *zoomOutBtn = new QPushButton("-", m_zoomControlWidget);
    zoomOutBtn->setToolTip("缩小地图");
    zoomOutBtn->setFixedSize(35, 35);
    controlBtnLayout->addWidget(zoomOutBtn);

    // 将地图视图添加到布局
    mapLayout->addWidget(m_mapView);

    // 将控制按钮定位到右上角
    m_zoomControlWidget->move(m_mapContainer->width() - 130, 10);
    m_zoomControlWidget->raise(); // 确保按钮在地图上方

    // 连接地图信号
    connect(m_mapView, &TileMapView::coordinateClicked,
            this, &GeoInfoWidget::onMapCoordinateClicked);
    connect(m_mapView, &TileMapView::centerChanged,
            this, &GeoInfoWidget::onMapCenterChanged);
    connect(m_mapView, &TileMapView::zoomChanged,
            this, &GeoInfoWidget::onMapZoomChanged);
    connect(m_mapView, &TileMapView::mouseCoordinateChanged,
            this, &GeoInfoWidget::onMouseCoordinateChanged);

    // 连接缩放按钮信号
    connect(zoomInBtn, &QPushButton::clicked, [this]()
            {
        int currentZoom = m_mapView->getZoomLevel();
        if (currentZoom < 18) { // 最大缩放级别
            m_mapView->setZoomLevel(currentZoom + 1);
        } });

    connect(zoomOutBtn, &QPushButton::clicked, [this]()
            {
        int currentZoom = m_mapView->getZoomLevel();
        if (currentZoom > 1) { // 最小缩放级别
            m_mapView->setZoomLevel(currentZoom - 1);
        } });

    // 设置初始位置（中国中心）和默认缩放级别4
    m_mapView->setCenter(35.0, 104.0);
    m_mapView->setZoomLevel(DEFAULT_ZOOM);
}

void GeoInfoWidget::createControlPanel()
{
    // 创建控制面板
    m_controlPanel = new QWidget();
    m_controlPanel->setMaximumWidth(220); // 减小最大宽度
    m_controlPanel->setMinimumWidth(180); // 减小最小宽度

    QVBoxLayout *controlLayout = new QVBoxLayout(m_controlPanel);
    controlLayout->setContentsMargins(10, 10, 10, 10);
    controlLayout->setSpacing(10);

    // 位置控制组
    QGroupBox *locationGroup = new QGroupBox("位置控制");
    QVBoxLayout *locationLayout = new QVBoxLayout(locationGroup);

    // 定位按钮
    m_locationBtn = new QPushButton("获取当前位置");
    m_locationBtn->setToolTip("获取当前GPS位置");
    locationLayout->addWidget(m_locationBtn);

    // 坐标输入
    QHBoxLayout *coordLayout = new QHBoxLayout();
    coordLayout->addWidget(new QLabel("纬度:"));
    m_latInput = new QLineEdit();
    m_latInput->setPlaceholderText("39.9042");
    m_latInput->setToolTip("输入纬度坐标 (-90 到 90)\n支持小数点后6位精度");
    coordLayout->addWidget(m_latInput);
    locationLayout->addLayout(coordLayout);

    QHBoxLayout *lngLayout = new QHBoxLayout();
    lngLayout->addWidget(new QLabel("经度:"));
    m_lngInput = new QLineEdit();
    m_lngInput->setPlaceholderText("116.4074");
    m_lngInput->setToolTip("输入经度坐标 (-180 到 180)\n支持小数点后6位精度");
    lngLayout->addWidget(m_lngInput);
    locationLayout->addLayout(lngLayout);

    // 跳转按钮
    m_jumpBtn = new QPushButton("跳转到坐标");
    m_jumpBtn->setToolTip("跳转到指定的经纬度坐标\n快捷键: 在输入框中按回车");
    locationLayout->addWidget(m_jumpBtn);

    controlLayout->addWidget(locationGroup);

    // 图层控制组
    QGroupBox *layerGroup = new QGroupBox("图层控制");
    QVBoxLayout *layerLayout = new QVBoxLayout(layerGroup);

    m_layerCombo = new QComboBox();
    m_layerCombo->addItem("街道图", "roadmap");
    m_layerCombo->addItem("卫星图", "satellite");
    m_layerCombo->addItem("混合图", "hybrid");
    m_layerCombo->addItem("地形图", "terrain");
    m_layerCombo->setToolTip("选择地图图层类型\n• 街道图: 显示道路和地名\n• 卫星图: 显示卫星影像\n• 混合图: 卫星图+道路标注\n• 地形图: 显示地形特征");
    layerLayout->addWidget(m_layerCombo);

    controlLayout->addWidget(layerGroup);

    // 添加弹性空间
    controlLayout->addStretch();
}

void GeoInfoWidget::createInfoPanel()
{
    // 信息面板将作为地图上的悬浮面板显示
    // 这里先创建标签，稍后会添加到地图容器中

    m_coordSystemLabel = new QLabel("坐标系: WGS84");
    m_currentCoordLabel = new QLabel("当前坐标: 39.9042, 116.4074");
    m_zoomLevelLabel = new QLabel("缩放级别: 10");
    m_scaleLabel = new QLabel("比例尺: 1:100000");
    m_mouseCoordLabel = new QLabel("鼠标位置: --");
}

void GeoInfoWidget::connectSignals()
{
    // 连接控制按钮信号
    connect(m_locationBtn, &QPushButton::clicked, this, &GeoInfoWidget::onLocationButtonClicked);
    connect(m_jumpBtn, &QPushButton::clicked, this, &GeoInfoWidget::onCoordinateInputEnter);
    connect(m_layerCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &GeoInfoWidget::onLayerChanged);

    // 连接输入框回车信号
    connect(m_latInput, &QLineEdit::returnPressed, this, &GeoInfoWidget::onCoordinateInputEnter);
    connect(m_lngInput, &QLineEdit::returnPressed, this, &GeoInfoWidget::onCoordinateInputEnter);

    // 地图相关信号连接（暂时移除）
}

void GeoInfoWidget::initializeMap()
{
    // 创建更新定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &GeoInfoWidget::updateMousePosition);
    m_updateTimer->start(1000); // 1秒更新一次

    // 检查网络连接并设置初始模式
    if (checkNetworkConnection())
    {
        switchToOnlineMode();
    }
    else
    {
        switchToOfflineMode();
    }

    // TileMapView已经在initializeUI中创建和初始化，无需额外操作
    qDebug() << "GeoInfoWidget: 地图初始化完成，使用TileMapView显示瓦片地图";
}

void GeoInfoWidget::loadMapHTML()
{
    // 创建包含Google Maps的HTML内容
    QString htmlContent = QString(R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>LiteAPPStar 地理信息</title>
    <style>
        body, html { margin: 0; padding: 0; height: 100%; font-family: Arial, sans-serif; }
        #map { height: 100%; width: 100%; }
        .info-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(43, 43, 43, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        .info-panel div { margin: 2px 0; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <div id="coord-system">坐标系: WGS84</div>
        <div id="current-coord">当前坐标: %1, %2</div>
        <div id="zoom-level">缩放级别: %3</div>
        <div id="scale">比例尺: 1:100000</div>
        <div id="mouse-coord">鼠标位置: --</div>
    </div>
    
    <script>
        // 地图初始化将在后续实现
        console.log('地图HTML已加载');
    </script>
</body>
</html>
    )")
                              .arg(m_currentLat)
                              .arg(m_currentLng)
                              .arg(m_currentZoom);

    // 获取当前图层名称
    QString layerName;
    if (m_currentLayer == "roadmap")
        layerName = "街道图";
    else if (m_currentLayer == "satellite")
        layerName = "卫星图";
    else if (m_currentLayer == "hybrid")
        layerName = "混合图";
    else if (m_currentLayer == "terrain")
        layerName = "地形图";
    else
        layerName = "街道图";

    // 计算比例尺（Web Mercator投影）
    double latRad = m_currentLat * M_PI / 180.0;
    double scale = 156543.03392 * qCos(latRad) / qPow(2, m_currentZoom);
    QString scaleText;
    if (scale > 1000000)
    {
        scaleText = QString("1:%1M").arg(QString::number(scale / 1000000, 'f', 1));
    }
    else if (scale > 1000)
    {
        scaleText = QString("1:%1K").arg(QString::number(scale / 1000, 'f', 0));
    }
    else
    {
        scaleText = QString("1:%1").arg(QString::number(scale, 'f', 0));
    }

    // 地图区域现在由TileMapView管理，无需手动设置内容
}

void GeoInfoWidget::createMapControls()
{
    // 创建右上角控制面板
    m_mapControlPanel = new QWidget(m_mapContainer);
    m_mapControlPanel->setFixedSize(200, 150);
    m_mapControlPanel->move(m_mapContainer->width() - 210, 10);
    m_mapControlPanel->setStyleSheet(R"(
        QWidget {
            background-color: rgba(43, 43, 43, 0.95);
            border: 2px solid #555555;
            border-radius: 8px;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 4px;
            padding: 6px 12px;
            color: #ffffff;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #505050;
            border-color: #707070;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 4px;
            padding: 4px 8px;
            color: #ffffff;
            min-height: 20px;
        }
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #ffffff;
            margin-right: 5px;
        }
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #606060;
            selection-background-color: #505050;
            color: #ffffff;
        }
        QLabel {
            color: #ffffff;
            font-size: 12px;
            font-weight: bold;
        }
    )");

    QVBoxLayout *controlLayout = new QVBoxLayout(m_mapControlPanel);
    controlLayout->setContentsMargins(10, 10, 10, 10);
    controlLayout->setSpacing(8);

    // 定位按钮
    QPushButton *quickLocationBtn = new QPushButton("定位");
    quickLocationBtn->setFixedHeight(25);
    quickLocationBtn->setToolTip("获取当前位置");
    connect(quickLocationBtn, &QPushButton::clicked, this, &GeoInfoWidget::onLocationButtonClicked);
    controlLayout->addWidget(quickLocationBtn);

    // 图层选择
    QLabel *layerLabel = new QLabel("图层:");
    layerLabel->setStyleSheet("color: #ffffff; font-size: 11px;");
    controlLayout->addWidget(layerLabel);

    QComboBox *quickLayerCombo = new QComboBox();
    quickLayerCombo->setFixedHeight(25);
    quickLayerCombo->addItem("街道图", "roadmap");
    quickLayerCombo->addItem("卫星图", "satellite");
    quickLayerCombo->addItem("混合图", "hybrid");
    quickLayerCombo->addItem("地形图", "terrain");
    connect(quickLayerCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this, quickLayerCombo]()
            {
                QString layerType = quickLayerCombo->currentData().toString();
                switchMapLayer(layerType);
            });
    controlLayout->addWidget(quickLayerCombo);

    // 缩放控制
    QLabel *zoomLabel = new QLabel("缩放:");
    zoomLabel->setStyleSheet("color: #ffffff; font-size: 11px;");
    controlLayout->addWidget(zoomLabel);

    QHBoxLayout *zoomLayout = new QHBoxLayout();
    QPushButton *zoomInBtn = new QPushButton("+");
    QPushButton *zoomOutBtn = new QPushButton("-");
    m_zoomLevelLabel = new QLabel("4");

    zoomInBtn->setFixedSize(30, 25);
    zoomOutBtn->setFixedSize(30, 25);
    m_zoomLevelLabel->setFixedSize(30, 25);
    m_zoomLevelLabel->setAlignment(Qt::AlignCenter);
    m_zoomLevelLabel->setStyleSheet("color: #ffffff; font-size: 12px; font-weight: bold; border: 1px solid #606060; border-radius: 4px; background-color: #404040;");

    zoomInBtn->setToolTip("放大");
    zoomOutBtn->setToolTip("缩小");

    connect(zoomInBtn, &QPushButton::clicked, [this]()
            {
        if (m_mapView) {
            int currentZoom = m_mapView->getZoomLevel();
            m_mapView->setZoomLevel(currentZoom + 1);
        } });
    connect(zoomOutBtn, &QPushButton::clicked, [this]()
            {
        if (m_mapView) {
            int currentZoom = m_mapView->getZoomLevel();
            m_mapView->setZoomLevel(currentZoom - 1);
        } });

    zoomLayout->addWidget(zoomOutBtn);
    zoomLayout->addWidget(m_zoomLevelLabel);
    zoomLayout->addWidget(zoomInBtn);
    zoomLayout->addStretch();
    controlLayout->addLayout(zoomLayout);

    m_mapControlPanel->show();
}

void GeoInfoWidget::createMapInfoPanel()
{
    // 创建左上角信息面板
    m_mapInfoPanel = new QWidget(m_mapContainer);
    m_mapInfoPanel->setFixedSize(250, 120);
    m_mapInfoPanel->move(10, 10);
    m_mapInfoPanel->setStyleSheet(R"(
        QWidget {
            background-color: rgba(43, 43, 43, 0.95);
            border: 2px solid #555555;
            border-radius: 8px;
        }
        QLabel {
            color: #ffffff;
            font-size: 11px;
            padding: 3px 5px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        QLabel:first-child {
            font-weight: bold;
            color: #4CAF50;
        }
    )");

    QVBoxLayout *infoLayout = new QVBoxLayout(m_mapInfoPanel);
    infoLayout->setContentsMargins(8, 8, 8, 8);
    infoLayout->setSpacing(3);

    // 创建信息标签
    m_coordSystemLabel = new QLabel("坐标系: WGS84");
    m_currentCoordLabel = new QLabel("当前坐标: 39.904200, 116.407400");
    m_zoomLevelLabel = new QLabel("缩放级别: 10");
    m_scaleLabel = new QLabel("比例尺: 1:100K");
    m_mouseCoordLabel = new QLabel("鼠标位置: --");
    m_statusLabel = new QLabel("状态: 离线模式");

    infoLayout->addWidget(m_coordSystemLabel);
    infoLayout->addWidget(m_currentCoordLabel);
    infoLayout->addWidget(m_zoomLevelLabel);
    infoLayout->addWidget(m_scaleLabel);
    infoLayout->addWidget(m_mouseCoordLabel);
    infoLayout->addWidget(m_statusLabel);

    m_mapInfoPanel->show();
}

void GeoInfoWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 地图视图大小由布局自动管理
    // TileMapView会自动调整大小

    // 调整缩放控制按钮位置到右上角
    if (m_zoomControlWidget && m_mapContainer)
    {
        m_zoomControlWidget->move(m_mapContainer->width() - 130, 10);
        m_zoomControlWidget->raise(); // 确保按钮在地图上方
    }
}

// eventFilter方法已移除，TileMapView自己处理鼠标和滚轮事件

// 槽函数实现
void GeoInfoWidget::jumpToCoordinate(double latitude, double longitude)
{
    m_currentLat = latitude;
    m_currentLng = longitude;

    // 更新地图中心点（稍后实现JavaScript调用）
    updateCoordinateDisplay(latitude, longitude);

    qDebug() << "跳转到坐标:" << latitude << "," << longitude;
}

void GeoInfoWidget::getCurrentLocation()
{
    // 模拟获取当前位置（北京天安门）
    jumpToCoordinate(39.9042, 116.4074);
    m_latInput->setText("39.904200");
    m_lngInput->setText("116.407400");

    QMessageBox::information(this, "位置信息", "已跳转到默认位置：北京天安门\n\n注意：GPS定位功能正在开发中");
}

void GeoInfoWidget::switchMapLayer(const QString &layerType)
{
    m_currentLayer = layerType;

    // 更新地图显示内容
    QString layerName;
    if (layerType == "roadmap")
        layerName = "街道图";
    else if (layerType == "satellite")
        layerName = "卫星图";
    else if (layerType == "hybrid")
        layerName = "混合图";
    else if (layerType == "terrain")
        layerName = "地形图";
    else
        layerName = "未知图层";

    // TileMapView会自动处理图层切换，无需手动更新

    qDebug() << "切换到图层:" << layerName << "(" << layerType << ")";
}

void GeoInfoWidget::setZoomLevel(int zoomLevel)
{
    m_currentZoom = zoomLevel;
    updateZoomDisplay(zoomLevel);
    updateScaleDisplay(zoomLevel);
}

void GeoInfoWidget::onCoordinateInputEnter()
{
    bool latOk, lngOk;
    double lat = m_latInput->text().toDouble(&latOk);
    double lng = m_lngInput->text().toDouble(&lngOk);

    if (latOk && lngOk && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180)
    {
        jumpToCoordinate(lat, lng);
    }
    else
    {
        QMessageBox::warning(this, "输入错误", "请输入有效的经纬度坐标\n纬度范围: -90 到 90\n经度范围: -180 到 180");
    }
}

void GeoInfoWidget::onLocationButtonClicked()
{
    getCurrentLocation();
}

void GeoInfoWidget::onLayerChanged()
{
    QString layerType = m_layerCombo->currentData().toString();
    switchMapLayer(layerType);
}

void GeoInfoWidget::updateMousePosition()
{
    // 性能优化：只在需要时更新
    static int counter = 0;
    counter++;

    // 每5次更新一次，减少CPU使用
    if (counter % 5 != 0)
    {
        return;
    }

    // 模拟鼠标在地图上移动的坐标
    double mouseLat = m_currentLat + (qSin(counter * 0.02) * 0.005);
    double mouseLng = m_currentLng + (qCos(counter * 0.02) * 0.005);

    if (m_mouseCoordLabel)
    {
        // 使用静态字符串避免重复分配
        static QString lastText;
        QString newText = QString("鼠标位置: %1, %2")
                              .arg(mouseLat, 0, 'f', 6)
                              .arg(mouseLng, 0, 'f', 6);

        if (newText != lastText)
        {
            m_mouseCoordLabel->setText(newText);
            lastText = newText;
        }
    }
}

void GeoInfoWidget::updateCoordinateDisplay(double lat, double lng)
{
    if (m_currentCoordLabel)
    {
        m_currentCoordLabel->setText(QString("当前坐标: %1, %2").arg(lat, 0, 'f', 6).arg(lng, 0, 'f', 6));
    }
}

void GeoInfoWidget::updateZoomDisplay(int zoom)
{
    if (m_zoomLevelLabel)
    {
        m_zoomLevelLabel->setText(QString("缩放级别: %1").arg(zoom));
    }
}

void GeoInfoWidget::updateScaleDisplay(int zoom)
{
    // 根据缩放级别计算比例尺（Web Mercator投影）
    // 使用当前纬度计算准确的比例尺
    double latRad = m_currentLat * M_PI / 180.0;
    double scale = 156543.03392 * qCos(latRad) / qPow(2, zoom);
    QString scaleText;

    if (scale > 1000000)
    {
        scaleText = QString("1:%1M").arg(QString::number(scale / 1000000, 'f', 1));
    }
    else if (scale > 1000)
    {
        scaleText = QString("1:%1K").arg(QString::number(scale / 1000, 'f', 0));
    }
    else
    {
        scaleText = QString("1:%1").arg(QString::number(scale, 'f', 0));
    }

    if (m_scaleLabel)
    {
        m_scaleLabel->setText(QString("比例尺: %1").arg(scaleText));
    }
}

// 旧的鼠标事件处理方法已移除，现在由TileMapView直接处理

bool GeoInfoWidget::checkNetworkConnection()
{
    // 简单的网络连接检查
    // 在实际应用中，这里可以ping一个可靠的服务器
    // 目前返回false表示离线模式
    return false;
}

void GeoInfoWidget::switchToOfflineMode()
{
    m_isOnlineMode = false;
    updateStatus("离线模式");

    // TileMapView会自动处理离线模式，无需手动更新

    qDebug() << "切换到离线模式";
}

void GeoInfoWidget::onMouseCoordinateChanged(double lat, double lng)
{
    // 转发鼠标坐标变化信号到主窗口
    emit mouseCoordinateChanged(lat, lng);
}

void GeoInfoWidget::switchToOnlineMode()
{
    if (checkNetworkConnection())
    {
        m_isOnlineMode = true;
        updateStatus("在线模式");

        // TileMapView会自动处理在线模式，无需手动更新

        qDebug() << "切换到在线模式";
    }
    else
    {
        showError("无法连接到网络，保持离线模式");
    }
}

void GeoInfoWidget::showError(const QString &error)
{
    m_lastError = error;
    updateStatus(QString("错误: %1").arg(error));

    // 显示错误消息框
    QMessageBox::warning(this, "地理信息错误", error);

    qDebug() << "地理信息错误:" << error;
}

void GeoInfoWidget::updateStatus(const QString &status)
{
    if (m_statusLabel)
    {
        m_statusLabel->setText(QString("状态: %1").arg(status));

        // 根据状态设置不同颜色
        if (status.contains("错误"))
        {
            m_statusLabel->setStyleSheet("color: #f44336; font-weight: bold;");
        }
        else if (status.contains("在线"))
        {
            m_statusLabel->setStyleSheet("color: #4CAF50; font-weight: bold;");
        }
        else if (status.contains("离线"))
        {
            m_statusLabel->setStyleSheet("color: #FF9800; font-weight: bold;");
        }
        else
        {
            m_statusLabel->setStyleSheet("color: #ffffff;");
        }
    }
}

void GeoInfoWidget::onMapCoordinateClicked(double lat, double lng)
{
    // 更新当前坐标
    m_currentLat = lat;
    m_currentLng = lng;

    // 更新状态信息
    QString status = QString("点击坐标: %1, %2").arg(lat, 0, 'f', 6).arg(lng, 0, 'f', 6);
    updateStatus(status);

    // 发射坐标改变信号
    emit coordinateChanged(lat, lng);
}

void GeoInfoWidget::onMapCenterChanged(double lat, double lng)
{
    // 更新当前坐标
    m_currentLat = lat;
    m_currentLng = lng;

    // 发射坐标改变信号
    emit coordinateChanged(lat, lng);
}

void GeoInfoWidget::onMapZoomChanged(int zoom)
{
    // 更新当前缩放级别
    m_currentZoom = zoom;

    // 更新缩放级别标签
    if (m_zoomLevelLabel)
    {
        m_zoomLevelLabel->setText(QString::number(zoom));
    }

    // 更新状态信息
    QString status = QString("缩放级别: %1").arg(zoom);
    updateStatus(status);

    // 发射缩放改变信号
    emit zoomLevelChanged(zoom);
}
