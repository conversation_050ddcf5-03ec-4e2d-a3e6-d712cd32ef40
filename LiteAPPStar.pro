QT += core widgets network

CONFIG += c++17

TARGET = LiteAPPStar
TEMPLATE = app

# 版本信息
VERSION = 0.4.0
QMAKE_TARGET_COMPANY = "LiteAPPStar Team"
QMAKE_TARGET_PRODUCT = "LiteAPPStar"
QMAKE_TARGET_DESCRIPTION = "跨平台桌面应用程序"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2024 LiteAPPStar Team"

# 包含目录
INCLUDEPATH += $$PWD/include
INCLUDEPATH += $$PWD/build

# 源文件
SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp \
    src/signalanalysiswidget.cpp \
    src/qgismapwidget.cpp \
    src/tilemapview.cpp \
    src/localtilemanager.cpp \
    src/spectrumplot_simple.cpp \
    src/waterfallplot_simple.cpp

# 头文件
HEADERS += \
    include/mainwindow.h \
    src/signalanalysiswidget.h \
    src/qgismapwidget.h \
    src/tilemapview.h \
    src/localtilemanager.h \
    src/spectrumplot_simple.h \
    src/waterfallplot_simple.h

# UI文件
FORMS += \
    src/mainwindow.ui \
    src/signalanalysis.ui

# 资源文件
RESOURCES += \
    resources/resources.qrc

# 输出目录
DESTDIR = $$PWD/bin

# 对象文件目录
OBJECTS_DIR = $$PWD/build/obj
MOC_DIR = $$PWD/build/moc
RCC_DIR = $$PWD/build/rcc
UI_DIR = $$PWD/build/ui

# Windows特定配置
win32 {
    RC_FILE = resources/app.rc
    CONFIG += embed_manifest_exe
    
    # 设置图标
    RC_ICONS = resources/app.ico
    
    # 调试配置
    CONFIG(debug, debug|release) {
        TARGET = $${TARGET}_debug
    }
}

# macOS特定配置
macx {
    ICON = resources/app.icns
    QMAKE_INFO_PLIST = resources/Info.plist
    
    # 设置最低系统版本
    QMAKE_MACOSX_DEPLOYMENT_TARGET = 10.12
}

# Linux特定配置
unix:!macx {
    # 设置RPATH
    QMAKE_RPATHDIR += $ORIGIN
    
    # 桌面文件
    desktop.files = resources/liteappstar.desktop
    desktop.path = /usr/share/applications
    
    # 图标文件
    icon.files = resources/app.png
    icon.path = /usr/share/pixmaps
    
    INSTALLS += desktop icon
}

# 调试信息
CONFIG(debug, debug|release) {
    DEFINES += DEBUG
    message("Building in debug mode")
} else {
    DEFINES += QT_NO_DEBUG_OUTPUT
    message("Building in release mode")
}

# 编译器警告
CONFIG += warn_on
QMAKE_CXXFLAGS += -Wall

# 清理配置
QMAKE_CLEAN += $$DESTDIR/$$TARGET*
