/********************************************************************************
** Form generated from reading UI file 'signalanalysis.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SIGNALANALYSIS_H
#define UI_SIGNALANALYSIS_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SignalAnalysisWidget
{
public:
    QVBoxLayout *mainLayout;
    QGroupBox *frequencyControlGroup;
    QHBoxLayout *frequencyControlLayout;
    QLabel *centerFreqLabel;
    QLineEdit *centerFreqLineEdit;
    QComboBox *centerFreqUnitCombo;
    QLabel *spanLabel;
    QLineEdit *spanLineEdit;
    QComboBox *spanUnitCombo;
    QLabel *rangeLabel;
    QLabel *frequencyRangeLabel;
    QSpacerItem *horizontalSpacer;
    QPushButton *presetISMBtn;
    QPushButton *presetWiFiBtn;
    QPushButton *presetFMBtn;
    QLabel *separatorLabel;
    QPushButton *dynamicDataBtn;
    QSplitter *mainSplitter;
    QGroupBox *spectrumGroup;
    QVBoxLayout *spectrumLayout;
    QWidget *spectrumDisplay;
    QGroupBox *waterfallGroup;
    QVBoxLayout *waterfallLayout;
    QWidget *waterfallDisplay;
    QGroupBox *dataGroup;
    QVBoxLayout *dataLayout;
    QTableWidget *dataTable;

    void setupUi(QWidget *SignalAnalysisWidget)
    {
        if (SignalAnalysisWidget->objectName().isEmpty())
            SignalAnalysisWidget->setObjectName(QString::fromUtf8("SignalAnalysisWidget"));
        SignalAnalysisWidget->resize(1400, 900);
        SignalAnalysisWidget->setStyleSheet(QString::fromUtf8("QWidget {\n"
"    background-color: #1a1a2e;\n"
"    color: #eee;\n"
"    font-family: \"Microsoft YaHei\", \"SimHei\", sans-serif;\n"
"    font-size: 9pt;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 8px;\n"
"    margin-top: 1ex;\n"
"    font-weight: bold;\n"
"    color: #eee;\n"
"    padding-top: 10px;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 15px;\n"
"    padding: 0 8px 0 8px;\n"
"    color: #64a0ff;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #eee;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"QSpinBox, QDoubleSpinBox {\n"
"    background-color: #0f3460;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"    color: #eee;\n"
"    padding: 4px;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QSpinBox:focus, QDoubleSpinBox:focus {\n"
"    border-color: #64a0ff;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    background-color: #0f3460;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"    color: #eee;\n"
"    padding: 4px;\n"
""
                        "    min-width: 80px;\n"
"}\n"
"\n"
"QLineEdit:focus {\n"
"    border-color: #64a0ff;\n"
"}\n"
"\n"
"QComboBox {\n"
"    background-color: #0f3460;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"    color: #eee;\n"
"    padding: 4px;\n"
"    min-width: 60px;\n"
"}\n"
"\n"
"QComboBox:focus {\n"
"    border-color: #64a0ff;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border-left: 5px solid transparent;\n"
"    border-right: 5px solid transparent;\n"
"    border-top: 5px solid #eee;\n"
"    margin-right: 5px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    background-color: #533483;\n"
"    border: none;\n"
"    border-radius: 6px;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    padding: 8px 16px;\n"
"    min-width: 80px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #6a4c93;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #4a2c73;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    backgrou"
                        "nd-color: #2a2a3e;\n"
"    color: #666;\n"
"}\n"
"\n"
"QSplitter::handle {\n"
"    background-color: #16213e;\n"
"}\n"
"\n"
"QSplitter::handle:horizontal {\n"
"    width: 3px;\n"
"}\n"
"\n"
"QSplitter::handle:vertical {\n"
"    height: 2px;\n"
"}"));
        mainLayout = new QVBoxLayout(SignalAnalysisWidget);
        mainLayout->setSpacing(8);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        mainLayout->setContentsMargins(10, 10, 10, 10);
        frequencyControlGroup = new QGroupBox(SignalAnalysisWidget);
        frequencyControlGroup->setObjectName(QString::fromUtf8("frequencyControlGroup"));
        QFont font;
        font.setFamily(QString::fromUtf8("Microsoft YaHei"));
        font.setPointSize(9);
        font.setBold(true);
        font.setWeight(75);
        frequencyControlGroup->setFont(font);
        frequencyControlGroup->setMaximumSize(QSize(16777215, 130));
        frequencyControlGroup->setMinimumSize(QSize(0, 85));
        frequencyControlLayout = new QHBoxLayout(frequencyControlGroup);
        frequencyControlLayout->setSpacing(8);
        frequencyControlLayout->setObjectName(QString::fromUtf8("frequencyControlLayout"));
        frequencyControlLayout->setContentsMargins(12, 10, 12, 10);
        centerFreqLabel = new QLabel(frequencyControlGroup);
        centerFreqLabel->setObjectName(QString::fromUtf8("centerFreqLabel"));

        frequencyControlLayout->addWidget(centerFreqLabel);

        centerFreqLineEdit = new QLineEdit(frequencyControlGroup);
        centerFreqLineEdit->setObjectName(QString::fromUtf8("centerFreqLineEdit"));
        centerFreqLineEdit->setMinimumSize(QSize(90, 0));
        centerFreqLineEdit->setMaximumSize(QSize(130, 16777215));
        centerFreqLineEdit->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        frequencyControlLayout->addWidget(centerFreqLineEdit);

        centerFreqUnitCombo = new QComboBox(frequencyControlGroup);
        centerFreqUnitCombo->addItem(QString());
        centerFreqUnitCombo->addItem(QString());
        centerFreqUnitCombo->addItem(QString());
        centerFreqUnitCombo->addItem(QString());
        centerFreqUnitCombo->setObjectName(QString::fromUtf8("centerFreqUnitCombo"));
        centerFreqUnitCombo->setMinimumSize(QSize(55, 28));
        centerFreqUnitCombo->setMaximumSize(QSize(75, 28));

        frequencyControlLayout->addWidget(centerFreqUnitCombo);

        spanLabel = new QLabel(frequencyControlGroup);
        spanLabel->setObjectName(QString::fromUtf8("spanLabel"));

        frequencyControlLayout->addWidget(spanLabel);

        spanLineEdit = new QLineEdit(frequencyControlGroup);
        spanLineEdit->setObjectName(QString::fromUtf8("spanLineEdit"));
        spanLineEdit->setMinimumSize(QSize(90, 0));
        spanLineEdit->setMaximumSize(QSize(130, 16777215));
        spanLineEdit->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        frequencyControlLayout->addWidget(spanLineEdit);

        spanUnitCombo = new QComboBox(frequencyControlGroup);
        spanUnitCombo->addItem(QString());
        spanUnitCombo->addItem(QString());
        spanUnitCombo->addItem(QString());
        spanUnitCombo->addItem(QString());
        spanUnitCombo->setObjectName(QString::fromUtf8("spanUnitCombo"));
        spanUnitCombo->setMinimumSize(QSize(55, 28));
        spanUnitCombo->setMaximumSize(QSize(75, 28));

        frequencyControlLayout->addWidget(spanUnitCombo);

        rangeLabel = new QLabel(frequencyControlGroup);
        rangeLabel->setObjectName(QString::fromUtf8("rangeLabel"));

        frequencyControlLayout->addWidget(rangeLabel);

        frequencyRangeLabel = new QLabel(frequencyControlGroup);
        frequencyRangeLabel->setObjectName(QString::fromUtf8("frequencyRangeLabel"));
        frequencyRangeLabel->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #64a0ff;\n"
"    font-weight: bold;\n"
"}"));

        frequencyControlLayout->addWidget(frequencyRangeLabel);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        frequencyControlLayout->addItem(horizontalSpacer);

        presetISMBtn = new QPushButton(frequencyControlGroup);
        presetISMBtn->setObjectName(QString::fromUtf8("presetISMBtn"));
        presetISMBtn->setMinimumSize(QSize(75, 30));

        frequencyControlLayout->addWidget(presetISMBtn);

        presetWiFiBtn = new QPushButton(frequencyControlGroup);
        presetWiFiBtn->setObjectName(QString::fromUtf8("presetWiFiBtn"));
        presetWiFiBtn->setMinimumSize(QSize(75, 30));

        frequencyControlLayout->addWidget(presetWiFiBtn);

        presetFMBtn = new QPushButton(frequencyControlGroup);
        presetFMBtn->setObjectName(QString::fromUtf8("presetFMBtn"));
        presetFMBtn->setMinimumSize(QSize(55, 30));

        frequencyControlLayout->addWidget(presetFMBtn);

        separatorLabel = new QLabel(frequencyControlGroup);
        separatorLabel->setObjectName(QString::fromUtf8("separatorLabel"));

        frequencyControlLayout->addWidget(separatorLabel);

        dynamicDataBtn = new QPushButton(frequencyControlGroup);
        dynamicDataBtn->setObjectName(QString::fromUtf8("dynamicDataBtn"));
        dynamicDataBtn->setCheckable(true);
        dynamicDataBtn->setChecked(true);
        dynamicDataBtn->setMinimumSize(QSize(110, 30));

        frequencyControlLayout->addWidget(dynamicDataBtn);


        mainLayout->addWidget(frequencyControlGroup);

        mainSplitter = new QSplitter(SignalAnalysisWidget);
        mainSplitter->setObjectName(QString::fromUtf8("mainSplitter"));
        mainSplitter->setOrientation(Qt::Vertical);
        spectrumGroup = new QGroupBox(mainSplitter);
        spectrumGroup->setObjectName(QString::fromUtf8("spectrumGroup"));
        spectrumGroup->setMinimumSize(QSize(0, 200));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(3);
        sizePolicy.setHeightForWidth(spectrumGroup->sizePolicy().hasHeightForWidth());
        spectrumGroup->setSizePolicy(sizePolicy);
        spectrumGroup->setFont(font);
        spectrumLayout = new QVBoxLayout(spectrumGroup);
        spectrumLayout->setSpacing(0);
        spectrumLayout->setObjectName(QString::fromUtf8("spectrumLayout"));
        spectrumLayout->setContentsMargins(5, 5, 5, 5);
        spectrumDisplay = new QWidget(spectrumGroup);
        spectrumDisplay->setObjectName(QString::fromUtf8("spectrumDisplay"));
        spectrumDisplay->setStyleSheet(QString::fromUtf8("QWidget {\n"
"    background-color: #0f1419;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"}"));

        spectrumLayout->addWidget(spectrumDisplay);

        mainSplitter->addWidget(spectrumGroup);
        waterfallGroup = new QGroupBox(mainSplitter);
        waterfallGroup->setObjectName(QString::fromUtf8("waterfallGroup"));
        waterfallGroup->setMinimumSize(QSize(0, 150));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(2);
        sizePolicy1.setHeightForWidth(waterfallGroup->sizePolicy().hasHeightForWidth());
        waterfallGroup->setSizePolicy(sizePolicy1);
        waterfallGroup->setFont(font);
        waterfallLayout = new QVBoxLayout(waterfallGroup);
        waterfallLayout->setSpacing(0);
        waterfallLayout->setObjectName(QString::fromUtf8("waterfallLayout"));
        waterfallLayout->setContentsMargins(5, 15, 5, 5);
        waterfallDisplay = new QWidget(waterfallGroup);
        waterfallDisplay->setObjectName(QString::fromUtf8("waterfallDisplay"));
        waterfallDisplay->setStyleSheet(QString::fromUtf8("QWidget {\n"
"    background-color: #0f1419;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"}"));

        waterfallLayout->addWidget(waterfallDisplay);

        mainSplitter->addWidget(waterfallGroup);
        dataGroup = new QGroupBox(mainSplitter);
        dataGroup->setObjectName(QString::fromUtf8("dataGroup"));
        dataGroup->setMinimumSize(QSize(0, 120));
        sizePolicy1.setHeightForWidth(dataGroup->sizePolicy().hasHeightForWidth());
        dataGroup->setSizePolicy(sizePolicy1);
        dataLayout = new QVBoxLayout(dataGroup);
        dataLayout->setObjectName(QString::fromUtf8("dataLayout"));
        dataTable = new QTableWidget(dataGroup);
        dataTable->setObjectName(QString::fromUtf8("dataTable"));
        dataTable->setStyleSheet(QString::fromUtf8("QTableWidget {\n"
"    background-color: #0f3460;\n"
"    border: 1px solid #16213e;\n"
"    border-radius: 4px;\n"
"    gridline-color: #2a3a5e;\n"
"    selection-background-color: #64a0ff;\n"
"    font-size: 10pt;\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"    padding: 8px;\n"
"    border-bottom: 1px solid #2a3a5e;\n"
"    color: #f0f0f0;\n"
"    min-height: 20px;\n"
"}\n"
"\n"
"QTableWidget::item:selected {\n"
"    background-color: #64a0ff;\n"
"    color: white;\n"
"}\n"
"\n"
"QTableWidget::item:alternate {\n"
"    background-color: #1a2a4e;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: #1a1a2e;\n"
"    color: #64a0ff;\n"
"    padding: 10px;\n"
"    border: 1px solid #16213e;\n"
"    font-weight: bold;\n"
"    font-size: 10pt;\n"
"    min-height: 25px;\n"
"}\n"
"\n"
"QHeaderView::section:horizontal {\n"
"    border-top: none;\n"
"}\n"
"\n"
"QHeaderView::section:vertical {\n"
"    border-left: none;\n"
"}"));
        dataTable->setAlternatingRowColors(true);
        dataTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        dataTable->setSortingEnabled(true);

        dataLayout->addWidget(dataTable);

        mainSplitter->addWidget(dataGroup);

        mainLayout->addWidget(mainSplitter);


        retranslateUi(SignalAnalysisWidget);

        QMetaObject::connectSlotsByName(SignalAnalysisWidget);
    } // setupUi

    void retranslateUi(QWidget *SignalAnalysisWidget)
    {
        SignalAnalysisWidget->setWindowTitle(QCoreApplication::translate("SignalAnalysisWidget", "\351\242\221\350\260\261\345\210\206\346\236\220", nullptr));
        frequencyControlGroup->setTitle(QCoreApplication::translate("SignalAnalysisWidget", "\351\242\221\347\216\207\346\216\247\345\210\266", nullptr));
        frequencyControlGroup->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QGroupBox {\n"
"    color: rgb(200, 200, 200);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5ex;\n"
"    font-weight: bold;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    left: 10px;\n"
"    padding: 1px 5px 2px 5px;\n"
"    color: rgb(220, 220, 220);\n"
"}", nullptr));
        centerFreqLabel->setText(QCoreApplication::translate("SignalAnalysisWidget", "\344\270\255\345\277\203\351\242\221\347\216\207:", nullptr));
        centerFreqLineEdit->setText(QCoreApplication::translate("SignalAnalysisWidget", "100.000", nullptr));
        centerFreqLineEdit->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QLineEdit {\n"
"    background-color: #0f3460;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"    color: #eee;\n"
"    padding: 4px;\n"
"}\n"
"QLineEdit:focus {\n"
"    border-color: #64a0ff;\n"
"}", nullptr));
        centerFreqUnitCombo->setItemText(0, QCoreApplication::translate("SignalAnalysisWidget", "Hz", nullptr));
        centerFreqUnitCombo->setItemText(1, QCoreApplication::translate("SignalAnalysisWidget", "kHz", nullptr));
        centerFreqUnitCombo->setItemText(2, QCoreApplication::translate("SignalAnalysisWidget", "MHz", nullptr));
        centerFreqUnitCombo->setItemText(3, QCoreApplication::translate("SignalAnalysisWidget", "GHz", nullptr));

        centerFreqUnitCombo->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QComboBox {\n"
"    background-color: rgb(45, 50, 65);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 4px;\n"
"    padding: 4px 8px;\n"
"    color: rgb(220, 220, 220);\n"
"    font-family: Microsoft YaHei;\n"
"    font-size: 9pt;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QComboBox:hover {\n"
"    border-color: rgb(120, 120, 140);\n"
"    background-color: rgb(50, 55, 70);\n"
"}\n"
"\n"
"QComboBox:focus {\n"
"    border-color: rgb(140, 140, 160);\n"
"    background-color: rgb(55, 60, 75);\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: padding;\n"
"    subcontrol-position: top right;\n"
"    width: 20px;\n"
"    border-left: 1px solid rgb(100, 100, 120);\n"
"    border-top-right-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    background-color: rgb(60, 65, 80);\n"
"}\n"
"\n"
"QComboBox::drop-down:hover {\n"
"    background-color: rgb(70, 75, 90);\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border-left: 4px solid transparent;\n"
"    bor"
                        "der-right: 4px solid transparent;\n"
"    border-bottom: 6px solid rgb(200, 200, 200);\n"
"    width: 0px;\n"
"    height: 0px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView {\n"
"    background-color: rgb(45, 50, 65);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 4px;\n"
"    color: rgb(220, 220, 220);\n"
"    selection-background-color: rgb(80, 85, 100);\n"
"    outline: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 6px 8px;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: rgb(70, 75, 90);\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: rgb(80, 85, 100);\n"
"}", nullptr));
        spanLabel->setText(QCoreApplication::translate("SignalAnalysisWidget", "Span:", nullptr));
        spanLineEdit->setText(QCoreApplication::translate("SignalAnalysisWidget", "10.000", nullptr));
        spanLineEdit->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QLineEdit {\n"
"    background-color: #0f3460;\n"
"    border: 2px solid #16213e;\n"
"    border-radius: 4px;\n"
"    color: #eee;\n"
"    padding: 4px;\n"
"}\n"
"QLineEdit:focus {\n"
"    border-color: #64a0ff;\n"
"}", nullptr));
        spanUnitCombo->setItemText(0, QCoreApplication::translate("SignalAnalysisWidget", "Hz", nullptr));
        spanUnitCombo->setItemText(1, QCoreApplication::translate("SignalAnalysisWidget", "kHz", nullptr));
        spanUnitCombo->setItemText(2, QCoreApplication::translate("SignalAnalysisWidget", "MHz", nullptr));
        spanUnitCombo->setItemText(3, QCoreApplication::translate("SignalAnalysisWidget", "GHz", nullptr));

        spanUnitCombo->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QComboBox {\n"
"    background-color: rgb(45, 50, 65);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 4px;\n"
"    padding: 4px 8px;\n"
"    color: rgb(220, 220, 220);\n"
"    font-family: Microsoft YaHei;\n"
"    font-size: 9pt;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QComboBox:hover {\n"
"    border-color: rgb(120, 120, 140);\n"
"    background-color: rgb(50, 55, 70);\n"
"}\n"
"\n"
"QComboBox:focus {\n"
"    border-color: rgb(140, 140, 160);\n"
"    background-color: rgb(55, 60, 75);\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: padding;\n"
"    subcontrol-position: top right;\n"
"    width: 20px;\n"
"    border-left: 1px solid rgb(100, 100, 120);\n"
"    border-top-right-radius: 4px;\n"
"    border-bottom-right-radius: 4px;\n"
"    background-color: rgb(60, 65, 80);\n"
"}\n"
"\n"
"QComboBox::drop-down:hover {\n"
"    background-color: rgb(70, 75, 90);\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    image: none;\n"
"    border-left: 4px solid transparent;\n"
"    bor"
                        "der-right: 4px solid transparent;\n"
"    border-bottom: 6px solid rgb(200, 200, 200);\n"
"    width: 0px;\n"
"    height: 0px;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView {\n"
"    background-color: rgb(45, 50, 65);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 4px;\n"
"    color: rgb(220, 220, 220);\n"
"    selection-background-color: rgb(80, 85, 100);\n"
"    outline: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item {\n"
"    padding: 6px 8px;\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    background-color: rgb(70, 75, 90);\n"
"}\n"
"\n"
"QComboBox QAbstractItemView::item:selected {\n"
"    background-color: rgb(80, 85, 100);\n"
"}", nullptr));
        rangeLabel->setText(QCoreApplication::translate("SignalAnalysisWidget", "\351\242\221\347\216\207\350\214\203\345\233\264:", nullptr));
        frequencyRangeLabel->setText(QCoreApplication::translate("SignalAnalysisWidget", "95.000 - 105.000 MHz", nullptr));
        presetISMBtn->setText(QCoreApplication::translate("SignalAnalysisWidget", "ISM 2.4G", nullptr));
#if QT_CONFIG(tooltip)
        presetISMBtn->setToolTip(QCoreApplication::translate("SignalAnalysisWidget", "\350\256\276\347\275\256\344\270\272ISM 2.4GHz\351\242\221\346\256\265 (2.4-2.5GHz)", nullptr));
#endif // QT_CONFIG(tooltip)
        presetWiFiBtn->setText(QCoreApplication::translate("SignalAnalysisWidget", "WiFi 5G", nullptr));
#if QT_CONFIG(tooltip)
        presetWiFiBtn->setToolTip(QCoreApplication::translate("SignalAnalysisWidget", "\350\256\276\347\275\256\344\270\272WiFi 5GHz\351\242\221\346\256\265 (5.15-5.85GHz)", nullptr));
#endif // QT_CONFIG(tooltip)
        presetFMBtn->setText(QCoreApplication::translate("SignalAnalysisWidget", "FM", nullptr));
#if QT_CONFIG(tooltip)
        presetFMBtn->setToolTip(QCoreApplication::translate("SignalAnalysisWidget", "\350\256\276\347\275\256\344\270\272FM\345\271\277\346\222\255\351\242\221\346\256\265 (88-108MHz)", nullptr));
#endif // QT_CONFIG(tooltip)
        separatorLabel->setText(QCoreApplication::translate("SignalAnalysisWidget", "|", nullptr));
        separatorLabel->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "color: rgb(100, 100, 120); font-weight: bold;", nullptr));
        dynamicDataBtn->setText(QCoreApplication::translate("SignalAnalysisWidget", "\345\220\257\345\212\250\345\212\250\346\200\201\346\225\260\346\215\256", nullptr));
#if QT_CONFIG(tooltip)
        dynamicDataBtn->setToolTip(QCoreApplication::translate("SignalAnalysisWidget", "\345\274\200\345\220\257/\345\205\263\351\227\255\345\212\250\346\200\201\346\225\260\346\215\256\347\224\237\346\210\220", nullptr));
#endif // QT_CONFIG(tooltip)
        dynamicDataBtn->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QPushButton {\n"
"    background-color: rgb(45, 50, 65);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 6px;\n"
"    padding: 6px 12px;\n"
"    color: rgb(220, 220, 220);\n"
"    font-family: Microsoft YaHei;\n"
"    font-size: 10pt;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    border-color: rgb(120, 120, 140);\n"
"    background-color: rgb(50, 55, 70);\n"
"}\n"
"\n"
"QPushButton:checked {\n"
"    background-color: rgb(34, 139, 34);\n"
"    border-color: rgb(50, 205, 50);\n"
"    color: rgb(255, 255, 255);\n"
"}\n"
"\n"
"QPushButton:checked:hover {\n"
"    background-color: rgb(50, 205, 50);\n"
"}", nullptr));
        spectrumGroup->setTitle(QCoreApplication::translate("SignalAnalysisWidget", "\351\242\221\350\260\261\345\233\276", nullptr));
        spectrumGroup->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QGroupBox {\n"
"    color: rgb(200, 200, 200);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 5px;\n"
"    margin-top: 1.5ex;\n"
"    font-weight: bold;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    left: 10px;\n"
"    padding: 2px 5px 2px 5px;\n"
"    color: rgb(220, 220, 220);\n"
"}", nullptr));
        waterfallGroup->setTitle(QCoreApplication::translate("SignalAnalysisWidget", "\347\200\221\345\270\203\345\233\276", nullptr));
        waterfallGroup->setStyleSheet(QCoreApplication::translate("SignalAnalysisWidget", "QGroupBox {\n"
"    color: rgb(200, 200, 200);\n"
"    border: 2px solid rgb(100, 100, 120);\n"
"    border-radius: 5px;\n"
"    margin-top: 1.5ex;\n"
"    font-weight: bold;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    subcontrol-position: top left;\n"
"    left: 10px;\n"
"    padding: 2px 5px 2px 5px;\n"
"    color: rgb(220, 220, 220);\n"
"}", nullptr));
        dataGroup->setTitle(QCoreApplication::translate("SignalAnalysisWidget", "\344\277\241\345\217\267\345\210\227\350\241\250", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SignalAnalysisWidget: public Ui_SignalAnalysisWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SIGNALANALYSIS_H
