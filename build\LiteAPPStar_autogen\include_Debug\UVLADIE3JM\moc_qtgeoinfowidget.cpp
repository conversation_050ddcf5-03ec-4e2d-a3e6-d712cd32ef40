/****************************************************************************
** Meta object code from reading C++ file 'qtgeoinfowidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/qtgeoinfowidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qtgeoinfowidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtGeoInfoWidget_t {
    QByteArrayData data[33];
    char stringdata0[478];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtGeoInfoWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtGeoInfoWidget_t qt_meta_stringdata_QtGeoInfoWidget = {
    {
QT_MOC_LITERAL(0, 0, 15), // "QtGeoInfoWidget"
QT_MOC_LITERAL(1, 16, 17), // "coordinateChanged"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 8), // "latitude"
QT_MOC_LITERAL(4, 44, 9), // "longitude"
QT_MOC_LITERAL(5, 54, 16), // "zoomLevelChanged"
QT_MOC_LITERAL(6, 71, 4), // "zoom"
QT_MOC_LITERAL(7, 76, 10), // "mapClicked"
QT_MOC_LITERAL(8, 87, 13), // "markerClicked"
QT_MOC_LITERAL(9, 101, 8), // "markerId"
QT_MOC_LITERAL(10, 110, 13), // "statusChanged"
QT_MOC_LITERAL(11, 124, 6), // "status"
QT_MOC_LITERAL(12, 131, 16), // "jumpToCoordinate"
QT_MOC_LITERAL(13, 148, 18), // "getCurrentLocation"
QT_MOC_LITERAL(14, 167, 13), // "switchMapType"
QT_MOC_LITERAL(15, 181, 7), // "mapType"
QT_MOC_LITERAL(16, 189, 6), // "zoomIn"
QT_MOC_LITERAL(17, 196, 7), // "zoomOut"
QT_MOC_LITERAL(18, 204, 9), // "resetView"
QT_MOC_LITERAL(19, 214, 12), // "fitToMarkers"
QT_MOC_LITERAL(20, 227, 24), // "onCoordinateInputChanged"
QT_MOC_LITERAL(21, 252, 19), // "onJumpButtonClicked"
QT_MOC_LITERAL(22, 272, 23), // "onLocationButtonClicked"
QT_MOC_LITERAL(23, 296, 16), // "onMapTypeChanged"
QT_MOC_LITERAL(24, 313, 18), // "onAddMarkerClicked"
QT_MOC_LITERAL(25, 332, 21), // "onClearMarkersClicked"
QT_MOC_LITERAL(26, 354, 19), // "onExportDataClicked"
QT_MOC_LITERAL(27, 374, 19), // "onImportDataClicked"
QT_MOC_LITERAL(28, 394, 18), // "onMapCenterChanged"
QT_MOC_LITERAL(29, 413, 16), // "onMapZoomChanged"
QT_MOC_LITERAL(30, 430, 12), // "onMapClicked"
QT_MOC_LITERAL(31, 443, 18), // "onMapDoubleClicked"
QT_MOC_LITERAL(32, 462, 15) // "onMarkerClicked"

    },
    "QtGeoInfoWidget\0coordinateChanged\0\0"
    "latitude\0longitude\0zoomLevelChanged\0"
    "zoom\0mapClicked\0markerClicked\0markerId\0"
    "statusChanged\0status\0jumpToCoordinate\0"
    "getCurrentLocation\0switchMapType\0"
    "mapType\0zoomIn\0zoomOut\0resetView\0"
    "fitToMarkers\0onCoordinateInputChanged\0"
    "onJumpButtonClicked\0onLocationButtonClicked\0"
    "onMapTypeChanged\0onAddMarkerClicked\0"
    "onClearMarkersClicked\0onExportDataClicked\0"
    "onImportDataClicked\0onMapCenterChanged\0"
    "onMapZoomChanged\0onMapClicked\0"
    "onMapDoubleClicked\0onMarkerClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtGeoInfoWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      25,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,  139,    2, 0x06 /* Public */,
       5,    1,  144,    2, 0x06 /* Public */,
       7,    2,  147,    2, 0x06 /* Public */,
       8,    3,  152,    2, 0x06 /* Public */,
      10,    1,  159,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    2,  162,    2, 0x0a /* Public */,
      13,    0,  167,    2, 0x0a /* Public */,
      14,    1,  168,    2, 0x0a /* Public */,
      16,    0,  171,    2, 0x0a /* Public */,
      17,    0,  172,    2, 0x0a /* Public */,
      18,    0,  173,    2, 0x0a /* Public */,
      19,    0,  174,    2, 0x0a /* Public */,
      20,    0,  175,    2, 0x08 /* Private */,
      21,    0,  176,    2, 0x08 /* Private */,
      22,    0,  177,    2, 0x08 /* Private */,
      23,    1,  178,    2, 0x08 /* Private */,
      24,    0,  181,    2, 0x08 /* Private */,
      25,    0,  182,    2, 0x08 /* Private */,
      26,    0,  183,    2, 0x08 /* Private */,
      27,    0,  184,    2, 0x08 /* Private */,
      28,    2,  185,    2, 0x08 /* Private */,
      29,    1,  190,    2, 0x08 /* Private */,
      30,    2,  193,    2, 0x08 /* Private */,
      31,    2,  198,    2, 0x08 /* Private */,
      32,    3,  203,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double,    6,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::Double, QMetaType::Double,    9,    3,    4,
    QMetaType::Void, QMetaType::QString,   11,

 // slots: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double,    6,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::Double, QMetaType::Double,    9,    3,    4,

       0        // eod
};

void QtGeoInfoWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QtGeoInfoWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->coordinateChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->zoomLevelChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 2: _t->mapClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 3: _t->markerClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3]))); break;
        case 4: _t->statusChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->jumpToCoordinate((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 6: _t->getCurrentLocation(); break;
        case 7: _t->switchMapType((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->zoomIn(); break;
        case 9: _t->zoomOut(); break;
        case 10: _t->resetView(); break;
        case 11: _t->fitToMarkers(); break;
        case 12: _t->onCoordinateInputChanged(); break;
        case 13: _t->onJumpButtonClicked(); break;
        case 14: _t->onLocationButtonClicked(); break;
        case 15: _t->onMapTypeChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onAddMarkerClicked(); break;
        case 17: _t->onClearMarkersClicked(); break;
        case 18: _t->onExportDataClicked(); break;
        case 19: _t->onImportDataClicked(); break;
        case 20: _t->onMapCenterChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 21: _t->onMapZoomChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 22: _t->onMapClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 23: _t->onMapDoubleClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 24: _t->onMarkerClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QtGeoInfoWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtGeoInfoWidget::coordinateChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QtGeoInfoWidget::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtGeoInfoWidget::zoomLevelChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QtGeoInfoWidget::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtGeoInfoWidget::mapClicked)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QtGeoInfoWidget::*)(int , double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtGeoInfoWidget::markerClicked)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (QtGeoInfoWidget::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtGeoInfoWidget::statusChanged)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QtGeoInfoWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_QtGeoInfoWidget.data,
    qt_meta_data_QtGeoInfoWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QtGeoInfoWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtGeoInfoWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtGeoInfoWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int QtGeoInfoWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 25)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 25;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 25)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 25;
    }
    return _id;
}

// SIGNAL 0
void QtGeoInfoWidget::coordinateChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtGeoInfoWidget::zoomLevelChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QtGeoInfoWidget::mapClicked(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QtGeoInfoWidget::markerClicked(int _t1, double _t2, double _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QtGeoInfoWidget::statusChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
