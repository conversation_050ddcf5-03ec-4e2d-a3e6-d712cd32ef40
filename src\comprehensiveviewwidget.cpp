#include "comprehensiveviewwidget.h"
#include "ui_comprehensive_view.h"
#include "databasemanager.h"
#include <QDateTime>
#include <QSettings>
#include <QFileDialog>
#include <QTextStream>
#include <QDebug>
#include <QDialog>
#include <QSpinBox>
#include <QMessageBox>
#include <QApplication>
#include <QTimer>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QCoreApplication>
#include <QFile>
#include <QHeaderView>
#include <QInputDialog>
#include <QMessageBox>

// 数据库表名常量
const QString TABLE_NAME = "target_position";

ComprehensiveViewWidget::ComprehensiveViewWidget(QWidget *parent)
    : QWidget(parent), ui(new Ui::ComprehensiveViewWidget),
      m_currentPage(1), m_pageSize(20), m_totalRecords(0),
      m_totalPages(0), m_sortColumn(-1), m_sortOrder(Qt::AscendingOrder),
      m_isLoading(false), m_isDatabaseConnected(false), m_statusBox(nullptr),
      m_tableView(nullptr), m_tableModel(nullptr), m_proxyModel(nullptr),
      m_recordCountLabel(nullptr), m_queryButton(nullptr), m_refreshButton(nullptr),
      m_progressBar(nullptr)
{
    // 设置UI
    ui->setupUi(this);

    // 设置表名
    m_tableName = TABLE_NAME;

    // 初始化可用列
    m_availableColumns << "id" << "system_type" << "ue_class_id" << "importance_level"
                       << "start_time" << "longitude" << "latitude" << "position_response_point_beam_id"
                       << "create_time" << "filepath";

    // 默认显示的列
    m_visibleColumns << "id" << "system_type" << "ue_class_id" << "importance_level"
                     << "start_time" << "create_time";

    // 初始化列可见性
    for (const QString &column : m_availableColumns)
    {
        m_columnVisibility[column] = m_visibleColumns.contains(column);
    }

    // 初始化UI组件
    initializeUIComponents();

    // 连接信号
    connectSignals();

    // 延迟初始化数据库，避免阻塞界面显示
    QTimer::singleShot(100, this, [this]()
                       { initializeDatabase(); });

    qDebug() << "ComprehensiveViewWidget: 综合阅览窗口初始化完成";
}

ComprehensiveViewWidget::~ComprehensiveViewWidget()
{
    // 断开数据库连接
    disconnectFromDatabase();

    delete ui;

    qDebug() << "ComprehensiveViewWidget: 综合阅览窗口已销毁";
}

bool ComprehensiveViewWidget::testDirectMySQLConnection()
{
    qDebug() << "ComprehensiveViewWidget: 开始直接MySQL连接测试";

    // 创建一个临时的数据库连接进行测试
    QString testConnectionName = "test_mysql_connection";

    // 如果连接已存在，先移除
    if (QSqlDatabase::contains(testConnectionName))
    {
        QSqlDatabase::removeDatabase(testConnectionName);
    }

    // 创建MySQL连接
    QSqlDatabase testDb = QSqlDatabase::addDatabase("QMYSQL", testConnectionName);
    testDb.setHostName("127.0.0.1");
    testDb.setPort(3306);
    testDb.setDatabaseName("test");
    testDb.setUserName("root");
    testDb.setPassword("123456");

    // 设置连接选项
    QString options = "MYSQL_OPT_RECONNECT=1;";
    options += "MYSQL_OPT_CONNECT_TIMEOUT=10;";
    options += "CLIENT_SSL=0;"; // 禁用SSL
    testDb.setConnectOptions(options);

    qDebug() << "ComprehensiveViewWidget: 尝试连接到MySQL数据库...";
    qDebug() << "  主机: 127.0.0.1:3306";
    qDebug() << "  数据库: test";
    qDebug() << "  用户名: root";

    bool success = testDb.open();

    if (success)
    {
        qDebug() << "ComprehensiveViewWidget: 直接MySQL连接成功！";

        // 测试执行一个简单的查询
        QSqlQuery query(testDb);
        if (query.exec("SELECT 1 as test"))
        {
            qDebug() << "ComprehensiveViewWidget: MySQL查询测试成功";
        }
        else
        {
            qWarning() << "ComprehensiveViewWidget: MySQL查询测试失败:" << query.lastError().text();
        }

        testDb.close();
    }
    else
    {
        QString error = testDb.lastError().text();
        qWarning() << "ComprehensiveViewWidget: 直接MySQL连接失败:" << error;

        // 显示详细的连接错误信息
        QString detailedError = QString("直接MySQL连接测试失败:\n\n");
        detailedError += QString("错误信息: %1\n\n").arg(error);
        detailedError += "连接参数:\n";
        detailedError += "- 主机: 127.0.0.1\n";
        detailedError += "- 端口: 3306\n";
        detailedError += "- 数据库: test\n";
        detailedError += "- 用户名: root\n";
        detailedError += "- 密码: 123456\n\n";

        detailedError += "请检查:\n";
        detailedError += "1. MySQL服务是否启动 (net start mysql)\n";
        detailedError += "2. 用户名和密码是否正确\n";
        detailedError += "3. 数据库'test'是否存在\n";
        detailedError += "4. 防火墙是否阻止连接\n";
        detailedError += "5. MySQL是否监听3306端口\n\n";

        detailedError += "测试命令:\n";
        detailedError += "mysql -h 127.0.0.1 -P 3306 -u root -p123456 -e \"SELECT 1\"";

        QMessageBox::critical(this, "MySQL连接测试失败", detailedError);
    }

    // 清理测试连接
    if (QSqlDatabase::contains(testConnectionName))
    {
        QSqlDatabase::removeDatabase(testConnectionName);
    }

    return success;
}

void ComprehensiveViewWidget::initializeUIComponents()
{
    qDebug() << "ComprehensiveViewWidget: 开始初始化UI组件";

    // 直接从UI文件获取组件引用
    if (ui)
    {
        // 获取主要的UI组件
        m_recordCountLabel = ui->recordCountLabel;
        m_queryButton = ui->queryButton;
        m_refreshButton = ui->refreshButton;
        m_tableView = ui->tableView;
        m_progressBar = ui->progressBar;

        // 获取其他按钮
        QPushButton *resetButton = ui->resetButton;
        QPushButton *columnSettingsButton = ui->columnSettingsButton;
        QPushButton *exportButton = ui->exportButton;

        // 获取分页按钮
        QPushButton *firstPageButton = ui->firstPageButton;
        QPushButton *previousPageButton = ui->previousPageButton;
        QPushButton *nextPageButton = ui->nextPageButton;
        QPushButton *lastPageButton = ui->lastPageButton;
        QPushButton *goToPageButton = ui->goToPageButton;

        // 验证关键组件是否存在
        if (m_recordCountLabel)
        {
            qDebug() << "✅ recordCountLabel 组件获取成功";
            m_recordCountLabel->setText("正在初始化...");
        }
        else
        {
            qWarning() << "❌ recordCountLabel 组件获取失败";
        }

        if (m_queryButton)
        {
            qDebug() << "✅ queryButton 组件获取成功";
            m_queryButton->setEnabled(true);
        }
        else
        {
            qWarning() << "❌ queryButton 组件获取失败";
        }

        if (m_refreshButton)
        {
            qDebug() << "✅ refreshButton 组件获取成功";
            m_refreshButton->setEnabled(true);
        }
        else
        {
            qWarning() << "❌ refreshButton 组件获取失败";
        }

        if (m_tableView)
        {
            qDebug() << "✅ tableView 组件获取成功";
            m_tableView->setEnabled(true);
        }
        else
        {
            qWarning() << "❌ tableView 组件获取失败";
        }

        if (m_progressBar)
        {
            qDebug() << "✅ progressBar 组件获取成功";
            m_progressBar->setVisible(false); // 默认隐藏
        }
        else
        {
            qWarning() << "❌ progressBar 组件获取失败";
        }

        // 启用其他按钮
        if (resetButton)
        {
            resetButton->setEnabled(true);
            qDebug() << "✅ resetButton 组件获取成功";
        }

        if (columnSettingsButton)
        {
            columnSettingsButton->setEnabled(true);
            qDebug() << "✅ columnSettingsButton 组件获取成功";
        }

        if (exportButton)
        {
            exportButton->setEnabled(true);
            qDebug() << "✅ exportButton 组件获取成功";
        }

        // 启用分页按钮
        if (firstPageButton)
            firstPageButton->setEnabled(true);
        if (previousPageButton)
            previousPageButton->setEnabled(true);
        if (nextPageButton)
            nextPageButton->setEnabled(true);
        if (lastPageButton)
            lastPageButton->setEnabled(true);
        if (goToPageButton)
            goToPageButton->setEnabled(true);

        qDebug() << "ComprehensiveViewWidget: 所有UI组件初始化完成";
    }
    else
    {
        qCritical() << "ComprehensiveViewWidget: UI对象为空，无法初始化组件！";
    }
}

void ComprehensiveViewWidget::initializeDatabase()
{
    qDebug() << "ComprehensiveViewWidget: 开始初始化数据库连接";

    // 异步连接数据库，避免阻塞界面
    QTimer::singleShot(10, this, [this]()
                       {
        qDebug() << "ComprehensiveViewWidget: 开始异步数据库连接";

        // 先设置UI为可用状态，即使数据库连接失败也能操作
        enableUI(true);

        bool success = connectToDatabase();

        // 更新状态显示
        if (m_recordCountLabel) {
            if (success) {
                m_recordCountLabel->setText("数据库连接成功 - 可以进行查询操作");
                // 自动执行一次查询来显示数据
                QTimer::singleShot(500, this, [this]() {
                    executeQuery();
                });
            } else {
                m_recordCountLabel->setText("数据库连接失败 - 请检查MySQL服务或点击重试");
                // 即使数据库连接失败，也要确保UI可用
                setupOfflineMode();
            }
        } });
}

bool ComprehensiveViewWidget::connectToDatabase()
{
    qDebug() << "ComprehensiveViewWidget: 使用DatabaseManager连接数据库";

    // 首先检查MySQL驱动是否可用
    QStringList drivers = QSqlDatabase::drivers();
    qDebug() << "ComprehensiveViewWidget: 可用的数据库驱动:" << drivers;

    if (!drivers.contains("QMYSQL"))
    {
        QString error = "MySQL驱动(QMYSQL)不可用！\n\n";
        error += "可用的驱动: " + drivers.join(", ") + "\n\n";
        error += "解决方案:\n";
        error += "1. 确保Qt安装时包含了MySQL驱动\n";
        error += "2. 检查Qt安装目录下的plugins/sqldrivers/目录\n";
        error += "3. 重新安装Qt并选择MySQL支持\n";
        error += "4. 或者下载MySQL驱动插件";

        QMessageBox::critical(this, "MySQL驱动错误", error);
        m_isDatabaseConnected = false;
        return false;
    }

    qDebug() << "ComprehensiveViewWidget: MySQL驱动可用，继续连接";

    // 初始化DatabaseManager
    QString configPath = QCoreApplication::applicationDirPath() + "/../../config/database.json";
    if (!QFile::exists(configPath))
    {
        // 尝试当前目录
        configPath = "config/database.json";
        if (!QFile::exists(configPath))
        {
            qWarning() << "ComprehensiveViewWidget: 配置文件不存在:" << configPath;
            showErrorMessage("配置文件错误", "数据库配置文件不存在，请检查config/database.json文件");
            m_isDatabaseConnected = false;
            return false;
        }
    }

    qDebug() << "ComprehensiveViewWidget: 使用配置文件:" << configPath;

    // 在初始化DatabaseManager之前，先进行直接的MySQL连接测试
    if (!testDirectMySQLConnection())
    {
        qWarning() << "ComprehensiveViewWidget: 直接MySQL连接测试失败";
        m_isDatabaseConnected = false;
        return false;
    }

    if (!DB_MANAGER->initialize(configPath))
    {
        QString error = DB_MANAGER->lastError();
        qWarning() << "ComprehensiveViewWidget: DatabaseManager初始化失败:" << error;

        // 显示详细的错误信息和解决方案
        QString detailedError = QString("数据库管理器初始化失败:\n\n错误信息: %1\n\n").arg(error);
        detailedError += "可能的解决方案:\n";
        detailedError += "1. 检查MySQL服务是否启动:\n";
        detailedError += "   - Windows: 服务管理器中启动MySQL服务\n";
        detailedError += "   - 命令行: net start mysql\n\n";
        detailedError += "2. 检查数据库连接参数:\n";
        detailedError += "   - 主机地址: 127.0.0.1\n";
        detailedError += "   - 端口: 3306\n";
        detailedError += "   - 用户名: root\n";
        detailedError += "   - 密码: 123456\n\n";
        detailedError += "3. 测试数据库连接:\n";
        detailedError += "   mysql -h 127.0.0.1 -P 3306 -u root -p123456\n\n";
        detailedError += "程序将以离线模式运行，您仍可以使用界面功能。";

        // 使用信息框而不是错误框，避免阻塞
        QMessageBox::information(this, "数据库连接提示", detailedError);

        m_isDatabaseConnected = false;
        return false;
    }

    // 测试连接
    if (!DB_MANAGER->testConnection())
    {
        QString error = DB_MANAGER->lastError();
        qWarning() << "ComprehensiveViewWidget: 数据库连接测试失败:" << error;

        QString connectionError = QString("数据库连接测试失败:\n\n错误信息: %1\n\n").arg(error);
        connectionError += "这通常表示:\n";
        connectionError += "• MySQL服务未启动\n";
        connectionError += "• 连接参数错误（主机、端口、用户名、密码）\n";
        connectionError += "• 防火墙阻止连接\n";
        connectionError += "• MySQL用户权限不足\n\n";
        connectionError += "程序将继续以离线模式运行。";

        QMessageBox::information(this, "数据库连接测试失败", connectionError);

        m_isDatabaseConnected = false;
        return false;
    }

    // 检查表是否存在，如果不存在则创建
    if (!DB_MANAGER->tableExists(m_tableName))
    {
        qDebug() << "ComprehensiveViewWidget: 表不存在，尝试创建表:" << m_tableName;

        QString createTableSql = QString(
                                     "CREATE TABLE IF NOT EXISTS %1 ("
                                     "id INT AUTO_INCREMENT PRIMARY KEY, "
                                     "target_id VARCHAR(50) NOT NULL, "
                                     "target_type VARCHAR(20) NOT NULL, "
                                     "latitude DECIMAL(10,8) NOT NULL, "
                                     "longitude DECIMAL(11,8) NOT NULL, "
                                     "altitude DECIMAL(8,2) DEFAULT 0, "
                                     "speed DECIMAL(6,2) DEFAULT 0, "
                                     "heading DECIMAL(5,2) DEFAULT 0, "
                                     "status VARCHAR(20) DEFAULT '活跃', "
                                     "timestamp DATETIME DEFAULT CURRENT_TIMESTAMP, "
                                     "created_at DATETIME DEFAULT CURRENT_TIMESTAMP, "
                                     "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
                                     ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4")
                                     .arg(m_tableName);

        QueryResult result = DB_MANAGER->executeQuery(createTableSql);
        if (result.hasError())
        {
            QString error = result.lastError();
            qWarning() << "ComprehensiveViewWidget: 创建表失败:" << error;
            showErrorMessage("数据库表创建错误", QString("无法创建表 %1: %2").arg(m_tableName).arg(error));
            m_isDatabaseConnected = false;
            return false;
        }

        qDebug() << "ComprehensiveViewWidget: 表创建成功:" << m_tableName;

        // 插入一些测试数据
        insertTestData();
    }

    // 创建表格模型
    m_tableModel = new QStandardItemModel(this);

    // 创建代理模型用于排序和过滤
    m_proxyModel = new QSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_tableModel);

    // 设置表格视图模型
    if (m_tableView)
    {
        m_tableView->setModel(m_proxyModel);

        // 连接表格头部点击信号
        connect(m_tableView->horizontalHeader(), &QHeaderView::sectionClicked,
                this, &ComprehensiveViewWidget::onHeaderClicked);
    }

    m_isDatabaseConnected = true;

    // 关闭状态对话框
    if (m_statusBox)
    {
        m_statusBox->close();
        m_statusBox->deleteLater();
        m_statusBox = nullptr;
    }

    showSuccessMessage("数据库连接成功");
    qDebug() << "ComprehensiveViewWidget: 数据库连接成功";

    return true;
}

void ComprehensiveViewWidget::disconnectFromDatabase()
{
    if (m_isDatabaseConnected)
    {
        // DatabaseManager会自动管理连接，这里只需要重置状态
        m_isDatabaseConnected = false;
        qDebug() << "ComprehensiveViewWidget: 数据库连接已断开";
    }
}

void ComprehensiveViewWidget::executeQuery()
{
    if (!DB_MANAGER->isConnected())
    {
        showErrorMessage("查询失败", "数据库未连接");
        return;
    }

    showLoadingState(true);

    // 构建查询条件
    QStringList conditions;
    QVariantList params;

    // 构建WHERE子句
    QString whereClause;
    if (!conditions.isEmpty())
    {
        whereClause = conditions.join(" AND ");
    }

    // 构建ORDER BY子句
    QString orderBy = "id DESC"; // 默认按ID降序

    // 计算总记录数 - 添加详细诊断
    qDebug() << "=== 数据库查询诊断 ===";
    qDebug() << "1. 数据库连接状态:" << DB_MANAGER->isConnected();
    qDebug() << "2. 表名:" << m_tableName;
    qDebug() << "3. WHERE条件:" << (whereClause.isEmpty() ? "无条件" : whereClause);
    qDebug() << "4. 参数:" << params;
    qDebug() << "5. 最后错误:" << DB_MANAGER->lastError();

    // 检查表是否存在
    bool tableExists = DB_MANAGER->tableExists(m_tableName);
    qDebug() << "6. 表是否存在:" << tableExists;

    if (!tableExists)
    {
        qWarning() << "表不存在，请先创建表或检查表名";
        showErrorMessage("查询失败", QString("表 '%1' 不存在，请检查数据库配置").arg(m_tableName));
        showLoadingState(false);
        return;
    }

    int totalCount = DB_MANAGER->count(m_tableName, whereClause, params);
    qDebug() << "7. 查询结果:" << totalCount;
    qDebug() << "8. 查询后错误:" << DB_MANAGER->lastError();

    if (totalCount < 0)
    {
        showErrorMessage("查询失败", QString("获取记录总数失败: %1").arg(DB_MANAGER->lastError()));
        showLoadingState(false);
        return;
    }

    m_totalRecords = totalCount;
    m_totalPages = (m_totalRecords + m_pageSize - 1) / m_pageSize;

    // 计算分页参数
    int offset = (m_currentPage - 1) * m_pageSize;

    // 执行查询
    QueryResult result = DB_MANAGER->select(m_tableName, m_visibleColumns, whereClause, params, orderBy, m_pageSize, offset);

    if (result.hasError())
    {
        showErrorMessage("查询失败", QString("数据库查询失败: %1").arg(result.lastError()));
        showLoadingState(false);
        return;
    }

    // 更新表格模型
    updateTableModel(result);

    // 更新分页信息
    updatePaginationInfo();

    showLoadingState(false);

    qDebug() << "ComprehensiveViewWidget: 查询完成，总记录数:" << m_totalRecords;
}

void ComprehensiveViewWidget::updateTableModel(const QueryResult &result)
{
    if (!m_tableModel)
    {
        return;
    }

    // 清空现有数据
    m_tableModel->clear();

    // 设置列标题
    m_tableModel->setHorizontalHeaderLabels(m_visibleColumns);

    // 获取所有记录
    QVariantList records = result.recordList();

    // 设置行数
    m_tableModel->setRowCount(records.size());
    m_tableModel->setColumnCount(m_visibleColumns.size());

    // 填充数据
    for (int row = 0; row < records.size(); ++row)
    {
        QVariantMap record = records.at(row).toMap();

        for (int col = 0; col < m_visibleColumns.size(); ++col)
        {
            QString columnName = m_visibleColumns.at(col);
            QVariant value = record.value(columnName);

            QStandardItem *item = new QStandardItem(value.toString());
            item->setEditable(false); // 设置为只读
            m_tableModel->setItem(row, col, item);
        }
    }

    // 更新表格视图
    updateTableView();
}

void ComprehensiveViewWidget::insertTestData()
{
    if (!DB_MANAGER->isConnected())
    {
        qWarning() << "ComprehensiveViewWidget: 数据库未连接，无法插入测试数据";
        return;
    }

    qDebug() << "ComprehensiveViewWidget: 插入测试数据";

    // 准备测试数据
    QVariantMap data1;
    data1["target_id"] = "T001";
    data1["target_type"] = "飞机";
    data1["latitude"] = 39.9042;
    data1["longitude"] = 116.4074;
    data1["altitude"] = 10000.0;
    data1["speed"] = 850.0;
    data1["heading"] = 90.0;
    data1["status"] = "活跃";

    if (!DB_MANAGER->insert(m_tableName, data1))
    {
        qWarning() << "ComprehensiveViewWidget: 插入测试数据失败:" << DB_MANAGER->lastError();
    }

    qDebug() << "ComprehensiveViewWidget: 测试数据插入完成";
}

// 显示错误消息
void ComprehensiveViewWidget::showErrorMessage(const QString &title, const QString &message)
{
    QMessageBox::critical(this, title, message);
    qWarning() << "ComprehensiveViewWidget Error:" << title << "-" << message;
}

// 显示成功消息
void ComprehensiveViewWidget::showSuccessMessage(const QString &message)
{
    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText(message);
    }
    qDebug() << "ComprehensiveViewWidget Success:" << message;
}

// 显示加载状态
void ComprehensiveViewWidget::showLoadingState(bool loading)
{
    m_isLoading = loading;

    if (m_progressBar)
    {
        m_progressBar->setVisible(loading);
        if (loading)
        {
            m_progressBar->setRange(0, 0); // 无限进度条
        }
    }

    if (m_queryButton)
    {
        m_queryButton->setEnabled(!loading);
        m_queryButton->setText(loading ? "查询中..." : "查询");
    }

    if (m_refreshButton)
    {
        m_refreshButton->setEnabled(!loading);
    }

    if (loading && m_recordCountLabel)
    {
        m_recordCountLabel->setText("正在查询数据...");
    }
}

// 空实现的方法，避免编译错误
void ComprehensiveViewWidget::updatePaginationInfo() {}
void ComprehensiveViewWidget::updateTableView() {}
void ComprehensiveViewWidget::onHeaderClicked(int) {}
void ComprehensiveViewWidget::loadColumnSettings() {}
void ComprehensiveViewWidget::saveColumnSettings() {}
void ComprehensiveViewWidget::initializeUIReferences() {}
void ComprehensiveViewWidget::connectSignals()
{
    qDebug() << "ComprehensiveViewWidget: 开始连接信号";

    // 连接主要按钮信号
    if (m_queryButton)
    {
        connect(m_queryButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onQueryButtonClicked);
        qDebug() << "✅ 查询按钮信号已连接";
    }
    else
    {
        qWarning() << "❌ 查询按钮为空，无法连接信号";
    }

    if (m_refreshButton)
    {
        connect(m_refreshButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onRefreshButtonClicked);
        qDebug() << "✅ 刷新按钮信号已连接";
    }
    else
    {
        qWarning() << "❌ 刷新按钮为空，无法连接信号";
    }

    // 连接其他按钮信号
    if (ui)
    {
        if (ui->resetButton)
        {
            connect(ui->resetButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onResetButtonClicked);
            qDebug() << "✅ 重置按钮信号已连接";
        }

        if (ui->columnSettingsButton)
        {
            connect(ui->columnSettingsButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::showColumnSettingsDialog);
            qDebug() << "✅ 列设置按钮信号已连接";
        }

        if (ui->exportButton)
        {
            connect(ui->exportButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onExportButtonClicked);
            qDebug() << "✅ 导出按钮信号已连接";
        }

        // 连接分页按钮信号
        if (ui->firstPageButton)
        {
            connect(ui->firstPageButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onFirstPageClicked);
            qDebug() << "✅ 首页按钮信号已连接";
        }

        if (ui->previousPageButton)
        {
            connect(ui->previousPageButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onPreviousPageClicked);
            qDebug() << "✅ 上一页按钮信号已连接";
        }

        if (ui->nextPageButton)
        {
            connect(ui->nextPageButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onNextPageClicked);
            qDebug() << "✅ 下一页按钮信号已连接";
        }

        if (ui->lastPageButton)
        {
            connect(ui->lastPageButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onLastPageClicked);
            qDebug() << "✅ 末页按钮信号已连接";
        }

        if (ui->goToPageButton)
        {
            connect(ui->goToPageButton, &QPushButton::clicked, this, &ComprehensiveViewWidget::onGoToPageClicked);
            qDebug() << "✅ 跳转按钮信号已连接";
        }
    }

    qDebug() << "ComprehensiveViewWidget: 所有信号连接完成";
}
void ComprehensiveViewWidget::onQueryButtonClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击查询按钮";

    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("正在查询数据...");
    }

    if (!m_isDatabaseConnected)
    {
        if (m_recordCountLabel)
        {
            m_recordCountLabel->setText("数据库未连接，正在尝试重新连接...");
        }

        // 尝试重新连接数据库
        QTimer::singleShot(100, this, [this]()
                           {
            if (connectToDatabase()) {
                executeQuery();
            } else {
                if (m_recordCountLabel) {
                    m_recordCountLabel->setText("数据库连接失败 - 请检查MySQL服务");
                }
            } });
        return;
    }

    // 执行查询
    executeQuery();
}

void ComprehensiveViewWidget::onResetButtonClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击重置按钮";

    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("查询条件已重置");
    }

    // 重置查询条件
    resetQueryConditions();
}

void ComprehensiveViewWidget::onRefreshButtonClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击刷新按钮";

    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("正在重新连接数据库...");
    }

    // 重置连接状态
    m_isDatabaseConnected = false;

    // 重新初始化数据库连接
    QTimer::singleShot(100, this, [this]()
                       { initializeDatabase(); });
}

void ComprehensiveViewWidget::resetQueryConditions()
{
    qDebug() << "ComprehensiveViewWidget: 重置查询条件";

    // 重置分页
    m_currentPage = 1;
    m_totalPages = 0;
    m_totalRecords = 0;

    // 重置排序
    m_sortColumn = -1;
    m_sortOrder = Qt::AscendingOrder;

    // 清空表格
    if (m_tableModel)
    {
        m_tableModel->clear();
    }

    qDebug() << "ComprehensiveViewWidget: 查询条件重置完成";
}

void ComprehensiveViewWidget::onExportButtonClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击导出按钮";

    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("导出功能开发中...");
    }

    QMessageBox::information(this, "导出功能", "导出功能正在开发中，敬请期待！");
}

void ComprehensiveViewWidget::onFirstPageClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击首页按钮";

    m_currentPage = 1;
    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText(QString("跳转到第1页"));
    }

    executeQuery();
}

void ComprehensiveViewWidget::onPreviousPageClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击上一页按钮";

    if (m_currentPage > 1)
    {
        m_currentPage--;
        if (m_recordCountLabel)
        {
            m_recordCountLabel->setText(QString("跳转到第%1页").arg(m_currentPage));
        }
        executeQuery();
    }
}

void ComprehensiveViewWidget::onNextPageClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击下一页按钮";

    if (m_currentPage < m_totalPages)
    {
        m_currentPage++;
        if (m_recordCountLabel)
        {
            m_recordCountLabel->setText(QString("跳转到第%1页").arg(m_currentPage));
        }
        executeQuery();
    }
}

void ComprehensiveViewWidget::onLastPageClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击末页按钮";

    if (m_totalPages > 0)
    {
        m_currentPage = m_totalPages;
        if (m_recordCountLabel)
        {
            m_recordCountLabel->setText(QString("跳转到第%1页").arg(m_currentPage));
        }
        executeQuery();
    }
}

void ComprehensiveViewWidget::onGoToPageClicked()
{
    qDebug() << "ComprehensiveViewWidget: 用户点击跳转按钮";

    bool ok;
    int page = QInputDialog::getInt(this, "跳转页面", "请输入页码:", m_currentPage, 1, m_totalPages, 1, &ok);

    if (ok && page != m_currentPage)
    {
        m_currentPage = page;
        if (m_recordCountLabel)
        {
            m_recordCountLabel->setText(QString("跳转到第%1页").arg(m_currentPage));
        }
        executeQuery();
    }
}

// 其他槽函数的空实现
void ComprehensiveViewWidget::onPageSizeChanged(int pageSize)
{
    qDebug() << "ComprehensiveViewWidget: 页面大小改变为" << pageSize;
    m_pageSize = pageSize;
    m_currentPage = 1; // 重置到第一页
    executeQuery();
}

void ComprehensiveViewWidget::onTableContextMenu(const QPoint &pos)
{
    qDebug() << "ComprehensiveViewWidget: 表格右键菜单" << pos;
    // 右键菜单功能待实现
}

void ComprehensiveViewWidget::showColumnSettingsDialog()
{
    qDebug() << "ComprehensiveViewWidget: 显示列设置对话框";
    QMessageBox::information(this, "列设置", "列设置功能正在开发中，敬请期待！");
}

void ComprehensiveViewWidget::initializeUI()
{
    qDebug() << "ComprehensiveViewWidget: initializeUI 调用";
    // 这个方法已经被 initializeUIComponents 替代
}

void ComprehensiveViewWidget::enableUI(bool enabled)
{
    // 启用/禁用UI组件
    if (m_queryButton)
    {
        m_queryButton->setEnabled(enabled);
    }
    if (m_refreshButton)
    {
        m_refreshButton->setEnabled(enabled);
    }
    if (m_tableView)
    {
        m_tableView->setEnabled(enabled);
    }

    qDebug() << "ComprehensiveViewWidget: UI状态设置为" << (enabled ? "启用" : "禁用");
}

void ComprehensiveViewWidget::setupOfflineMode()
{
    qDebug() << "ComprehensiveViewWidget: 设置离线模式";

    // 确保UI组件可用
    enableUI(true);

    // 创建一个简单的表格模型显示提示信息
    if (!m_tableModel)
    {
        m_tableModel = new QStandardItemModel(this);
    }

    if (!m_proxyModel)
    {
        m_proxyModel = new QSortFilterProxyModel(this);
        m_proxyModel->setSourceModel(m_tableModel);
    }

    if (m_tableView)
    {
        m_tableView->setModel(m_proxyModel);
    }

    // 显示离线提示信息
    m_tableModel->clear();
    m_tableModel->setHorizontalHeaderLabels(QStringList() << "状态" << "说明");

    QStandardItem *statusItem = new QStandardItem("离线模式");
    QStandardItem *descItem = new QStandardItem("数据库连接失败，请检查MySQL服务状态");
    statusItem->setEditable(false);
    descItem->setEditable(false);

    m_tableModel->setItem(0, 0, statusItem);
    m_tableModel->setItem(0, 1, descItem);

    // 添加操作提示
    QStandardItem *actionItem = new QStandardItem("操作提示");
    QStandardItem *actionDescItem = new QStandardItem("1. 检查MySQL服务是否启动\n2. 检查config/database.json配置\n3. 点击刷新按钮重试连接");
    actionItem->setEditable(false);
    actionDescItem->setEditable(false);

    m_tableModel->setItem(1, 0, actionItem);
    m_tableModel->setItem(1, 1, actionDescItem);

    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("离线模式 - 数据库连接失败，但界面仍可操作");
    }
}
void ComprehensiveViewWidget::onColumnVisibilityChanged()
{
    qDebug() << "ComprehensiveViewWidget: 列可见性改变";
    // 列可见性功能待实现
}

void ComprehensiveViewWidget::onColumnOrderChanged()
{
    qDebug() << "ComprehensiveViewWidget: 列顺序改变";
    // 列顺序功能待实现
}

void ComprehensiveViewWidget::onDatabaseConnected()
{
    qDebug() << "ComprehensiveViewWidget: 数据库连接成功";
    m_isDatabaseConnected = true;
    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText("数据库连接成功 - 可以进行查询操作");
    }
}

void ComprehensiveViewWidget::onDatabaseError(const QString &error)
{
    qDebug() << "ComprehensiveViewWidget: 数据库错误:" << error;
    m_isDatabaseConnected = false;
    if (m_recordCountLabel)
    {
        m_recordCountLabel->setText(QString("数据库错误: %1").arg(error));
    }
}
