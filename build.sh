#!/bin/bash

# LiteAPPStar 构建脚本
# 支持Linux和macOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "LiteAPPStar 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -c, --clean         清理构建文件"
    echo "  -r, --release       发布版本构建 (默认为调试版本)"
    echo "  -q, --qmake         使用qmake构建 (默认使用CMake)"
    echo "  -j, --jobs N        并行构建任务数 (默认为CPU核心数)"
    echo "  --install           构建后安装"
    echo "  --package           创建安装包"
    echo ""
    echo "示例:"
    echo "  $0                  # 使用CMake进行调试构建"
    echo "  $0 -r               # 使用CMake进行发布构建"
    echo "  $0 -q -r            # 使用qmake进行发布构建"
    echo "  $0 -c               # 清理构建文件"
    echo "  $0 -r --package     # 发布构建并创建安装包"
}

# 检查依赖
check_dependencies() {
    print_info "检查构建依赖..."
    
    # 检查Qt5
    if ! command -v qmake &> /dev/null; then
        print_error "未找到qmake，请安装Qt5开发环境"
        exit 1
    fi
    
    # 检查CMake (如果使用CMake构建)
    if [ "$USE_QMAKE" != "1" ] && ! command -v cmake &> /dev/null; then
        print_error "未找到cmake，请安装CMake"
        exit 1
    fi
    
    # 检查编译器
    if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
        print_error "未找到C++编译器，请安装GCC或Clang"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."
    
    rm -rf build/
    rm -rf bin/
    rm -f Makefile
    rm -f .qmake.stash
    find . -name "moc_*.cpp" -delete
    find . -name "moc_*.h" -delete
    find . -name "qrc_*.cpp" -delete
    find . -name "ui_*.h" -delete
    find . -name "*.o" -delete
    find . -name "*.obj" -delete
    
    print_success "清理完成"
}

# CMake构建
cmake_build() {
    print_info "使用CMake构建..."
    
    BUILD_TYPE="Debug"
    if [ "$RELEASE_BUILD" = "1" ]; then
        BUILD_TYPE="Release"
    fi
    
    print_info "构建类型: $BUILD_TYPE"
    
    # 配置
    cmake -B build -S . -DCMAKE_BUILD_TYPE=$BUILD_TYPE
    
    # 构建
    cmake --build build --config $BUILD_TYPE -j $JOBS
    
    print_success "CMake构建完成"
    
    if [ "$INSTALL" = "1" ]; then
        print_info "安装应用程序..."
        cmake --install build
        print_success "安装完成"
    fi
    
    if [ "$PACKAGE" = "1" ]; then
        print_info "创建安装包..."
        cd build
        cpack
        cd ..
        print_success "安装包创建完成"
    fi
}

# qmake构建
qmake_build() {
    print_info "使用qmake构建..."
    
    CONFIG="debug"
    if [ "$RELEASE_BUILD" = "1" ]; then
        CONFIG="release"
    fi
    
    print_info "构建类型: $CONFIG"
    
    # 生成Makefile
    qmake LiteAPPStar.pro CONFIG+=$CONFIG
    
    # 构建
    make -j $JOBS
    
    print_success "qmake构建完成"
}

# 默认参数
USE_QMAKE=0
RELEASE_BUILD=0
CLEAN_ONLY=0
INSTALL=0
PACKAGE=0
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN_ONLY=1
            shift
            ;;
        -r|--release)
            RELEASE_BUILD=1
            shift
            ;;
        -q|--qmake)
            USE_QMAKE=1
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        --install)
            INSTALL=1
            shift
            ;;
        --package)
            PACKAGE=1
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主程序
main() {
    print_info "LiteAPPStar 构建脚本启动"
    
    # 如果只是清理，执行清理后退出
    if [ "$CLEAN_ONLY" = "1" ]; then
        clean_build
        exit 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 构建
    if [ "$USE_QMAKE" = "1" ]; then
        qmake_build
    else
        cmake_build
    fi
    
    print_success "构建完成！"
    
    # 显示可执行文件位置
    if [ "$USE_QMAKE" = "1" ]; then
        print_info "可执行文件位置: ./bin/LiteAPPStar"
    else
        print_info "可执行文件位置: ./build/bin/LiteAPPStar"
    fi
}

# 运行主程序
main
