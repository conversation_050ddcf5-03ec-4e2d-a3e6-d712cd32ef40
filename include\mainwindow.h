#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QVBoxLayout>
#include <QMessageBox>
#include <QApplication>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class MainWindow;
}
QT_END_NAMESPACE

// 前向声明
class SignalAnalysisWidget;
// class GeoInfoWidget;  // 暂时禁用
// class QtGeoInfoWidget;  // 暂时禁用
class QgisMapWidget;
class ComprehensiveViewWidget;
class QStackedWidget;

/**
 * @brief 主窗口类
 *
 * 负责管理整个应用程序的主界面，集成信号分析模块
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit MainWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MainWindow();

protected:
    /**
     * @brief 关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    // 文件菜单槽函数
    void newFile();
    void open();
    void save();
    void saveAs();
    void exitApp();

    // 分析菜单槽函数
    void showSignalAnalysis(); // 切换到信号分析界面

    // 地理信息菜单槽函数
    void showQgisMap();           // QGIS地图
    void showComprehensiveView(); // 综合阅览
    void onMouseCoordinateChanged(double lat, double lng);

    // 视图菜单槽函数
    void toggleSpectrum(bool show);
    void toggleWaterfall(bool show);
    void toggleDataTable(bool show);

    // 帮助菜单槽函数
    void about();
    void aboutQt();

    // 移除了信号分析模块槽函数

private:
    /**
     * @brief 初始化UI组件
     */
    void initializeUI();

    /**
     * @brief 创建菜单和工具栏
     */
    void createMenusAndToolbars();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 读取设置
     */
    void readSettings();

    /**
     * @brief 写入设置
     */
    void writeSettings();

    /**
     * @brief 检查是否需要保存
     * @return true表示可以继续操作，false表示用户取消
     */
    bool maybeSave();

private:
    Ui::MainWindow *ui;                     ///< UI界面指针
    QStackedWidget *m_stackedWidget;        ///< 堆叠窗口管理器
    SignalAnalysisWidget *m_signalAnalysis; ///< 信号分析模块
    // GeoInfoWidget *m_geoInfo;               ///< 地理信息模块（原版）- 已移除
    // QtGeoInfoWidget *m_qtGeoInfo;           ///< Qt Location地理信息模块 - 已移除
    QgisMapWidget *m_qgisMap;                     ///< QGIS地图模块
    ComprehensiveViewWidget *m_comprehensiveView; ///< 综合阅览模块

    QString m_currentFile; ///< 当前文件路径
};

#endif // MAINWINDOW_H
