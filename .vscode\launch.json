{"version": "0.2.0", "configurations": [{"name": "Debug LiteAPPStar (CMake - VS2022)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/Debug/LiteAPPStar.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\bin;${env:PATH}"}], "console": "integratedTerminal", "preLaunchTask": "CMake Build (VS2022)", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dde9450d54f48bd50b63f7b9d200f9f5\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Debug LiteAPPStar (CMake - MinGW)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/LiteAPPStar.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake Build", "windows": {"program": "${workspaceFolder}/build/bin/LiteAPPStar.exe", "MIMode": "gdb", "miDebuggerPath": "C:/msys64/mingw64/bin/gdb.exe"}, "linux": {"program": "${workspaceFolder}/build/bin/LiteAPPStar", "MIMode": "gdb"}, "osx": {"program": "${workspaceFolder}/build/bin/LiteAPPStar", "MIMode": "lldb"}, "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dde9450d54f48bd50b63f7b9d200f9f5\\tonka3000.qtvsctools\\qt.natvis.xml"}, {"name": "Debug LiteAPPStar (qmake)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/LiteAPPStar.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "qmake Build", "windows": {"program": "${workspaceFolder}/bin/LiteAPPStar.exe", "MIMode": "gdb", "miDebuggerPath": "C:/msys64/mingw64/bin/gdb.exe"}, "linux": {"program": "${workspaceFolder}/bin/LiteAPPStar", "MIMode": "gdb"}, "osx": {"program": "${workspaceFolder}/bin/LiteAPPStar", "MIMode": "lldb"}, "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dde9450d54f48bd50b63f7b9d200f9f5\\tonka3000.qtvsctools\\qt.natvis.xml"}]}