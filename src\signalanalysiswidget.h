#ifndef SIGNALANALYSISWIDGET_H
#define SIGNALANALYSISWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QSplitter>
#include <QGroupBox>
#include <QFrame>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QSlider>
#include <QProgressBar>
#include <QCheckBox>
#include <QShortcut>
#include <QKeySequence>
#include <QDebug>
#include <QRandomGenerator>
#include <cmath>
#include "spectrumplot_simple.h"
#include "waterfallplot_simple.h"

QT_BEGIN_NAMESPACE
namespace Ui
{
    class SignalAnalysisWidget;
}
QT_END_NAMESPACE

/**
 * @brief 现代化信号分析组件类
 *
 * 集成了频谱图、瀑布图和数据表格的完整信号分析界面
 * 采用全新的现代化设计和交互体验
 *
 * 主要特性：
 * - 基于QCustomPlot的高性能频谱图和瀑布图
 * - 频率轴精确对齐和双向同步
 * - 实时数据更新和处理
 * - 丰富的频率控制功能
 * - 现代化的深色主题界面
 */
class SignalAnalysisWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit SignalAnalysisWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SignalAnalysisWidget();

    /**
     * @brief 开始分析
     * 启动数据分析和实时更新
     */
    void startAnalysis();

    /**
     * @brief 停止分析
     * 停止数据分析和实时更新
     */
    void stopAnalysis();

    /**
     * @brief 获取分析状态
     * @return 是否正在分析
     */
    bool isAnalysisRunning() const;

    /**
     * @brief 设置中心频率
     * @param frequency 中心频率 (Hz)
     */
    void setCenterFrequency(double frequency);

    /**
     * @brief 设置带宽
     * @param bandwidth 带宽 (Hz)
     */
    void setBandwidth(double bandwidth);

    /**
     * @brief 获取当前中心频率
     * @return 中心频率 (Hz)
     */
    double centerFrequency() const;

    /**
     * @brief 获取当前带宽
     * @return 带宽 (Hz)
     */
    double bandwidth() const;

    /**
     * @brief 清除所有数据
     * 清空频谱图、瀑布图和数据表格
     */
    void clearAllData();

    /**
     * @brief 设置动态数据生成模式
     * @param enabled 是否启用动态数据生成
     */
    void setDynamicDataEnabled(bool enabled);

    /**
     * @brief 获取动态数据生成状态
     * @return 是否启用动态数据生成
     */
    bool isDynamicDataEnabled() const;

    /**
     * @brief 设置数据更新间隔
     * @param intervalMs 更新间隔（毫秒）
     */
    void setUpdateInterval(int intervalMs);

    /**
     * @brief 获取当前更新间隔
     * @return 更新间隔（毫秒）
     */
    int updateInterval() const;

signals:
    /**
     * @brief 分析状态改变信号
     * @param running 是否正在运行分析
     */
    void analysisStateChanged(bool running);

    /**
     * @brief 频率范围改变信号
     * @param centerFreq 中心频率 (Hz)
     * @param bandwidth 带宽 (Hz)
     */
    void frequencyRangeChanged(double centerFreq, double bandwidth);

    /**
     * @brief 数据更新信号
     * 当数据更新时发出此信号
     */
    void dataUpdated();

private slots:
    /**
     * @brief 更新数据槽函数
     * 生成新的频谱数据并更新显示
     */
    void updateData();

    /**
     * @brief 中心频率文本改变槽函数
     */
    void onCenterFrequencyTextChanged();

    /**
     * @brief 中心频率单位改变槽函数
     */
    void onCenterFrequencyUnitChanged();

    /**
     * @brief Span文本改变槽函数
     */
    void onSpanTextChanged();

    /**
     * @brief Span单位改变槽函数
     */
    void onSpanUnitChanged();

    /**
     * @brief 频谱图频率范围改变槽函数
     * @param startFreq 起始频率
     * @param endFreq 结束频率
     */
    void onSpectrumFrequencyRangeChanged(double startFreq, double endFreq);

    /**
     * @brief 瀑布图频率范围改变槽函数
     * @param startFreq 起始频率
     * @param endFreq 结束频率
     */
    void onWaterfallFrequencyRangeChanged(double startFreq, double endFreq);

    /**
     * @brief 预设频段槽函数
     */
    void onPresetISM();
    void onPresetWiFi();
    void onPresetFM();

    /**
     * @brief 动态数据按钮切换槽函数
     * @param enabled 是否启用动态数据
     */
    void onDynamicDataToggled(bool enabled);

    /**
     * @brief 表格列宽调整槽函数
     * @param logicalIndex 列索引
     * @param oldSize 旧尺寸
     * @param newSize 新尺寸
     */
    void onTableColumnResized(int logicalIndex, int oldSize, int newSize);

    /**
     * @brief 智能调整表格列宽
     * 根据表格总宽度和字段数量优化列宽分配
     */
    void adjustTableColumnWidths();

    /**
     * @brief 重置布局比例
     * 当布局出现问题时恢复默认的分割比例
     */
    void resetLayoutProportions();

protected:
    /**
     * @brief 事件过滤器
     * 监听表格大小变化事件
     */
    bool eventFilter(QObject *watched, QEvent *event) override;

    /**
     * @brief 窗口大小变化事件
     * 处理窗口大小变化时的布局调整
     */
    void resizeEvent(QResizeEvent *event) override;

private:
    /**
     * @brief 初始化UI组件
     * 设置频谱图、瀑布图和表格的初始状态
     */
    void initializeUI();

    /**
     * @brief 设置表格属性
     * 配置数据表格的列宽、样式等
     */
    void setupTableProperties();

    /**
     * @brief 连接信号槽
     * 连接各组件之间的信号槽
     */
    void connectSignals();

    /**
     * @brief 生成频谱数据
     * 生成模拟的频谱分析数据
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     */
    void generateSpectrumData(QVector<double> &frequencies, QVector<double> &amplitudes);

    /**
     * @brief 生成动态射频环境数据
     * 模拟真实的射频信号环境，包含多个信号源和时变特性
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     */
    void generateDynamicRFData(QVector<double> &frequencies, QVector<double> &amplitudes);

    /**
     * @brief 生成FFT频谱数据
     * 模拟真实的FFT频谱分析结果
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     */
    void generateFFTSpectrumData(QVector<double> &frequencies, QVector<double> &amplitudes);

    /**
     * @brief 生成测试信号数据（用于验证文字显示效果）
     * 生成多种类型的测试信号，包括正弦波、噪声、扫频等
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     * @param testMode 测试模式：0=综合测试，1=多频点正弦波，2=扫频信号，3=极值测试
     */
    void generateTestSignalData(QVector<double> &frequencies, QVector<double> &amplitudes, int testMode = 0);

    /**
     * @brief 测试信号生成辅助函数
     */
    void generateComprehensiveTestSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter);
    void generateMultiToneSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter);
    void generateSweepSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter);
    void generateExtremeValueSignals(QVector<double> &amplitudes, double startFreq, double freqStep, double timeCounter);

    /**
     * @brief 动态数据生成辅助函数
     */
    void initializeDynamicDataParameters();
    void generateCarrierSignals(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateInterferenceSignals(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateNoiseFloor(QVector<double> &amplitudes);
    void applyTimeVariation(QVector<double> &amplitudes);
    void applyContinuousSmoothing(QVector<double> &amplitudes);
    void updateCarrierFrequencies();
    void generateContinuousSignals(QVector<double> &amplitudes, double startFreq, double freqStep);

    /**
     * @brief FFT数据生成辅助函数
     */
    void generateFFTFundamentalTones(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateFFTHarmonics(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateFFTSpectralLeakage(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateFFTWindowingEffects(QVector<double> &amplitudes);
    void applyFFTCharacteristics(QVector<double> &amplitudes);

    /**
     * @brief 动态FFT数据生成函数
     */
    void generateDynamicNoiseFloor(QVector<double> &amplitudes);
    void generateDynamicFundamentalTones(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateBurstSignals(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateIntermittentInterference(QVector<double> &amplitudes, double startFreq, double freqStep);
    void generateDynamicHarmonics(QVector<double> &amplitudes, double startFreq, double freqStep);
    void applyMultipathFading(QVector<double> &amplitudes);
    void applyReducedSmoothing(QVector<double> &amplitudes);

    /**
     * @brief 更新频率范围显示
     * 更新UI中的频率范围标签
     */
    void updateFrequencyRangeDisplay();

    /**
     * @brief 更新数据表格
     * 根据当前频谱数据更新分析参数表格
     */
    void updateDataTable();

    /**
     * @brief 生成假信号数据
     */
    void generateFakeSignalData();

    /**
     * @brief 更新信号列表表格
     */
    void updateSignalListTable();

    /**
     * @brief 同步频率轴
     * 确保频谱图和瀑布图的频率轴对齐
     */
    void synchronizeFrequencyAxes();

    /**
     * @brief 计算分析参数
     * 根据频谱数据计算各种分析参数
     * @param amplitudes 幅度数据
     * @return 分析参数映射
     */
    QMap<QString, double> calculateAnalysisParameters(const QVector<double> &amplitudes);

    /**
     * @brief 解析频率文本
     * @param text 频率文本
     * @param unit 单位
     * @return 频率值(Hz)
     */
    double parseFrequencyText(const QString &text, const QString &unit);

    /**
     * @brief 格式化频率显示
     * @param frequency 频率值(Hz)
     * @param unit 单位
     * @return 格式化的文本
     */
    QString formatFrequencyText(double frequency, const QString &unit);

    /**
     * @brief 更新频率输入框显示
     */
    void updateFrequencyInputs();

private:
    Ui::SignalAnalysisWidget *ui; ///< UI界面指针

    // 图表组件
    SpectrumPlotSimple *m_spectrumPlot;   ///< 频谱图组件
    WaterfallPlotSimple *m_waterfallPlot; ///< 瀑布图组件

    // 数据更新定时器
    QTimer *m_updateTimer; ///< 数据更新定时器

    // 频率参数
    double m_centerFrequency; ///< 中心频率 (Hz)
    double m_bandwidth;       ///< 带宽 (Hz)

    // 状态变量
    bool m_analysisRunning;    ///< 分析运行状态
    bool m_synchronizing;      ///< 同步状态标志
    bool m_testMode;           ///< 测试模式开关
    int m_currentTestType;     ///< 当前测试信号类型
    bool m_dynamicDataEnabled; ///< 动态数据生成开关

    // 动态数据生成参数
    double m_timeCounter;                 ///< 时间计数器
    QVector<double> m_signalPhases;       ///< 信号相位数组
    QVector<double> m_signalAmplitudes;   ///< 信号幅度数组
    QVector<double> m_noiseBuffer;        ///< 噪声缓冲区
    QVector<double> m_previousAmplitudes; ///< 上一帧幅度数据（用于平滑）
    QVector<double> m_carrierFrequencies; ///< 载波频率数组
    QVector<double> m_carrierDriftRates;  ///< 载波频率漂移速率
    double m_smoothingFactor;             ///< 数据平滑因子

    // FFT数据生成参数
    QVector<double> m_fundamentalFreqs; ///< 基频数组
    QVector<double> m_fundamentalAmps;  ///< 基频幅度数组
    QVector<double> m_harmonicPhases;   ///< 谐波相位数组
    double m_spectralResolution;        ///< 频谱分辨率
    int m_fftSize;                      ///< FFT大小

    // 动态特性参数
    QVector<double> m_burstSignalTimes; ///< 突发信号时间
    QVector<double> m_burstSignalFreqs; ///< 突发信号频率
    QVector<double> m_burstSignalAmps;  ///< 突发信号幅度
    QVector<bool> m_signalActive;       ///< 信号激活状态
    double m_lastBurstTime;             ///< 上次突发信号时间
    double m_fadingPhase;               ///< 衰落相位

    // 信号数据结构
    struct SignalInfo
    {
        int id;                ///< 序号
        double centerFreq;     ///< 中心频率(Hz)
        double bandwidth;      ///< 带宽(Hz)
        QString modulation;    ///< 调制方式
        double dataRate;       ///< 速率(bps)
        double snr;            ///< 信噪比(dB)
        double freqOffset;     ///< 频偏(Hz)
        QString protocol;      ///< 通讯体制
        bool active;           ///< 是否激活
        double lastUpdateTime; ///< 上次更新时间
    };

    // 数据缓存
    QVector<double> m_currentFrequencies;  ///< 当前频率数据
    QVector<double> m_currentAmplitudes;   ///< 当前幅度数据
    QVector<SignalInfo> m_detectedSignals; ///< 检测到的信号列表

    // 常量定义
    static const int UPDATE_INTERVAL_MS = 100; ///< 更新间隔(毫秒)
    static const int SPECTRUM_POINTS = 1024;   ///< 频谱点数
    static const double DEFAULT_CENTER_FREQ;   ///< 默认中心频率
    static const double DEFAULT_BANDWIDTH;     ///< 默认带宽
    static const double MIN_AMPLITUDE;         ///< 最小幅度
    static const double MAX_AMPLITUDE;         ///< 最大幅度
};

#endif // SIGNALANALYSISWIDGET_H
