#include "configmanager.h"
#include <QJsonArray>
#include <QFileSystemWatcher>
#include <QMutex>
#include <QMutexLocker>

static QMutex g_configMutex;

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent), m_currentEnvironment("development"), m_configLoaded(false), m_configVersion("1.0.0")
{
    initializeDefaults();

    // 尝试自动加载配置文件
    QString defaultPath = getDefaultConfigPath();
    if (QFile::exists(defaultPath))
    {
        loadConfig(defaultPath);
    }
    else
    {
        qDebug() << "ConfigManager: 配置文件不存在，使用默认配置:" << defaultPath;
        m_fallbackConfig = createDefaultConfig();
        m_currentConfig = m_fallbackConfig;
    }
}

ConfigManager::~ConfigManager() = default;

void ConfigManager::initializeDefaults()
{
    m_supportedDatabaseTypes << "QMYSQL" << "QPSQL" << "QSQLITE" << "QODBC";
    m_supportedSslModes << "disabled" << "preferred" << "required";
    m_supportedLogLevels << "debug" << "info" << "warning" << "error";
    m_requiredFields << "database.type" << "database.host" << "database.port"
                     << "database.database_name" << "database.username" << "database.table_name";
}

bool ConfigManager::loadConfig(const QString &configPath)
{
    QMutexLocker locker(&g_configMutex);

    QString filePath = configPath.isEmpty() ? getDefaultConfigPath() : configPath;

    qDebug() << "ConfigManager: 加载配置文件:" << filePath;

    if (!QFile::exists(filePath))
    {
        QString error = QString("配置文件不存在: %1").arg(filePath);
        qWarning() << "ConfigManager:" << error;
        emit configError(error);

        // 使用默认配置
        m_fallbackConfig = createDefaultConfig();
        m_currentConfig = m_fallbackConfig;
        m_configLoaded = false;
        return false;
    }

    if (!parseConfigFile(filePath))
    {
        QString error = QString("配置文件解析失败: %1").arg(filePath);
        qWarning() << "ConfigManager:" << error;
        emit configError(error);

        // 使用默认配置
        m_fallbackConfig = createDefaultConfig();
        m_currentConfig = m_fallbackConfig;
        m_configLoaded = false;
        return false;
    }

    m_configPath = filePath;
    m_configLoaded = true;

    // 设置当前环境（内部调用，不使用锁）
    QString currentEnv = m_configData.value("current_environment").toString("development");
    if (!setCurrentEnvironmentInternal(currentEnv))
    {
        qWarning() << "ConfigManager: 设置当前环境失败，使用development环境";
        setCurrentEnvironmentInternal("development");
    }

    logConfigInfo();
    emit configLoaded(m_currentEnvironment);

    return true;
}

bool ConfigManager::parseConfigFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qWarning() << "ConfigManager: 无法打开配置文件:" << filePath;
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError)
    {
        qWarning() << "ConfigManager: JSON解析错误:" << parseError.errorString();
        return false;
    }

    if (!doc.isObject())
    {
        qWarning() << "ConfigManager: 配置文件根节点不是JSON对象";
        return false;
    }

    m_configData = doc.object();

    // 解析版本信息
    m_configVersion = m_configData.value("version").toString("1.0.0");

    // 解析fallback配置
    QJsonObject fallbackObj = m_configData.value("fallback").toObject();
    if (!fallbackObj.isEmpty())
    {
        m_fallbackConfig = parseEnvironmentConfig(fallbackObj);
        m_fallbackConfig.name = "fallback";
    }
    else
    {
        m_fallbackConfig = createDefaultConfig();
    }

    return true;
}

bool ConfigManager::setCurrentEnvironment(const QString &environment)
{
    QMutexLocker locker(&g_configMutex);

    if (!m_configLoaded)
    {
        qWarning() << "ConfigManager: 配置未加载，无法切换环境";
        return false;
    }

    QJsonObject environments = m_configData.value("environments").toObject();
    if (!environments.contains(environment))
    {
        qWarning() << "ConfigManager: 环境不存在:" << environment;
        return false;
    }

    QJsonObject envObject = environments.value(environment).toObject();
    EnvironmentConfig newConfig = parseEnvironmentConfig(envObject);
    newConfig.name = environment;

    QString errorMessage;
    if (!validateEnvironmentConfig(newConfig, &errorMessage))
    {
        qWarning() << "ConfigManager: 环境配置验证失败:" << errorMessage;
        return false;
    }

    QString oldEnv = m_currentEnvironment;
    m_currentEnvironment = environment;
    m_currentConfig = newConfig;

    qDebug() << "ConfigManager: 切换环境:" << oldEnv << "->" << environment;
    emit environmentChanged(oldEnv, environment);
    emit configChanged(environment);

    return true;
}

bool ConfigManager::setCurrentEnvironmentInternal(const QString &environment)
{
    // 注意：此方法不使用锁，仅供内部调用

    if (!m_configLoaded)
    {
        qWarning() << "ConfigManager: 配置未加载，无法切换环境";
        return false;
    }

    QJsonObject environments = m_configData.value("environments").toObject();
    if (!environments.contains(environment))
    {
        qWarning() << "ConfigManager: 环境不存在:" << environment;
        return false;
    }

    QJsonObject envObject = environments.value(environment).toObject();
    EnvironmentConfig newConfig = parseEnvironmentConfig(envObject);
    newConfig.name = environment;

    QString errorMessage;
    if (!validateEnvironmentConfig(newConfig, &errorMessage))
    {
        qWarning() << "ConfigManager: 环境配置验证失败:" << errorMessage;
        return false;
    }

    QString oldEnv = m_currentEnvironment;
    m_currentEnvironment = environment;
    m_currentConfig = newConfig;

    qDebug() << "ConfigManager: 切换环境:" << oldEnv << "->" << environment;
    emit environmentChanged(oldEnv, environment);
    emit configChanged(environment);

    return true;
}

EnvironmentConfig ConfigManager::parseEnvironmentConfig(const QJsonObject &envObject) const
{
    EnvironmentConfig config;

    // 解析数据库配置
    QJsonObject dbObject = envObject.value("database").toObject();
    config.database = parseDatabaseConfig(dbObject);

    // 解析连接池配置
    QJsonObject poolObject = envObject.value("connection_pool").toObject();
    config.connectionPool = parseConnectionPoolConfig(poolObject);

    // 解析数据库选项
    QJsonObject optionsObject = envObject.value("options").toObject();
    config.options = parseDatabaseOptions(optionsObject);

    return config;
}

DatabaseConfig ConfigManager::parseDatabaseConfig(const QJsonObject &dbObject) const
{
    DatabaseConfig config;

    config.type = dbObject.value("type").toString("QMYSQL");
    config.host = dbObject.value("host").toString("127.0.0.1");
    config.port = dbObject.value("port").toInt(3306);
    config.databaseName = dbObject.value("database_name").toString("test");
    config.username = dbObject.value("username").toString("root");
    config.password = dbObject.value("password").toString("");
    config.tableName = dbObject.value("table_name").toString("target_position");
    config.charset = dbObject.value("charset").toString("utf8mb4");
    config.timezone = dbObject.value("timezone").toString("+08:00");

    return config;
}

ConnectionPoolConfig ConfigManager::parseConnectionPoolConfig(const QJsonObject &poolObject) const
{
    ConnectionPoolConfig config;

    config.maxConnections = poolObject.value("max_connections").toInt(10);
    config.minConnections = poolObject.value("min_connections").toInt(2);
    config.connectionTimeout = poolObject.value("connection_timeout").toInt(30);
    config.idleTimeout = poolObject.value("idle_timeout").toInt(300);
    config.retryAttempts = poolObject.value("retry_attempts").toInt(3);
    config.retryDelay = poolObject.value("retry_delay").toInt(1000);

    return config;
}

DatabaseOptions ConfigManager::parseDatabaseOptions(const QJsonObject &optionsObject) const
{
    DatabaseOptions options;

    options.autoReconnect = optionsObject.value("auto_reconnect").toBool(true);
    options.sslMode = optionsObject.value("ssl_mode").toString("disabled");
    options.connectTimeout = optionsObject.value("connect_timeout").toInt(10);
    options.readTimeout = optionsObject.value("read_timeout").toInt(30);
    options.writeTimeout = optionsObject.value("write_timeout").toInt(30);
    options.enableLogging = optionsObject.value("enable_logging").toBool(true);
    options.logLevel = optionsObject.value("log_level").toString("info");

    return options;
}

QString ConfigManager::getCurrentEnvironment() const
{
    return m_currentEnvironment;
}

QStringList ConfigManager::getAvailableEnvironments() const
{
    if (!m_configLoaded)
    {
        return QStringList() << "development";
    }

    QJsonObject environments = m_configData.value("environments").toObject();
    return environments.keys();
}

EnvironmentConfig ConfigManager::getCurrentConfig() const
{
    return m_currentConfig;
}

EnvironmentConfig ConfigManager::getEnvironmentConfig(const QString &environment) const
{
    if (!m_configLoaded)
    {
        return m_fallbackConfig;
    }

    QJsonObject environments = m_configData.value("environments").toObject();
    if (!environments.contains(environment))
    {
        return m_fallbackConfig;
    }

    QJsonObject envObject = environments.value(environment).toObject();
    EnvironmentConfig config = parseEnvironmentConfig(envObject);
    config.name = environment;

    return config;
}

EnvironmentConfig ConfigManager::getFallbackConfig() const
{
    return m_fallbackConfig;
}

QString ConfigManager::getDefaultConfigPath()
{
    // 优先查找应用程序目录下的config文件夹
    QString appDir = QCoreApplication::applicationDirPath();
    QString configPath = QDir(appDir).absoluteFilePath("config/database.json");

    if (QFile::exists(configPath))
    {
        return configPath;
    }

    // 查找项目根目录的config文件夹
    QDir currentDir(appDir);
    while (currentDir.cdUp())
    {
        QString testPath = currentDir.absoluteFilePath("config/database.json");
        if (QFile::exists(testPath))
        {
            return testPath;
        }

        // 防止无限循环
        if (currentDir.isRoot())
        {
            break;
        }
    }

    // 返回默认路径（即使文件不存在）
    return QDir(appDir).absoluteFilePath("config/database.json");
}

EnvironmentConfig ConfigManager::createDefaultConfig()
{
    EnvironmentConfig config;
    config.name = "default";

    // 数据库配置
    config.database.type = "QMYSQL";
    config.database.host = "127.0.0.1";
    config.database.port = 3306;
    config.database.databaseName = "test";
    config.database.username = "root";
    config.database.password = "123456";
    config.database.tableName = "target_position";
    config.database.charset = "utf8mb4";
    config.database.timezone = "+08:00";

    // 连接池配置（使用默认构造函数的值）
    // config.connectionPool 已经有默认值

    // 数据库选项（使用默认构造函数的值）
    // config.options 已经有默认值

    return config;
}

void ConfigManager::logConfigInfo() const
{
    qDebug() << "ConfigManager: 配置信息:";
    qDebug() << "  配置文件:" << m_configPath;
    qDebug() << "  配置版本:" << m_configVersion;
    qDebug() << "  当前环境:" << m_currentEnvironment;
    qDebug() << "  可用环境:" << getAvailableEnvironments();
    qDebug() << "  数据库配置:" << m_currentConfig.database.toString();
}

bool ConfigManager::isConfigLoaded() const
{
    return m_configLoaded;
}

QString ConfigManager::getConfigVersion() const
{
    return m_configVersion;
}

QString ConfigManager::getConfigPath() const
{
    return m_configPath;
}

bool ConfigManager::validateEnvironmentConfig(const EnvironmentConfig &config, QString *errorMessage) const
{
    if (config.name.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "环境名称不能为空";
        return false;
    }

    if (!validateDatabaseConfig(config.database, errorMessage))
    {
        return false;
    }

    if (!validateConnectionPoolConfig(config.connectionPool, errorMessage))
    {
        return false;
    }

    if (!validateDatabaseOptions(config.options, errorMessage))
    {
        return false;
    }

    return true;
}

bool ConfigManager::validateDatabaseConfig(const DatabaseConfig &config, QString *errorMessage) const
{
    if (!m_supportedDatabaseTypes.contains(config.type))
    {
        if (errorMessage)
            *errorMessage = QString("不支持的数据库类型: %1").arg(config.type);
        return false;
    }

    if (config.host.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "数据库主机地址不能为空";
        return false;
    }

    if (config.port <= 0 || config.port > 65535)
    {
        if (errorMessage)
            *errorMessage = QString("数据库端口无效: %1").arg(config.port);
        return false;
    }

    if (config.databaseName.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "数据库名不能为空";
        return false;
    }

    if (config.username.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "数据库用户名不能为空";
        return false;
    }

    if (config.tableName.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "数据库表名不能为空";
        return false;
    }

    return true;
}

bool ConfigManager::validateConnectionPoolConfig(const ConnectionPoolConfig &config, QString *errorMessage) const
{
    if (config.maxConnections <= 0)
    {
        if (errorMessage)
            *errorMessage = "最大连接数必须大于0";
        return false;
    }

    if (config.minConnections < 0)
    {
        if (errorMessage)
            *errorMessage = "最小连接数不能小于0";
        return false;
    }

    if (config.minConnections > config.maxConnections)
    {
        if (errorMessage)
            *errorMessage = "最小连接数不能大于最大连接数";
        return false;
    }

    if (config.connectionTimeout <= 0)
    {
        if (errorMessage)
            *errorMessage = "连接超时时间必须大于0";
        return false;
    }

    if (config.retryAttempts < 0)
    {
        if (errorMessage)
            *errorMessage = "重试次数不能小于0";
        return false;
    }

    if (config.retryDelay < 0)
    {
        if (errorMessage)
            *errorMessage = "重试延迟不能小于0";
        return false;
    }

    return true;
}

bool ConfigManager::validateDatabaseOptions(const DatabaseOptions &options, QString *errorMessage) const
{
    if (!m_supportedSslModes.contains(options.sslMode))
    {
        if (errorMessage)
            *errorMessage = QString("不支持的SSL模式: %1").arg(options.sslMode);
        return false;
    }

    if (!m_supportedLogLevels.contains(options.logLevel))
    {
        if (errorMessage)
            *errorMessage = QString("不支持的日志级别: %1").arg(options.logLevel);
        return false;
    }

    if (options.connectTimeout <= 0)
    {
        if (errorMessage)
            *errorMessage = "连接超时时间必须大于0";
        return false;
    }

    if (options.readTimeout <= 0)
    {
        if (errorMessage)
            *errorMessage = "读取超时时间必须大于0";
        return false;
    }

    if (options.writeTimeout <= 0)
    {
        if (errorMessage)
            *errorMessage = "写入超时时间必须大于0";
        return false;
    }

    return true;
}

bool ConfigManager::validateConfig(const EnvironmentConfig &config, QString *errorMessage) const
{
    return validateEnvironmentConfig(config, errorMessage);
}

bool ConfigManager::validateConfigFile(const QString &configPath, QString *errorMessage) const
{
    if (!QFile::exists(configPath))
    {
        if (errorMessage)
            *errorMessage = QString("配置文件不存在: %1").arg(configPath);
        return false;
    }

    QFile file(configPath);
    if (!file.open(QIODevice::ReadOnly))
    {
        if (errorMessage)
            *errorMessage = QString("无法打开配置文件: %1").arg(configPath);
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError)
    {
        if (errorMessage)
            *errorMessage = QString("JSON解析错误: %1").arg(parseError.errorString());
        return false;
    }

    if (!doc.isObject())
    {
        if (errorMessage)
            *errorMessage = "配置文件根节点不是JSON对象";
        return false;
    }

    // 验证必需的字段
    QJsonObject rootObj = doc.object();
    QJsonObject environments = rootObj.value("environments").toObject();

    if (environments.isEmpty())
    {
        if (errorMessage)
            *errorMessage = "配置文件中没有环境配置";
        return false;
    }

    // 验证每个环境的配置
    for (auto it = environments.begin(); it != environments.end(); ++it)
    {
        QString envName = it.key();
        QJsonObject envObj = it.value().toObject();

        EnvironmentConfig config = parseEnvironmentConfig(envObj);
        config.name = envName;

        QString envError;
        if (!validateEnvironmentConfig(config, &envError))
        {
            if (errorMessage)
                *errorMessage = QString("环境 '%1' 配置错误: %2").arg(envName).arg(envError);
            return false;
        }
    }

    return true;
}

bool ConfigManager::reloadConfig()
{
    if (m_configPath.isEmpty())
    {
        qWarning() << "ConfigManager: 没有配置文件路径，无法重新加载";
        return false;
    }

    return loadConfig(m_configPath);
}

bool ConfigManager::saveConfig(const QString &configPath)
{
    // 这个方法可以在未来实现配置文件的保存功能
    Q_UNUSED(configPath)
    qWarning() << "ConfigManager: saveConfig方法尚未实现";
    return false;
}

void ConfigManager::onConfigFileChanged()
{
    qDebug() << "ConfigManager: 检测到配置文件变化，重新加载配置";
    reloadConfig();
}
