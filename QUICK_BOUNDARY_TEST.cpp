/**
 * LiteAPPStar 视口边界快速验证测试
 * 
 * 用于快速验证修正后的边界限制是否有效
 */

#include "qgismapwidget.h"
#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTimer>
#include <QDebug>

class QuickBoundaryTest : public QMainWindow
{
    Q_OBJECT

public:
    QuickBoundaryTest(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        
        // 启动自动测试定时器
        m_testTimer = new QTimer(this);
        connect(m_testTimer, &QTimer::timeout, this, &QuickBoundaryTest::runAutomaticTest);
        m_testTimer->start(2000); // 每2秒运行一次测试
    }

private slots:
    void testNorthBoundary()
    {
        qDebug() << "\n=== 测试北边界 ===";
        m_statusLabel->setText("测试北边界...");
        
        // 尝试设置到极北位置
        m_mapWidget->setCenter(89.5, 0.0);
        
        // 检查实际位置
        auto actualCenter = m_mapWidget->getTileMapView()->getCenter();
        qDebug() << "尝试设置到: (89.5, 0.0)";
        qDebug() << "实际设置到:" << actualCenter.first << "," << actualCenter.second;
        
        // 调试边界状态
        m_mapWidget->getTileMapView()->debugBoundaryStatus();
        
        // 检查是否会显示空白
        bool wouldShow = m_mapWidget->getTileMapView()->wouldShowEmptyArea(actualCenter.first, actualCenter.second);
        m_statusLabel->setText(QString("北边界测试 - 会显示空白: %1").arg(wouldShow ? "是" : "否"));
        
        if (wouldShow)
        {
            m_statusLabel->setStyleSheet("color: red;");
            qWarning() << "警告：北边界测试失败，仍可能显示空白！";
        }
        else
        {
            m_statusLabel->setStyleSheet("color: green;");
            qDebug() << "成功：北边界测试通过，不会显示空白";
        }
    }
    
    void testSouthBoundary()
    {
        qDebug() << "\n=== 测试南边界 ===";
        m_statusLabel->setText("测试南边界...");
        
        // 尝试设置到极南位置
        m_mapWidget->setCenter(-89.5, 0.0);
        
        // 检查实际位置
        auto actualCenter = m_mapWidget->getTileMapView()->getCenter();
        qDebug() << "尝试设置到: (-89.5, 0.0)";
        qDebug() << "实际设置到:" << actualCenter.first << "," << actualCenter.second;
        
        // 调试边界状态
        m_mapWidget->getTileMapView()->debugBoundaryStatus();
        
        // 检查是否会显示空白
        bool wouldShow = m_mapWidget->getTileMapView()->wouldShowEmptyArea(actualCenter.first, actualCenter.second);
        m_statusLabel->setText(QString("南边界测试 - 会显示空白: %1").arg(wouldShow ? "是" : "否"));
        
        if (wouldShow)
        {
            m_statusLabel->setStyleSheet("color: red;");
            qWarning() << "警告：南边界测试失败，仍可能显示空白！";
        }
        else
        {
            m_statusLabel->setStyleSheet("color: green;");
            qDebug() << "成功：南边界测试通过，不会显示空白";
        }
    }
    
    void testZoomBoundary()
    {
        qDebug() << "\n=== 测试缩放边界 ===";
        m_statusLabel->setText("测试缩放边界...");
        
        // 测试不同缩放级别下的边界
        QList<int> testZooms = {4, 8, 12, 16, 18};
        
        for (int zoom : testZooms)
        {
            qDebug() << "--- 测试缩放级别" << zoom << "---";
            m_mapWidget->getTileMapView()->setZoomLevel(zoom);
            
            // 尝试设置到高纬度
            m_mapWidget->setCenter(85.0, 0.0);
            auto center = m_mapWidget->getTileMapView()->getCenter();
            bool wouldShow = m_mapWidget->getTileMapView()->wouldShowEmptyArea(center.first, center.second);
            
            qDebug() << "缩放级别" << zoom << "- 中心:" << center.first << "会显示空白:" << (wouldShow ? "是" : "否");
            
            if (wouldShow)
            {
                qWarning() << "缩放级别" << zoom << "存在空白风险！";
            }
        }
        
        m_statusLabel->setText("缩放边界测试完成");
        m_statusLabel->setStyleSheet("color: blue;");
    }
    
    void runBoundaryMonitor()
    {
        qDebug() << "\n=== 运行边界监控 ===";
        m_statusLabel->setText("运行边界监控...");
        
        // 运行实时边界监控
        m_mapWidget->monitorBoundaryStatus();
        
        m_statusLabel->setText("边界监控完成");
        m_statusLabel->setStyleSheet("color: purple;");
    }
    
    void runAutomaticTest()
    {
        static int testStep = 0;
        
        switch (testStep % 4)
        {
            case 0:
                testNorthBoundary();
                break;
            case 1:
                testSouthBoundary();
                break;
            case 2:
                testZoomBoundary();
                break;
            case 3:
                runBoundaryMonitor();
                break;
        }
        
        testStep++;
        
        // 10次测试后停止自动测试
        if (testStep >= 10)
        {
            m_testTimer->stop();
            m_statusLabel->setText("自动测试完成");
            m_statusLabel->setStyleSheet("color: black;");
        }
    }

private:
    void setupUI()
    {
        auto *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        auto *layout = new QVBoxLayout(centralWidget);
        
        // 创建地图组件
        m_mapWidget = new QgisMapWidget(this);
        layout->addWidget(m_mapWidget);
        
        // 状态标签
        m_statusLabel = new QLabel("准备开始边界测试...", this);
        m_statusLabel->setStyleSheet("font-size: 14px; padding: 10px; background-color: #f0f0f0;");
        layout->addWidget(m_statusLabel);
        
        // 控制按钮
        auto *buttonLayout = new QHBoxLayout();
        
        auto *northBtn = new QPushButton("测试北边界", this);
        auto *southBtn = new QPushButton("测试南边界", this);
        auto *zoomBtn = new QPushButton("测试缩放边界", this);
        auto *monitorBtn = new QPushButton("边界监控", this);
        auto *stopBtn = new QPushButton("停止自动测试", this);
        
        buttonLayout->addWidget(northBtn);
        buttonLayout->addWidget(southBtn);
        buttonLayout->addWidget(zoomBtn);
        buttonLayout->addWidget(monitorBtn);
        buttonLayout->addWidget(stopBtn);
        
        layout->addLayout(buttonLayout);
        
        // 连接信号
        connect(northBtn, &QPushButton::clicked, this, &QuickBoundaryTest::testNorthBoundary);
        connect(southBtn, &QPushButton::clicked, this, &QuickBoundaryTest::testSouthBoundary);
        connect(zoomBtn, &QPushButton::clicked, this, &QuickBoundaryTest::testZoomBoundary);
        connect(monitorBtn, &QPushButton::clicked, this, &QuickBoundaryTest::runBoundaryMonitor);
        connect(stopBtn, &QPushButton::clicked, [this]() {
            m_testTimer->stop();
            m_statusLabel->setText("自动测试已停止");
        });
        
        setWindowTitle("LiteAPPStar 边界快速验证测试");
        resize(1000, 700);
        
        qDebug() << "QuickBoundaryTest: 界面初始化完成，开始自动测试...";
    }

private:
    QgisMapWidget *m_mapWidget;
    QLabel *m_statusLabel;
    QTimer *m_testTimer;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    QuickBoundaryTest test;
    test.show();
    
    qDebug() << "启动边界快速验证测试程序";
    qDebug() << "程序将自动运行各种边界测试";
    qDebug() << "请观察控制台输出和地图显示效果";
    qDebug() << "如果看到红色警告，说明仍存在空白区域问题";
    qDebug() << "如果看到绿色成功，说明边界限制正常工作";
    
    return app.exec();
}

#include "QUICK_BOUNDARY_TEST.moc"

/**
 * 使用说明：
 * 
 * 1. 编译并运行此测试程序
 * 2. 程序会自动运行各种边界测试
 * 3. 观察控制台输出的调试信息
 * 4. 检查地图是否还会出现空白区域
 * 5. 状态标签会显示测试结果
 * 
 * 预期结果：
 * - 所有测试都应该显示"不会显示空白"
 * - 地图上不应该出现任何灰色空白区域
 * - 控制台应该显示详细的边界调试信息
 * 
 * 如果测试失败：
 * - 检查控制台的警告信息
 * - 查看边界状态调试输出
 * - 确认安全边距是否足够
 */
