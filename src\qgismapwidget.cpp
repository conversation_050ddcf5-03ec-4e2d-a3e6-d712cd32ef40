#include "qgismapwidget.h"
#include "tilemapview.h"
#include "databasemanager.h"
#include "geographicdatapanel.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QHeaderView>
#include <QTreeWidgetItem>
#include <QtMath>
#include <QInputDialog>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QDateTime>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QLocale>
#include <QResizeEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QFrame>
#include <QCheckBox>
#include <QSlider>
#include <QCheckBox>
#include <QGroupBox>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QDateTime>
#include <QMessageBox>
#include <QDialog>
#include <QListWidget>
#include <QDoubleValidator>
#include <algorithm>
#include <QScrollArea>
#include <QLineEdit>
#include <QSvgRenderer>
#include <QPainter>

// 常量定义
const QString QgisMapWidget::DEFAULT_CRS = "EPSG:4326"; // WGS84
const int QgisMapWidget::DEFAULT_ZOOM = 10;
const double QgisMapWidget::DEFAULT_LATITUDE = 39.9042; // 北京天安门
const double QgisMapWidget::DEFAULT_LONGITUDE = 116.4074;
const int QgisMapWidget::LAYER_PANEL_WIDTH = 300;
const int QgisMapWidget::PROPERTIES_PANEL_WIDTH = 250;

QgisMapWidget::QgisMapWidget(QWidget *parent)
    : QWidget(parent), m_mainLayout(nullptr), m_mapContainer(nullptr)
#ifdef QGIS_ENABLED
      ,
      m_mapCanvas(nullptr), m_panTool(nullptr), m_zoomInTool(nullptr), m_zoomOutTool(nullptr)
#else
      ,
      m_tileMapView(nullptr), m_mapPlaceholder(nullptr)
#endif
      ,
      m_toolbar(nullptr), m_panBtn(nullptr), m_zoomInBtn(nullptr), m_zoomOutBtn(nullptr), m_zoomFullBtn(nullptr), m_identifyBtn(nullptr), m_refreshBtn(nullptr), m_floatingToolPanel(nullptr), m_layerToggleBtn(nullptr), m_measureBtn(nullptr), m_layerSelectorPanel(nullptr), m_layerPanel(nullptr), m_layerLayout(nullptr), m_layerGroup(nullptr), m_geographicDataPanel(nullptr), m_topRightControlPanel(nullptr), m_dataPanelToggleBtn(nullptr), m_topRightMeasureBtn(nullptr), m_locationMarker(nullptr)
#ifdef QGIS_ENABLED
      ,
      m_layerTreeView(nullptr), m_layerTreeModel(nullptr)
#else
      ,
      m_layerTreeWidget(nullptr)
#endif
      ,
      m_addVectorBtn(nullptr), m_addRasterBtn(nullptr), m_removeLayerBtn(nullptr), m_layerPropsBtn(nullptr), m_propertiesPanel(nullptr), m_propertiesTabs(nullptr), m_mapInfoTab(nullptr), m_crsLabel(nullptr), m_extentLabel(nullptr), m_scaleLabel(nullptr), m_crsCombo(nullptr), m_layerPropsTab(nullptr), m_layerNameEdit(nullptr), m_opacitySlider(nullptr), m_opacityLabel(nullptr), m_visibilityCheck(nullptr), m_coordTab(nullptr), m_xCoordEdit(nullptr), m_yCoordEdit(nullptr), m_gotoCoordBtn(nullptr), m_mouseCoordLabel(nullptr), m_statusBar(nullptr), m_coordStatusLabel(nullptr), m_scaleStatusLabel(nullptr), m_crsStatusLabel(nullptr), m_currentCrs(DEFAULT_CRS), m_selectedLayerId(""), m_measureMode(false), m_totalDistance(0.0), m_bAreaLogMarkers(), m_bAreaLogPositions()
{
    qDebug() << "QgisMapWidget: 初始化开始";

    // 初始化QGIS（如果可用）
    initializeQgis();

    // 设置UI
    setupUI();
    connectSignals();

    // 加载默认图层
    loadDefaultLayers();

    // 延迟加载地理数据（等待界面完全初始化）
    QTimer::singleShot(1000, this, &QgisMapWidget::refreshGeographicData);

    qDebug() << "QgisMapWidget: 初始化完成";
}

QgisMapWidget::~QgisMapWidget()
{
    qDebug() << "QgisMapWidget: 析构";
}

void QgisMapWidget::setupUI()
{
    // 设置整个widget的样式，确保没有红色边框
    this->setStyleSheet("QgisMapWidget { border: none; background-color: #f0f0f0; }");

    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 创建工具栏 - 已禁用
    // createToolbar();

    // 创建地图画布
    createMapCanvas();

    // 将地图容器添加到主布局
    m_mainLayout->addWidget(m_mapContainer);

    // 创建浮动工具面板（已合并到右上角控制面板）
    // createFloatingToolPanel();

    // 创建地图信息面板
    createMapInfoPanel();

    // 创建地理数据面板
    createGeographicDataPanel();

    // 创建右上角控制面板
    createTopRightControlPanel();

    // 创建状态栏 - 已禁用
    // createStatusBar();

    qDebug() << "QgisMapWidget: UI设置完成";
}

void QgisMapWidget::createMapCanvas()
{
    m_mapContainer = new QWidget(this);
    QVBoxLayout *mapLayout = new QVBoxLayout(m_mapContainer);
    mapLayout->setContentsMargins(0, 0, 0, 0);

#ifdef QGIS_ENABLED
    // 创建QGIS地图画布
    m_mapCanvas = new QgsMapCanvas(m_mapContainer);
    m_mapCanvas->setCanvasColor(QColor(255, 255, 255));
    m_mapCanvas->enableAntiAliasing(true);
    mapLayout->addWidget(m_mapCanvas);

    // 设置地图工具
    setupMapTools();

    qDebug() << "QgisMapWidget: QGIS地图画布创建完成";
#else
    // 创建瓦片地图视图
    m_tileMapView = new TileMapView(m_mapContainer);
    m_tileMapView->setMinimumSize(600, 400);
    mapLayout->addWidget(m_tileMapView);

    // 连接瓦片地图信号
    connect(m_tileMapView, &TileMapView::coordinateClicked,
            this, &QgisMapWidget::onMapClicked);
    connect(m_tileMapView, &TileMapView::centerChanged,
            this, [this](double lat, double lng)
            {
                qDebug() << "QgisMapWidget: 地图中心变更到" << lat << "," << lng;
                updateMapInfoPanel();
                // 更新B区域log标记位置
                updateBAreaLogPositions();
                // 更新目标图标位置
                updateTargetIconPositions();
                // 更新测距线位置
                updateMeasureLinePositions();
                // 更新轨迹线位置
                updateTrajectoryLinePositions(); });
    connect(m_tileMapView, &TileMapView::zoomChanged,
            this, [this](int zoom)
            {
                qDebug() << "QgisMapWidget: 缩放级别变更到" << zoom;
                updateMapInfoPanel();
                // 更新B区域log标记位置
                updateBAreaLogPositions();
                // 更新目标图标位置
                updateTargetIconPositions();
                // 更新测距线位置
                updateMeasureLinePositions();
                // 更新轨迹线位置
                updateTrajectoryLinePositions();
                // 强制刷新地图容器和所有子组件
                if (m_mapContainer) {
                    m_mapContainer->update();
                    // 递归更新所有子组件
                    QList<QWidget*> allChildren = m_mapContainer->findChildren<QWidget*>();
                    for (QWidget* child : allChildren) {
                        child->update();
                    }
                } });
    connect(m_tileMapView, &TileMapView::mouseCoordinateChanged,
            this, [this](double lat, double lng)
            {
                updateMouseCoordinateDisplay(lat, lng);
                // 如果处于测距模式且已设置起始点，更新动态线条
                if (m_measureMode && m_hasStartPoint) {
                    updateDynamicLine(lat, lng);
                } });

    // 创建占位符作为备用（隐藏）
    m_mapPlaceholder = new QLabel("", m_mapContainer);
    m_mapPlaceholder->hide();

    qDebug() << "QgisMapWidget: 瓦片地图创建完成";
#endif
}

void QgisMapWidget::createToolbar()
{
    m_toolbar = new QWidget(this);
    m_toolbar->setFixedHeight(40);
    m_toolbar->setStyleSheet("* { border: none !important; background-color: #f5f5f5; margin: 0; padding: 0; outline: none; }");
    m_toolbar->setAttribute(Qt::WA_NoSystemBackground, false);
    m_toolbar->setAutoFillBackground(true);

    QHBoxLayout *toolbarLayout = new QHBoxLayout(m_toolbar);
    toolbarLayout->setContentsMargins(5, 5, 5, 5);

    // 地图工具按钮
    m_panBtn = new QPushButton("平移", m_toolbar);
    m_panBtn->setCheckable(true);
    m_panBtn->setChecked(true);
    m_panBtn->setToolTip("平移地图");

    m_zoomInBtn = new QPushButton("放大", m_toolbar);
    m_zoomInBtn->setCheckable(true);
    m_zoomInBtn->setToolTip("放大地图");

    m_zoomOutBtn = new QPushButton("缩小", m_toolbar);
    m_zoomOutBtn->setCheckable(true);
    m_zoomOutBtn->setToolTip("缩小地图");

    m_zoomFullBtn = new QPushButton("全图", m_toolbar);
    m_zoomFullBtn->setToolTip("缩放到全图范围");

    m_identifyBtn = new QPushButton("识别", m_toolbar);
    m_identifyBtn->setCheckable(true);
    m_identifyBtn->setToolTip("识别要素");

    m_refreshBtn = new QPushButton("刷新", m_toolbar);
    m_refreshBtn->setToolTip("刷新地图");

    QPushButton *m_exportBtn = new QPushButton("导出", m_toolbar);
    m_exportBtn->setToolTip("导出地图");

    // 为所有按钮设置统一样式，确保没有红色边框
    QString buttonStyle = "QPushButton { "
                          "background-color: #f0f0f0; "
                          "border: 1px solid #c0c0c0; "
                          "border-radius: 3px; "
                          "padding: 5px 10px; "
                          "margin: 2px; "
                          "} "
                          "QPushButton:hover { "
                          "background-color: #e0e0e0; "
                          "border-color: #a0a0a0; "
                          "} "
                          "QPushButton:pressed { "
                          "background-color: #d0d0d0; "
                          "} "
                          "QPushButton:checked { "
                          "background-color: #b0d4f1; "
                          "border-color: #4a90e2; "
                          "}";

    m_panBtn->setStyleSheet(buttonStyle);
    m_zoomInBtn->setStyleSheet(buttonStyle);
    m_zoomOutBtn->setStyleSheet(buttonStyle);
    m_zoomFullBtn->setStyleSheet(buttonStyle);
    m_identifyBtn->setStyleSheet(buttonStyle);
    m_refreshBtn->setStyleSheet(buttonStyle);
    m_exportBtn->setStyleSheet(buttonStyle);

    // 添加到工具栏
    toolbarLayout->addWidget(m_panBtn);
    toolbarLayout->addWidget(m_zoomInBtn);
    toolbarLayout->addWidget(m_zoomOutBtn);
    toolbarLayout->addWidget(m_zoomFullBtn);
    toolbarLayout->addSpacing(10);
    toolbarLayout->addWidget(m_identifyBtn);
    toolbarLayout->addSpacing(10);
    toolbarLayout->addWidget(m_refreshBtn);
    toolbarLayout->addWidget(m_exportBtn);
    toolbarLayout->addStretch();

    m_mainLayout->addWidget(m_toolbar);

    qDebug() << "QgisMapWidget: 工具栏创建完成";
}

void QgisMapWidget::createLayerPanel()
{
    m_layerPanel = new QWidget(this);
    m_layerPanel->setFixedWidth(LAYER_PANEL_WIDTH);

    m_layerLayout = new QVBoxLayout(m_layerPanel);

    // 图层组
    m_layerGroup = new QGroupBox("图层管理", m_layerPanel);
    QVBoxLayout *layerGroupLayout = new QVBoxLayout(m_layerGroup);

    // 图层操作按钮
    QHBoxLayout *layerBtnLayout = new QHBoxLayout();
    m_addVectorBtn = new QPushButton("添加矢量", m_layerGroup);
    m_addRasterBtn = new QPushButton("添加栅格", m_layerGroup);
    m_removeLayerBtn = new QPushButton("移除图层", m_layerGroup);
    m_layerPropsBtn = new QPushButton("图层属性", m_layerGroup);

    layerBtnLayout->addWidget(m_addVectorBtn);
    layerBtnLayout->addWidget(m_addRasterBtn);
    layerGroupLayout->addLayout(layerBtnLayout);

    QHBoxLayout *layerBtnLayout2 = new QHBoxLayout();
    layerBtnLayout2->addWidget(m_removeLayerBtn);
    layerBtnLayout2->addWidget(m_layerPropsBtn);
    layerGroupLayout->addLayout(layerBtnLayout2);

    // 添加快速工具
    QHBoxLayout *quickToolsLayout = new QHBoxLayout();
    QPushButton *loadSampleBtn = new QPushButton("加载示例", m_layerGroup);
    loadSampleBtn->setToolTip("加载示例数据");
    QPushButton *clearAllBtn = new QPushButton("清空全部", m_layerGroup);
    clearAllBtn->setToolTip("清空所有图层");

    quickToolsLayout->addWidget(loadSampleBtn);
    quickToolsLayout->addWidget(clearAllBtn);
    layerGroupLayout->addLayout(quickToolsLayout);

    // 连接快速工具信号
    connect(loadSampleBtn, &QPushButton::clicked, [this]()
            {
        clearLayers();
        loadDefaultLayers();
        updateLayerList(); });
    connect(clearAllBtn, &QPushButton::clicked, [this]()
            {
        clearLayers();
        updateLayerList(); });

#ifdef QGIS_ENABLED
    // QGIS图层树视图
    m_layerTreeModel = new QgsLayerTreeModel(QgsProject::instance()->layerTreeRoot(), this);
    m_layerTreeView = new QgsLayerTreeView(m_layerGroup);
    m_layerTreeView->setModel(m_layerTreeModel);
    layerGroupLayout->addWidget(m_layerTreeView);
#else
    // 简化的图层列表
    m_layerTreeWidget = new QTreeWidget(m_layerGroup);
    m_layerTreeWidget->setHeaderLabels(QStringList() << "图层名称" << "类型");
    m_layerTreeWidget->header()->setStretchLastSection(false);
    m_layerTreeWidget->header()->setSectionResizeMode(0, QHeaderView::Stretch);
    m_layerTreeWidget->header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);
    layerGroupLayout->addWidget(m_layerTreeWidget);
#endif

    m_layerLayout->addWidget(m_layerGroup);
    m_layerLayout->addStretch();

    // m_rightSplitter->addWidget(m_layerPanel); // 已禁用

    qDebug() << "QgisMapWidget: 图层面板创建完成";
}

void QgisMapWidget::createPropertiesPanel()
{
    m_propertiesPanel = new QWidget(this);
    m_propertiesPanel->setFixedWidth(PROPERTIES_PANEL_WIDTH);

    QVBoxLayout *propsLayout = new QVBoxLayout(m_propertiesPanel);

    // 简化的属性面板
    QLabel *propsLabel = new QLabel("属性面板", m_propertiesPanel);
    propsLabel->setAlignment(Qt::AlignCenter);
    propsLabel->setStyleSheet("font-weight: bold; padding: 10px;");
    propsLayout->addWidget(propsLabel);

    // 基本信息显示
    QTextEdit *infoText = new QTextEdit(m_propertiesPanel);
    infoText->setMaximumHeight(200);
    infoText->setPlainText("QGIS专业地图组件\n\n当前功能:\n• 图层管理\n• 地图工具\n• 坐标显示\n• 数据导入导出");
    propsLayout->addWidget(infoText);

    // m_rightSplitter->addWidget(m_propertiesPanel); // 已禁用

    qDebug() << "QgisMapWidget: 属性面板创建完成";
}

// 简化版本：移除复杂的标签页创建方法

void QgisMapWidget::createStatusBar()
{
    m_statusBar = new QStatusBar(this);

    // 创建状态标签
    m_coordStatusLabel = new QLabel("坐标: 116.407400, 39.904200");
    m_scaleStatusLabel = new QLabel("比例尺: 1:1000000");
    m_crsStatusLabel = new QLabel("CRS: EPSG:4326");

    // 添加到状态栏
    m_statusBar->addWidget(m_coordStatusLabel);
    m_statusBar->addPermanentWidget(m_scaleStatusLabel);
    m_statusBar->addPermanentWidget(m_crsStatusLabel);

    m_mainLayout->addWidget(m_statusBar);

    qDebug() << "QgisMapWidget: 状态栏创建完成";
}

void QgisMapWidget::connectSignals()
{
    // 工具栏按钮信号连接
    connect(m_panBtn, &QPushButton::clicked, this, &QgisMapWidget::setPanTool);
    connect(m_zoomInBtn, &QPushButton::clicked, this, &QgisMapWidget::setZoomInTool);
    connect(m_zoomOutBtn, &QPushButton::clicked, this, &QgisMapWidget::setZoomOutTool);
    connect(m_zoomFullBtn, &QPushButton::clicked, this, &QgisMapWidget::zoomToFullExtent);
    connect(m_identifyBtn, &QPushButton::clicked, this, &QgisMapWidget::setIdentifyTool);
    connect(m_refreshBtn, &QPushButton::clicked, this, &QgisMapWidget::refreshMap);
    // 导出按钮连接将在后面添加

    // 浮动工具面板信号连接
    if (m_layerToggleBtn)
        connect(m_layerToggleBtn, &QPushButton::clicked, this, &QgisMapWidget::onLayerToggleClicked);
    if (m_measureBtn)
        connect(m_measureBtn, &QPushButton::clicked, this, &QgisMapWidget::onMeasureClicked);

    // 图层管理按钮信号连接
    connect(m_addVectorBtn, &QPushButton::clicked, this, &QgisMapWidget::onAddVectorLayerClicked);
    connect(m_addRasterBtn, &QPushButton::clicked, this, &QgisMapWidget::onAddRasterLayerClicked);
    connect(m_removeLayerBtn, &QPushButton::clicked, this, &QgisMapWidget::onRemoveLayerClicked);
    connect(m_layerPropsBtn, &QPushButton::clicked, this, &QgisMapWidget::onLayerPropertiesClicked);

    // 属性面板信号连接 - 只连接存在的控件
    if (m_crsCombo)
    {
        connect(m_crsCombo, &QComboBox::currentTextChanged, [this](const QString &text)
                {
            QString crsCode = text.split(" ").first();
            setCrs(crsCode); });
    }

    if (m_gotoCoordBtn && m_xCoordEdit && m_yCoordEdit)
    {
        connect(m_gotoCoordBtn, &QPushButton::clicked, [this]()
                {
            bool xOk, yOk;
            double x = m_xCoordEdit->text().toDouble(&xOk);
            double y = m_yCoordEdit->text().toDouble(&yOk);
            if (xOk && yOk) {
                setCenter(y, x); // 注意：setCenter参数是(lat, lng)
            } });
    }

    if (m_opacitySlider && m_opacityLabel)
    {
        connect(m_opacitySlider, &QSlider::valueChanged, [this](int value)
                {
            m_opacityLabel->setText(QString("%1%").arg(value));
            if (!m_selectedLayerId.isEmpty()) {
                setLayerOpacity(m_selectedLayerId, value / 100.0);
            } });
    }

    if (m_visibilityCheck)
    {
        connect(m_visibilityCheck, &QCheckBox::toggled, [this](bool checked)
                {
            if (!m_selectedLayerId.isEmpty()) {
                toggleLayerVisibility(m_selectedLayerId, checked);
            } });
    }

#ifndef QGIS_ENABLED
    // 简化版本的图层树信号连接
    if (m_layerTreeWidget)
    {
        connect(m_layerTreeWidget, &QTreeWidget::itemSelectionChanged, [this]()
                {
            auto selectedItems = m_layerTreeWidget->selectedItems();
            if (!selectedItems.isEmpty()) {
                QTreeWidgetItem *item = selectedItems.first();
                m_selectedLayerId = item->data(0, Qt::UserRole).toString();
                if (m_layerNameEdit) {
                    m_layerNameEdit->setText(item->text(0));
                }
            } });
    }
#endif

    qDebug() << "QgisMapWidget: 信号连接完成";
}

// QGIS初始化和工具设置
void QgisMapWidget::initializeQgis()
{
#ifdef QGIS_ENABLED
    // 初始化QGIS应用程序
    QgsApplication::setPrefixPath("/usr", true);
    QgsApplication::initQgis();
    qDebug() << "QgisMapWidget: QGIS初始化完成";
#else
    qDebug() << "QgisMapWidget: QGIS未启用，使用简化模式";
#endif
}

void QgisMapWidget::setupMapTools()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        // 创建地图工具
        m_panTool = new QgsMapToolPan(m_mapCanvas);
        m_zoomInTool = new QgsMapToolZoomIn(m_mapCanvas);
        m_zoomOutTool = new QgsMapToolZoomOut(m_mapCanvas);

        // 设置默认工具
        m_mapCanvas->setMapTool(m_panTool);

        qDebug() << "QgisMapWidget: 地图工具设置完成";
    }
#endif
}

void QgisMapWidget::loadDefaultLayers()
{
    // 添加一些示例图层信息到简化版本
#ifndef QGIS_ENABLED
    if (m_layerTreeWidget)
    {
        // 添加示例图层
        QTreeWidgetItem *item1 = new QTreeWidgetItem(m_layerTreeWidget);
        item1->setText(0, "🌍 世界边界");
        item1->setText(1, "矢量");
        item1->setData(0, Qt::UserRole, "world_boundaries");
        item1->setCheckState(0, Qt::Checked);

        QTreeWidgetItem *item2 = new QTreeWidgetItem(m_layerTreeWidget);
        item2->setText(0, "🗻 地形图");
        item2->setText(1, "栅格");
        item2->setData(0, Qt::UserRole, "terrain_map");
        item2->setCheckState(0, Qt::Checked);

        QTreeWidgetItem *item3 = new QTreeWidgetItem(m_layerTreeWidget);
        item3->setText(0, "🏙️ 城市点");
        item3->setText(1, "矢量");
        item3->setData(0, Qt::UserRole, "cities");
        item3->setCheckState(0, Qt::Checked);

        QTreeWidgetItem *item4 = new QTreeWidgetItem(m_layerTreeWidget);
        item4->setText(0, "🛣️ 道路网络");
        item4->setText(1, "矢量");
        item4->setData(0, Qt::UserRole, "roads");
        item4->setCheckState(0, Qt::Unchecked);

        m_layerIds << "world_boundaries" << "terrain_map" << "cities" << "roads";

        // 更新图层列表
        updateLayerList();

        qDebug() << "QgisMapWidget: 默认图层加载完成，共" << m_layerIds.size() << "个图层";
    }
#endif
}

// 公共接口实现
void QgisMapWidget::setCenter(double latitude, double longitude)
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        QgsPointXY center(longitude, latitude);
        m_mapCanvas->setCenter(center);
        m_mapCanvas->refresh();
    }
#endif

    // 更新坐标显示
    m_coordStatusLabel->setText(QString("坐标: %1, %2").arg(longitude, 0, 'f', 6).arg(latitude, 0, 'f', 6));
    m_xCoordEdit->setText(QString::number(longitude, 'f', 6));
    m_yCoordEdit->setText(QString::number(latitude, 'f', 6));

    qDebug() << "QgisMapWidget: 地图中心设置为" << latitude << longitude;
}

void QgisMapWidget::setZoomLevel(int zoom)
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        // QGIS使用比例尺而不是缩放级别
        double scale = 591657527.591555 / qPow(2.0, zoom); // Web Mercator比例尺转换
        m_mapCanvas->zoomScale(scale);
        m_mapCanvas->refresh();
    }
#endif

    // 更新比例尺显示（使用正确的Web Mercator公式）
    // 假设在北京纬度（39.9度）计算比例尺
    double latRad = 39.9 * M_PI / 180.0;
    double scale = 156543.03392 * qCos(latRad) / qPow(2.0, zoom);

    QString scaleText;
    if (scale > 1000000)
    {
        scaleText = QString("1:%1M").arg(QString::number(scale / 1000000, 'f', 1));
    }
    else if (scale > 1000)
    {
        scaleText = QString("1:%1K").arg(QString::number(scale / 1000, 'f', 0));
    }
    else
    {
        scaleText = QString("1:%1").arg(QString::number(scale, 'f', 0));
    }
    m_scaleStatusLabel->setText(QString("比例尺: %1").arg(scaleText));

    qDebug() << "QgisMapWidget: 缩放级别设置为" << zoom;
}

void QgisMapWidget::addVectorLayer(const QString &path, const QString &name)
{
#ifdef QGIS_ENABLED
    QgsVectorLayer *layer = new QgsVectorLayer(path, name, "ogr");
    if (layer->isValid())
    {
        QgsProject::instance()->addMapLayer(layer);
        m_layerIds.append(layer->id());
        emit layerAdded(layer->id(), name);
        qDebug() << "QgisMapWidget: 矢量图层添加成功:" << name;
    }
    else
    {
        delete layer;
        qWarning() << "QgisMapWidget: 矢量图层添加失败:" << path;
    }
#else
    // 简化版本：添加到图层列表
    if (m_layerTreeWidget)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_layerTreeWidget);
        item->setText(0, name);
        item->setText(1, "矢量");
        QString layerId = QString("vector_%1").arg(m_layerIds.size());
        item->setData(0, Qt::UserRole, layerId);
        item->setCheckState(0, Qt::Checked);

        m_layerIds.append(layerId);
        emit layerAdded(layerId, name);
        qDebug() << "QgisMapWidget: 矢量图层添加到列表:" << name;
    }
#endif
}

void QgisMapWidget::addRasterLayer(const QString &path, const QString &name)
{
#ifdef QGIS_ENABLED
    QgsRasterLayer *layer = new QgsRasterLayer(path, name);
    if (layer->isValid())
    {
        QgsProject::instance()->addMapLayer(layer);
        m_layerIds.append(layer->id());
        emit layerAdded(layer->id(), name);
        qDebug() << "QgisMapWidget: 栅格图层添加成功:" << name;
    }
    else
    {
        delete layer;
        qWarning() << "QgisMapWidget: 栅格图层添加失败:" << path;
    }
#else
    // 简化版本：添加到图层列表
    if (m_layerTreeWidget)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(m_layerTreeWidget);
        item->setText(0, name);
        item->setText(1, "栅格");
        QString layerId = QString("raster_%1").arg(m_layerIds.size());
        item->setData(0, Qt::UserRole, layerId);
        item->setCheckState(0, Qt::Checked);

        m_layerIds.append(layerId);
        emit layerAdded(layerId, name);
        qDebug() << "QgisMapWidget: 栅格图层添加到列表:" << name;
    }
#endif
}

void QgisMapWidget::removeLayer(const QString &layerId)
{
#ifdef QGIS_ENABLED
    QgsProject::instance()->removeMapLayer(layerId);
#else
    // 简化版本：从图层列表移除
    if (m_layerTreeWidget)
    {
        for (int i = 0; i < m_layerTreeWidget->topLevelItemCount(); ++i)
        {
            QTreeWidgetItem *item = m_layerTreeWidget->topLevelItem(i);
            if (item->data(0, Qt::UserRole).toString() == layerId)
            {
                delete m_layerTreeWidget->takeTopLevelItem(i);
                break;
            }
        }
    }
#endif

    m_layerIds.removeAll(layerId);
    emit layerRemoved(layerId);
    qDebug() << "QgisMapWidget: 图层移除:" << layerId;
}

void QgisMapWidget::clearLayers()
{
#ifdef QGIS_ENABLED
    QgsProject::instance()->clear();
#else
    if (m_layerTreeWidget)
    {
        m_layerTreeWidget->clear();
    }
#endif

    m_layerIds.clear();
    qDebug() << "QgisMapWidget: 所有图层已清除";
}

// 地图工具设置
void QgisMapWidget::setPanTool()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas && m_panTool)
    {
        m_mapCanvas->setMapTool(m_panTool);
    }
#endif

    // 更新按钮状态
    m_panBtn->setChecked(true);
    m_zoomInBtn->setChecked(false);
    m_zoomOutBtn->setChecked(false);
    m_identifyBtn->setChecked(false);

    qDebug() << "QgisMapWidget: 平移工具激活";
}

void QgisMapWidget::setZoomInTool()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas && m_zoomInTool)
    {
        m_mapCanvas->setMapTool(m_zoomInTool);
    }
#endif

    // 更新按钮状态
    m_panBtn->setChecked(false);
    m_zoomInBtn->setChecked(true);
    m_zoomOutBtn->setChecked(false);
    m_identifyBtn->setChecked(false);

    qDebug() << "QgisMapWidget: 放大工具激活";
}

void QgisMapWidget::setZoomOutTool()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas && m_zoomOutTool)
    {
        m_mapCanvas->setMapTool(m_zoomOutTool);
    }
#endif

    // 更新按钮状态
    m_panBtn->setChecked(false);
    m_zoomInBtn->setChecked(false);
    m_zoomOutBtn->setChecked(true);
    m_identifyBtn->setChecked(false);

    qDebug() << "QgisMapWidget: 缩小工具激活";
}

void QgisMapWidget::setIdentifyTool()
{
    // 更新按钮状态
    m_panBtn->setChecked(false);
    m_zoomInBtn->setChecked(false);
    m_zoomOutBtn->setChecked(false);
    m_identifyBtn->setChecked(true);

    qDebug() << "QgisMapWidget: 识别工具激活";
}

// 坐标系统和其他方法
void QgisMapWidget::setCrs(const QString &crsCode)
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        QgsCoordinateReferenceSystem crs(crsCode);
        if (crs.isValid())
        {
            m_mapCanvas->setDestinationCrs(crs);
            m_mapCanvas->refresh();
            m_currentCrs = crsCode;
            emit crsChanged(crsCode);
        }
    }
#else
    m_currentCrs = crsCode;
    emit crsChanged(crsCode);
#endif

    m_crsStatusLabel->setText(QString("CRS: %1").arg(crsCode));
    qDebug() << "QgisMapWidget: 坐标系统设置为" << crsCode;
}

QString QgisMapWidget::getCurrentCrs() const
{
    return m_currentCrs;
}

// 槽函数实现
void QgisMapWidget::zoomToFullExtent()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        m_mapCanvas->zoomToFullExtent();
        m_mapCanvas->refresh();
    }
#endif
    qDebug() << "QgisMapWidget: 缩放到全图范围";
}

void QgisMapWidget::zoomToLayer(const QString &layerId)
{
#ifdef QGIS_ENABLED
    QgsMapLayer *layer = QgsProject::instance()->mapLayer(layerId);
    if (layer && m_mapCanvas)
    {
        m_mapCanvas->setExtent(layer->extent());
        m_mapCanvas->refresh();
    }
#endif
    qDebug() << "QgisMapWidget: 缩放到图层" << layerId;
}

void QgisMapWidget::refreshMap()
{
#ifdef QGIS_ENABLED
    if (m_mapCanvas)
    {
        m_mapCanvas->refresh();
    }
#endif
    qDebug() << "QgisMapWidget: 地图刷新";
}

void QgisMapWidget::exportMap(const QString &filePath, const QString &format)
{
    Q_UNUSED(filePath)
    Q_UNUSED(format)

#ifdef QGIS_ENABLED
    // 实现地图导出功能
    if (m_mapCanvas)
    {
        // 这里可以实现具体的导出逻辑
        qDebug() << "QgisMapWidget: 地图导出到" << filePath << "格式:" << format;
    }
#else
    QMessageBox::information(this, "导出地图", "地图导出功能需要完整的QGIS支持");
#endif
}

void QgisMapWidget::toggleLayerVisibility(const QString &layerId, bool visible)
{
#ifdef QGIS_ENABLED
    QgsMapLayer *layer = QgsProject::instance()->mapLayer(layerId);
    if (layer)
    {
        // QGIS中通过图层树控制可见性
        QgsProject::instance()->layerTreeRoot()->findLayer(layer)->setItemVisibilityChecked(visible);
        m_mapCanvas->refresh();
    }
#else
    // 简化版本：更新图层列表中的复选框状态
    if (m_layerTreeWidget)
    {
        for (int i = 0; i < m_layerTreeWidget->topLevelItemCount(); ++i)
        {
            QTreeWidgetItem *item = m_layerTreeWidget->topLevelItem(i);
            if (item->data(0, Qt::UserRole).toString() == layerId)
            {
                item->setCheckState(0, visible ? Qt::Checked : Qt::Unchecked);
                break;
            }
        }
    }
#endif

    qDebug() << "QgisMapWidget: 图层可见性设置" << layerId << visible;
}

void QgisMapWidget::setLayerOpacity(const QString &layerId, double opacity)
{
#ifdef QGIS_ENABLED
    QgsMapLayer *layer = QgsProject::instance()->mapLayer(layerId);
    if (layer)
    {
        layer->setOpacity(opacity);
        m_mapCanvas->refresh();
    }
#endif

    qDebug() << "QgisMapWidget: 图层透明度设置" << layerId << opacity;
}

void QgisMapWidget::moveLayerUp(const QString &layerId)
{
    Q_UNUSED(layerId)
    qDebug() << "QgisMapWidget: 图层上移" << layerId;
}

void QgisMapWidget::moveLayerDown(const QString &layerId)
{
    Q_UNUSED(layerId)
    qDebug() << "QgisMapWidget: 图层下移" << layerId;
}

// 私有槽函数实现
void QgisMapWidget::onMapCanvasClicked()
{
    qDebug() << "QgisMapWidget: 地图画布点击";
}

void QgisMapWidget::onExtentChanged()
{
    qDebug() << "QgisMapWidget: 地图范围改变";
    emit mapExtentChanged();
}

void QgisMapWidget::onLayerTreeChanged()
{
    updateLayerList();
    qDebug() << "QgisMapWidget: 图层树改变";
}

void QgisMapWidget::onAddVectorLayerClicked()
{
    QString filePath = QFileDialog::getOpenFileName(this,
                                                    "添加矢量图层",
                                                    "",
                                                    "矢量文件 (*.shp *.geojson *.kml *.gpx);;所有文件 (*.*)");
    if (!filePath.isEmpty())
    {
        QFileInfo fileInfo(filePath);
        addVectorLayer(filePath, fileInfo.baseName());
    }
}

void QgisMapWidget::onAddRasterLayerClicked()
{
    QString filePath = QFileDialog::getOpenFileName(this,
                                                    "添加栅格图层",
                                                    "",
                                                    "栅格文件 (*.tif *.tiff *.jpg *.png *.bmp);;所有文件 (*.*)");
    if (!filePath.isEmpty())
    {
        QFileInfo fileInfo(filePath);
        addRasterLayer(filePath, fileInfo.baseName());
    }
}

void QgisMapWidget::onRemoveLayerClicked()
{
    if (!m_selectedLayerId.isEmpty())
    {
        int ret = QMessageBox::question(this, "移除图层",
                                        "确定要移除选中的图层吗？",
                                        QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes)
        {
            removeLayer(m_selectedLayerId);
            m_selectedLayerId.clear();
            m_layerNameEdit->clear();
        }
    }
    else
    {
        QMessageBox::information(this, "移除图层", "请先选择要移除的图层。");
    }
}

void QgisMapWidget::onLayerPropertiesClicked()
{
    if (!m_selectedLayerId.isEmpty())
    {
        showLayerProperties(m_selectedLayerId);
    }
    else
    {
        QMessageBox::information(this, "图层属性", "请先选择图层。");
    }
}

void QgisMapWidget::onExportMapClicked()
{
    QString filePath = QFileDialog::getSaveFileName(this,
                                                    "导出地图",
                                                    "map.png",
                                                    "PNG文件 (*.png);;JPEG文件 (*.jpg);;PDF文件 (*.pdf)");
    if (!filePath.isEmpty())
    {
        QFileInfo fileInfo(filePath);
        exportMap(filePath, fileInfo.suffix().toUpper());
    }
}

// 辅助方法实现
void QgisMapWidget::updateLayerList()
{
#ifdef QGIS_ENABLED
    // QGIS版本会自动更新图层树视图
#else
    // 简化版本：更新图层统计信息
    if (m_layerTreeWidget)
    {
        // 现在使用TileMapView，不需要更新占位符
        qDebug() << "QgisMapWidget: 图层列表已更新，当前图层数:" << m_layerIds.size();
    }
#endif
}

void QgisMapWidget::updateMapInfo()
{
    // 简化版本：更新基本地图信息
    qDebug() << "QgisMapWidget: 地图信息更新 - CRS:" << m_currentCrs << "图层数:" << m_layerIds.size();
}

void QgisMapWidget::showLayerProperties(const QString &layerId)
{
    Q_UNUSED(layerId)

    // 简化版本：显示图层属性对话框
    QMessageBox::information(this, "图层属性",
                             QString("图层ID: %1\n\n这里可以显示详细的图层属性信息。\n\n当前功能:\n• 图层可见性控制\n• 透明度调整\n• 基本信息显示").arg(layerId));
}

void QgisMapWidget::updateMapPlaceholder()
{
#ifndef QGIS_ENABLED
    // 现在使用TileMapView显示真实地图，不再需要更新占位符
    // 但保留此方法以维持接口兼容性
    qDebug() << "QgisMapWidget: 使用TileMapView显示瓦片地图，图层数量:" << m_layerIds.size();
#endif
}

void QgisMapWidget::createFloatingToolPanel()
{
    // 创建浮动工具面板 - 测距功能、图层管理和目标图标
    m_floatingToolPanel = new QWidget(m_mapContainer);
    m_floatingToolPanel->setFixedSize(190, 40); // 调整宽度适应窗口
    m_floatingToolPanel->setStyleSheet(
        "QWidget { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(255, 255, 255, 250), stop:1 rgba(240, 240, 240, 250)); "
        "border: 1px solid rgba(180, 180, 180, 200); "
        "border-radius: 6px; "
        "box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15); "
        "}"
        "QPushButton { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 #ffffff, stop:1 #f0f0f0); "
        "border: 1px solid #c0c0c0; "
        "border-radius: 3px; "
        "padding: 3px 6px; "
        "margin: 1px; "
        "font-size: 11px; "
        "font-weight: bold; "
        "color: #333; "
        "}"
        "QPushButton:hover { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 #e8f4fd, stop:1 #bee6fd); "
        "border: 1px solid #3c7fb1; "
        "}"
        "QPushButton:pressed { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 #daecfc, stop:1 #c4e0fc); "
        "border: 1px solid #3c7fb1; "
        "}");

    // 创建水平布局
    QHBoxLayout *toolLayout = new QHBoxLayout(m_floatingToolPanel);
    toolLayout->setContentsMargins(4, 4, 4, 4);
    toolLayout->setSpacing(2);

    // 创建图层管理按钮
    m_layerToggleBtn = new QPushButton("🗂️ 图层", m_floatingToolPanel);
    m_layerToggleBtn->setToolTip("图层管理");
    m_layerToggleBtn->setFixedSize(60, 28);

    // 创建测距按钮
    m_measureBtn = new QPushButton("📏 测距", m_floatingToolPanel);
    m_measureBtn->setToolTip("距离测量");
    m_measureBtn->setFixedSize(60, 28);

    // 创建目标图标测试按钮
    QPushButton *targetTestBtn = new QPushButton("🎯 目标", m_floatingToolPanel);
    targetTestBtn->setToolTip("添加测试目标");
    targetTestBtn->setFixedSize(60, 28);

    // 添加按钮到布局
    toolLayout->addWidget(m_layerToggleBtn);
    toolLayout->addWidget(m_measureBtn);
    toolLayout->addWidget(targetTestBtn);

    // 连接目标按钮信号 - 显示选择菜单
    connect(targetTestBtn, &QPushButton::clicked, [this, targetTestBtn]()
            { showTargetSelectionMenu(targetTestBtn); });

    // 设置浮动面板位置（右上角），确保完全显示在窗口内且不与地图信息面板重叠
    int panelWidth = m_floatingToolPanel->width();
    int containerWidth = m_mapContainer->width();
    int xPos = qMax(300, containerWidth - panelWidth - 10); // 确保不与左上角的地图信息面板重叠
    m_floatingToolPanel->move(xPos, 10);
    m_floatingToolPanel->raise();
    m_floatingToolPanel->show();

    // 创建图层选择面板（初始隐藏）
    createLayerSelectorPanel();

    // 初始化测距相关变量
    m_measureMode = false;
    m_measurePoints.clear();
    m_totalDistance = 0.0;
    m_startPointWidget = nullptr;
    m_dynamicLineWidget = nullptr;
    m_hasStartPoint = false;

    // 初始化轨迹相关变量
    m_trajectoryVisible = false;
    m_currentTrajectoryTargetId = "";

    qDebug() << "QgisMapWidget: 浮动工具面板创建完成";
}

void QgisMapWidget::createMapInfoPanel()
{
    // 创建地理功能分析展示面板（左上角）
    m_mapInfoPanel = new QWidget(m_mapContainer);
    m_mapInfoPanel->setFixedSize(280, 120); // 调整面板尺寸
    m_mapInfoPanel->setStyleSheet(
        "QWidget { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(255, 255, 255, 180), stop:1 rgba(245, 245, 245, 180)); " // 降低透明度
        "border: 1px solid rgba(180, 180, 180, 120); "                        // 降低边框透明度
        "border-radius: 6px; "
        "}"
        "QLabel { "
        "background: transparent; "
        "color: #333; "
        "font-size: 11px; " // 稍微增大字体
        "font-weight: bold; "
        "padding: 2px 4px; " // 增加内边距
        "}");

    // 创建垂直布局
    QVBoxLayout *infoLayout = new QVBoxLayout(m_mapInfoPanel);
    infoLayout->setContentsMargins(8, 5, 8, 5); // 增加边距
    infoLayout->setSpacing(2);                  // 增加行间距

    // 坐标系信息
    m_crsInfoLabel = new QLabel("坐标系: WGS84 (EPSG:4326)", m_mapInfoPanel);
    m_crsInfoLabel->setStyleSheet("color: #1e3a8a; font-weight: bold; font-size: 11px;");

    // 比例尺信息
    m_scaleInfoLabel = new QLabel("比例尺: 1:1,000,000", m_mapInfoPanel);
    m_scaleInfoLabel->setStyleSheet("color: #1e40af; font-size: 11px;");

    // 缩放层级信息
    m_zoomInfoLabel = new QLabel("层级: 5", m_mapInfoPanel);
    m_zoomInfoLabel->setStyleSheet("color: #1e40af; font-size: 11px;");

    // 鼠标坐标信息
    m_mouseCoordInfoLabel = new QLabel("坐标: 116.3974°E, 39.9093°N", m_mapInfoPanel);
    m_mouseCoordInfoLabel->setStyleSheet("color: #dc2626; font-weight: bold; font-size: 11px;");

    // 添加到布局
    infoLayout->addWidget(m_crsInfoLabel);
    infoLayout->addWidget(m_scaleInfoLabel);
    infoLayout->addWidget(m_zoomInfoLabel);
    infoLayout->addWidget(m_mouseCoordInfoLabel);

    // 设置位置（左上角，避免遮挡浮动工具面板）
    // 动态计算左上角位置
    int panelWidth = 280;
    int panelHeight = 120;
    int margin = 10;
    int x = margin;
    int y = margin;
    m_mapInfoPanel->move(x, y);
    m_mapInfoPanel->raise();
    m_mapInfoPanel->show();

    qDebug() << "QgisMapWidget: 地图信息面板创建完成";
}

void QgisMapWidget::createGeographicDataPanel()
{
    // 创建地理数据面板
    m_geographicDataPanel = new GeographicDataPanel(this);

    // 连接信号
    connect(m_geographicDataPanel, &GeographicDataPanel::panelClosed,
            this, &QgisMapWidget::onGeographicDataPanelClosed);
    connect(m_geographicDataPanel, &GeographicDataPanel::refreshRequested,
            this, &QgisMapWidget::onGeographicDataRefreshRequested);
    connect(m_geographicDataPanel, &GeographicDataPanel::rowSelected,
            this, &QgisMapWidget::onGeographicDataRowSelected);

    // 设置面板位置为底部
    m_geographicDataPanel->setPosition(GeographicDataPanel::Bottom);

    qDebug() << "QgisMapWidget: 地理数据面板创建完成";
}

void QgisMapWidget::createTopRightControlPanel()
{
    // 创建右上角控制面板 - 纵向布局，增加高度以容纳更多按钮
    m_topRightControlPanel = new QWidget(this);
    m_topRightControlPanel->setFixedSize(50, 280);

    // 设置面板样式
    m_topRightControlPanel->setStyleSheet(
        "QWidget { "
        "background-color: rgba(255, 255, 255, 230); "
        "border: 2px solid rgba(70, 130, 180, 200); "
        "border-radius: 8px; "
        "}");

    // 创建纵向布局
    QVBoxLayout *controlLayout = new QVBoxLayout(m_topRightControlPanel);
    controlLayout->setContentsMargins(8, 8, 8, 8);
    controlLayout->setSpacing(6);

    // 创建数据面板切换按钮
    m_dataPanelToggleBtn = new QPushButton("📊", m_topRightControlPanel);
    m_dataPanelToggleBtn->setFixedSize(34, 34);
    m_dataPanelToggleBtn->setToolTip("显示/隐藏地理数据面板");

    // 创建图层切换按钮（从浮动工具面板移过来）
    QPushButton *layerBtn = new QPushButton("🗺️", m_topRightControlPanel);
    layerBtn->setFixedSize(34, 34);
    layerBtn->setToolTip("图层管理");

    // 创建测量工具按钮
    m_topRightMeasureBtn = new QPushButton("📏", m_topRightControlPanel);
    m_topRightMeasureBtn->setFixedSize(34, 34);
    m_topRightMeasureBtn->setToolTip("测量工具");

    // 创建目标工具按钮
    QPushButton *targetBtn = new QPushButton("🎯", m_topRightControlPanel);
    targetBtn->setFixedSize(34, 34);
    targetBtn->setToolTip("添加目标");

    // 创建定位按钮
    QPushButton *locationBtn = new QPushButton("📍", m_topRightControlPanel);
    locationBtn->setFixedSize(34, 34);
    locationBtn->setToolTip("经纬度定位");

    // 创建刷新按钮
    QPushButton *refreshBtn = new QPushButton("🔄", m_topRightControlPanel);
    refreshBtn->setFixedSize(34, 34);
    refreshBtn->setToolTip("刷新地图");

    // 统一按钮样式
    QString buttonStyle =
        "QPushButton { "
        "background-color: rgba(52, 152, 219, 200); "
        "color: white; "
        "border: none; "
        "border-radius: 6px; "
        "font-size: 16px; "
        "font-weight: bold; "
        "} "
        "QPushButton:hover { "
        "background-color: rgba(41, 128, 185, 220); "
        "transform: scale(1.05); "
        "} "
        "QPushButton:pressed { "
        "background-color: rgba(31, 97, 141, 240); "
        "transform: scale(0.95); "
        "}";

    m_dataPanelToggleBtn->setStyleSheet(buttonStyle);
    layerBtn->setStyleSheet(buttonStyle);
    m_topRightMeasureBtn->setStyleSheet(buttonStyle);
    targetBtn->setStyleSheet(buttonStyle);
    locationBtn->setStyleSheet(buttonStyle);
    refreshBtn->setStyleSheet(buttonStyle);

    // 连接信号
    connect(m_dataPanelToggleBtn, &QPushButton::clicked, this, &QgisMapWidget::onDataPanelToggleClicked);
    connect(layerBtn, &QPushButton::clicked, this, &QgisMapWidget::onLayerToggleClicked);
    connect(m_topRightMeasureBtn, &QPushButton::clicked, this, &QgisMapWidget::onMeasureClicked);
    connect(targetBtn, &QPushButton::clicked, [this, targetBtn]()
            { showTargetSelectionMenu(targetBtn); });
    connect(locationBtn, &QPushButton::clicked, this, &QgisMapWidget::onLocationClicked);
    connect(refreshBtn, &QPushButton::clicked, this, &QgisMapWidget::refreshGeographicData);

    // 添加到纵向布局
    controlLayout->addWidget(m_dataPanelToggleBtn);
    controlLayout->addWidget(layerBtn);
    controlLayout->addWidget(m_topRightMeasureBtn);
    controlLayout->addWidget(targetBtn);
    controlLayout->addWidget(locationBtn);
    controlLayout->addWidget(refreshBtn);
    controlLayout->addStretch();

    // 定位到右上角
    updateTopRightControlPanelPosition();

    // 显示面板
    m_topRightControlPanel->show();

    // 创建图层选择面板（确保图层管理功能正常）
    createLayerSelectorPanel();

    // 初始化测距相关变量（从createFloatingToolPanel移过来）
    m_measureMode = false;
    m_measurePoints.clear();
    m_totalDistance = 0.0;
    m_startPointWidget = nullptr;
    m_dynamicLineWidget = nullptr;
    m_hasStartPoint = false;

    // 初始化轨迹相关变量
    m_trajectoryVisible = false;
    m_currentTrajectoryTargetId = "";

    qDebug() << "QgisMapWidget: 右上角纵向控制面板创建完成";
}

void QgisMapWidget::updateTopRightControlPanelPosition()
{
    if (!m_topRightControlPanel)
    {
        return;
    }

    // 定位到右上角，留出边距
    int margin = 15;
    int x = width() - m_topRightControlPanel->width() - margin;
    int y = margin;

    m_topRightControlPanel->move(x, y);
}

// 地理数据面板相关方法
void QgisMapWidget::showGeographicDataPanel()
{
    if (m_geographicDataPanel)
    {
        m_geographicDataPanel->showPanel();
        qDebug() << "QgisMapWidget: 显示地理数据面板";
    }
}

void QgisMapWidget::hideGeographicDataPanel()
{
    if (m_geographicDataPanel)
    {
        m_geographicDataPanel->hidePanel();
        qDebug() << "QgisMapWidget: 隐藏地理数据面板";
    }
}

void QgisMapWidget::toggleGeographicDataPanel()
{
    if (m_geographicDataPanel)
    {
        m_geographicDataPanel->togglePanel();
        qDebug() << "QgisMapWidget: 切换地理数据面板显示状态";
    }
}

// 地理数据面板槽函数
void QgisMapWidget::onGeographicDataPanelClosed()
{
    qDebug() << "QgisMapWidget: 地理数据面板已关闭";
}

void QgisMapWidget::onGeographicDataRefreshRequested()
{
    qDebug() << "QgisMapWidget: 地理数据刷新请求";
    refreshGeographicData();
}

void QgisMapWidget::onGeographicDataRowSelected(int row, const QStringList &data)
{
    qDebug() << "QgisMapWidget: 选中地理数据行" << row << "数据:" << data;

    // 如果数据包含坐标信息，可以在地图上高亮显示
    if (data.size() >= 7)
    {
        bool latOk, lngOk;
        double latitude = data[6].toDouble(&latOk);
        double longitude = data[5].toDouble(&lngOk);

        if (latOk && lngOk)
        {
            // 在地图上定位到该坐标
            setCenter(latitude, longitude);
            qDebug() << "QgisMapWidget: 定位到坐标" << latitude << "," << longitude;
        }
    }
}

void QgisMapWidget::onDataPanelToggleClicked()
{
    qDebug() << "QgisMapWidget: 数据面板切换按钮点击";
    toggleGeographicDataPanel();
}

void QgisMapWidget::onLocationClicked()
{
    qDebug() << "QgisMapWidget: 定位按钮被点击";
    showLocationDialog();
}

void QgisMapWidget::processGeographicData()
{
    qDebug() << "QgisMapWidget: 开始处理地理数据";

    if (!m_geographicDataPanel)
    {
        qWarning() << "QgisMapWidget: 地理数据面板未初始化";
        return;
    }

    // 准备示例数据
    QList<QStringList> sampleData;
    sampleData << (QStringList() << "1" << "测试系统" << "UE001" << "1"
                                 << "2024-01-15 10:30:00" << "116.404000" << "39.915000" << "2024-01-15 10:30:00");
    sampleData << (QStringList() << "2" << "测试系统" << "UE002" << "2"
                                 << "2024-01-15 11:00:00" << "121.473000" << "31.230000" << "2024-01-15 11:00:00");
    sampleData << (QStringList() << "3" << "测试系统" << "UE003" << "1"
                                 << "2024-01-15 11:30:00" << "113.264000" << "23.129000" << "2024-01-15 11:30:00");

    // 更新面板数据
    m_geographicDataPanel->setTableData(sampleData);

    // 更新统计信息
    m_geographicDataPanel->updateStatistics(
        3, 100,
        "华北地区: 1个点 (33.3%)\n华东地区: 1个点 (33.3%)\n华南地区: 1个点 (33.3%)",
        "纬度: 23.129000° ~ 39.915000°\n经度: 113.264000° ~ 121.473000°\n覆盖范围: 16.786° × 8.209°",
        "数据点总数: 3\n最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));

    qDebug() << "QgisMapWidget: 地理数据处理完成";
}

// 旧的方法已移除，功能已集成到GeographicDataPanel中

// 旧的统计方法已移除，功能已集成到GeographicDataPanel中

// 这些方法已移动到GeographicDataPanel中

void QgisMapWidget::refreshGeographicData()
{
    qDebug() << "QgisMapWidget: 刷新地理数据";
    processGeographicData();

    // 不自动显示面板，由用户通过按钮控制
    // showGeographicDataPanel();
}

void QgisMapWidget::repositionMapInfoPanel()
{
    if (!m_mapInfoPanel || !m_mapContainer)
    {
        return;
    }

    // 动态计算左上角位置
    int panelWidth = 280;
    int panelHeight = 120;
    int margin = 10;
    int x = margin;
    int y = margin;
    m_mapInfoPanel->move(x, y);
}

void QgisMapWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 重新定位地图信息面板到右下角
    repositionMapInfoPanel();

    // 重新定位浮动工具面板到右上角（已合并到右上角控制面板）
    // repositionFloatingToolPanel();

    // 重新定位右上角控制面板
    updateTopRightControlPanelPosition();

    // 重新定位图层选择面板
    if (m_layerSelectorPanel && m_mapContainer)
    {
        int panelX = m_mapContainer->width() - 190 - 60; // 控制面板宽度50px + 间距10px
        int panelY = 15;                                 // 与控制面板顶部对齐
        m_layerSelectorPanel->move(panelX, panelY);
    }
}

void QgisMapWidget::updateMapInfoPanel()
{
    if (!m_mapInfoPanel || !m_tileMapView)
        return;

    // 更新坐标系信息
    m_crsInfoLabel->setText("坐标系: WGS84 (EPSG:4326)");

    // 从TileMapView获取实际的缩放级别
    int zoomLevel = m_tileMapView->getZoomLevel();
    m_zoomInfoLabel->setText(QString("层级: %1").arg(zoomLevel));

    // 根据缩放级别计算比例尺（Web Mercator投影）
    // 在赤道处的比例尺计算：156543.03392 * cos(纬度) / 2^缩放级别
    auto center = m_tileMapView->getCenter();
    double lat = center.first;
    double latRad = lat * M_PI / 180.0;
    double scale = 156543.03392 * qCos(latRad) / qPow(2, zoomLevel);

    QString scaleText;
    if (scale > 1000000)
    {
        scaleText = QString("1:%1M").arg(QString::number(scale / 1000000, 'f', 1));
    }
    else if (scale > 1000)
    {
        scaleText = QString("1:%1K").arg(QString::number(scale / 1000, 'f', 0));
    }
    else
    {
        scaleText = QString("1:%1").arg(QString::number(scale, 'f', 0));
    }
    m_scaleInfoLabel->setText(QString("比例尺: %1").arg(scaleText));

    // 注意：鼠标坐标由updateMouseCoordinateDisplay方法单独更新
}

void QgisMapWidget::updateMouseCoordinateDisplay(double lat, double lng)
{
    if (!m_mapInfoPanel)
        return;

    // 更新鼠标坐标显示
    m_mouseCoordInfoLabel->setText(QString("鼠标: %1°E, %2°N")
                                       .arg(QString::number(lng, 'f', 4))
                                       .arg(QString::number(lat, 'f', 4)));
}

void QgisMapWidget::createLayerSelectorPanel()
{
    // 创建图层选择面板
    m_layerSelectorPanel = new QWidget(m_mapContainer);
    m_layerSelectorPanel->setFixedSize(180, 150);
    m_layerSelectorPanel->setStyleSheet(
        "QWidget { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(255, 255, 255, 240), stop:1 rgba(245, 245, 245, 240)); "
        "border: 1px solid rgba(180, 180, 180, 200); "
        "border-radius: 8px; "
        "}"
        "QCheckBox { "
        "padding: 3px; "
        "font-size: 12px; "
        "color: #333; "
        "}"
        "QCheckBox::indicator { "
        "width: 16px; "
        "height: 16px; "
        "}"
        "QCheckBox::indicator:unchecked { "
        "background-color: white; "
        "border: 1px solid #ccc; "
        "border-radius: 3px; "
        "}"
        "QCheckBox::indicator:checked { "
        "background-color: #4CAF50; "
        "border: 1px solid #4CAF50; "
        "border-radius: 3px; "
        "}");

    QVBoxLayout *layerLayout = new QVBoxLayout(m_layerSelectorPanel);
    layerLayout->setContentsMargins(10, 8, 10, 8);
    layerLayout->setSpacing(5);

    // 添加标题
    QLabel *titleLabel = new QLabel("🗺️ 图层管理", m_layerSelectorPanel);
    titleLabel->setStyleSheet("font-weight: bold; font-size: 13px; color: #2c3e50;");
    layerLayout->addWidget(titleLabel);

    // 添加分隔线
    QFrame *line = new QFrame();
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    line->setStyleSheet("color: #bdc3c7;");
    layerLayout->addWidget(line);

    // 添加图层选项
    QCheckBox *bAreaCheck = new QCheckBox("B区域", m_layerSelectorPanel);
    bAreaCheck->setChecked(false);
    QCheckBox *bPointCheck = new QCheckBox("B点", m_layerSelectorPanel);
    bPointCheck->setChecked(false);
    QCheckBox *gAreaCheck = new QCheckBox("G区域", m_layerSelectorPanel);
    gAreaCheck->setChecked(false);
    QCheckBox *gPointCheck = new QCheckBox("G点", m_layerSelectorPanel);
    gPointCheck->setChecked(false);

    layerLayout->addWidget(bAreaCheck);
    layerLayout->addWidget(bPointCheck);
    layerLayout->addWidget(gAreaCheck);
    layerLayout->addWidget(gPointCheck);
    layerLayout->addStretch();

    if (bAreaCheck->isChecked())
    {
        showBAreaLogs();
    }

    // 连接图层选择信号
    connect(bAreaCheck, &QCheckBox::toggled, [this](bool checked)
            {
                qDebug() << "QgisMapWidget: B区域图层" << (checked ? "显示" : "隐藏");
                if (checked) {
                    showBAreaLogs();
                } else {
                    hideBAreaLogs();
                } });

    connect(bPointCheck, &QCheckBox::toggled, [this](bool checked)
            {
                qDebug() << "QgisMapWidget: B点图层" << (checked ? "显示" : "隐藏");
                // 这里可以添加实际的图层显示/隐藏逻辑
            });

    connect(gAreaCheck, &QCheckBox::toggled, [this](bool checked)
            {
                qDebug() << "QgisMapWidget: G区域图层" << (checked ? "显示" : "隐藏");
                // 这里可以添加实际的图层显示/隐藏逻辑
            });

    connect(gPointCheck, &QCheckBox::toggled, [this](bool checked)
            {
                qDebug() << "QgisMapWidget: G点图层" << (checked ? "显示" : "隐藏");
                // 这里可以添加实际的图层显示/隐藏逻辑
            });

    // 设置位置并隐藏（右上角控制面板左侧，避免重叠）
    int panelX = m_mapContainer->width() - 190 - 60; // 控制面板宽度50px + 间距10px
    int panelY = 15;                                 // 与控制面板顶部对齐
    m_layerSelectorPanel->move(panelX, panelY);
    m_layerSelectorPanel->hide();

    qDebug() << "QgisMapWidget: 图层选择面板创建完成";
}

// 浮动工具面板槽函数实现

void QgisMapWidget::onLayerToggleClicked()
{
    qDebug() << "QgisMapWidget: 图层管理按钮被点击";
    if (m_layerSelectorPanel)
    {
        if (m_layerSelectorPanel->isVisible())
        {
            m_layerSelectorPanel->hide();
            // 恢复按钮样式
            if (m_layerToggleBtn)
            {
                m_layerToggleBtn->setStyleSheet("");
            }
            qDebug() << "QgisMapWidget: 图层选择面板已隐藏";
        }
        else
        {
            // 关闭测距模式
            if (m_measureMode)
            {
                m_measureMode = false;
                clearMeasureLines();
                // 重置测量按钮状态
                if (m_measureBtn)
                {
                    m_measureBtn->setText("📏 测距");
                    m_measureBtn->setStyleSheet("");
                }
                if (m_topRightMeasureBtn)
                {
                    m_topRightMeasureBtn->setToolTip("测量工具");
                    m_topRightMeasureBtn->setStyleSheet(
                        "QPushButton { "
                        "background-color: rgba(52, 152, 219, 200); "
                        "color: white; "
                        "border: none; "
                        "border-radius: 6px; "
                        "font-size: 16px; "
                        "font-weight: bold; "
                        "} "
                        "QPushButton:hover { "
                        "background-color: rgba(41, 128, 185, 220); "
                        "transform: scale(1.05); "
                        "} "
                        "QPushButton:pressed { "
                        "background-color: rgba(31, 97, 141, 240); "
                        "transform: scale(0.95); "
                        "}");
                }
            }

            m_layerSelectorPanel->show();
            m_layerSelectorPanel->raise();

            // 设置按钮为激活状态（绿色）
            if (m_layerToggleBtn)
            {
                m_layerToggleBtn->setStyleSheet("background-color: #4CAF50; color: white; border-radius: 4px;");
            }

            qDebug() << "QgisMapWidget: 图层选择面板已显示";
        }
    }
    else
    {
        qDebug() << "QgisMapWidget: 图层选择面板为空指针";
    }
}

void QgisMapWidget::onMeasureClicked()
{
    qDebug() << "QgisMapWidget: 测距按钮被点击";

    if (m_measureMode)
    {
        // 关闭测距模式
        m_measureMode = false;
        m_measurePoints.clear();
        m_totalDistance = 0.0;
        m_hasStartPoint = false;

        // 清除地图上的所有测距元素
        clearMeasureLines();
        clearDynamicLine();

        // 重置按钮样式 - 处理两个可能的按钮
        if (m_measureBtn)
        {
            m_measureBtn->setText("📏 测距");
            m_measureBtn->setStyleSheet("");
        }
        if (m_topRightMeasureBtn)
        {
            m_topRightMeasureBtn->setToolTip("测量工具");
            m_topRightMeasureBtn->setStyleSheet(
                "QPushButton { "
                "background-color: rgba(52, 152, 219, 200); "
                "color: white; "
                "border: none; "
                "border-radius: 6px; "
                "font-size: 16px; "
                "font-weight: bold; "
                "} "
                "QPushButton:hover { "
                "background-color: rgba(41, 128, 185, 220); "
                "transform: scale(1.05); "
                "} "
                "QPushButton:pressed { "
                "background-color: rgba(31, 97, 141, 240); "
                "transform: scale(0.95); "
                "}");
        }

        qDebug() << "QgisMapWidget: 测距模式已关闭";
    }
    else
    {
        // 开启测距模式
        m_measureMode = true;
        m_measurePoints.clear();
        m_totalDistance = 0.0;
        m_hasStartPoint = false;

        // 更新按钮样式表示激活状态 - 处理两个可能的按钮
        if (m_measureBtn)
        {
            m_measureBtn->setText("📏 测距中");
            m_measureBtn->setStyleSheet("background-color: #4CAF50; color: white;");
        }
        if (m_topRightMeasureBtn)
        {
            m_topRightMeasureBtn->setToolTip("测量中... (点击停止)");
            m_topRightMeasureBtn->setStyleSheet("background-color: #4CAF50; color: white; border-radius: 6px;");
        }

        qDebug() << "QgisMapWidget: 测距模式已开启，点击地图设置起始点";
    }

    qDebug() << "QgisMapWidget: 测距工具状态，模式:" << m_measureMode;
}

void QgisMapWidget::showLocationDialog()
{
    // 创建定位对话框
    QDialog *locationDialog = new QDialog(this);
    locationDialog->setWindowTitle("经纬度定位");
    locationDialog->setFixedSize(400, 250);
    locationDialog->setModal(true);
    locationDialog->setWindowFlags(Qt::Dialog | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    locationDialog->setWindowIcon(QIcon()); // 移除窗口图标

    // 设置对话框样式
    locationDialog->setStyleSheet(
        "QDialog { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(255, 255, 255, 255), stop:1 rgba(248, 250, 252, 255)); "
        "border: 2px solid rgba(70, 130, 180, 150); "
        "border-radius: 12px; "
        "} "
        "QLabel { "
        "color: #2c3e50; "
        "font-size: 13px; "
        "font-weight: 600; "
        "} "
        "QLabel#titleLabel { "
        "color: #34495e; "
        "font-size: 16px; "
        "font-weight: bold; "
        "margin-bottom: 10px; "
        "} "
        "QLineEdit { "
        "border: 2px solid #e0e6ed; "
        "border-radius: 6px; "
        "padding: 10px 12px; "
        "font-size: 13px; "
        "background-color: white; "
        "selection-background-color: rgba(52, 152, 219, 100); "
        "} "
        "QLineEdit:focus { "
        "border: 2px solid rgba(52, 152, 219, 200); "
        "background-color: rgba(52, 152, 219, 10); "
        "} "
        "QLineEdit:hover { "
        "border: 2px solid rgba(52, 152, 219, 100); "
        "} "
        "QPushButton { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(52, 152, 219, 255), stop:1 rgba(41, 128, 185, 255)); "
        "color: white; "
        "border: none; "
        "border-radius: 8px; "
        "padding: 12px 24px; "
        "font-size: 13px; "
        "font-weight: 600; "
        "min-width: 80px; "
        "} "
        "QPushButton:hover { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(41, 128, 185, 255), stop:1 rgba(31, 97, 141, 255)); "
        "} "
        "QPushButton:pressed { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(31, 97, 141, 255), stop:1 rgba(21, 67, 96, 255)); "
        "} "
        "QPushButton#cancelBtn { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(149, 165, 166, 255), stop:1 rgba(127, 140, 141, 255)); "
        "} "
        "QPushButton#cancelBtn:hover { "
        "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "stop:0 rgba(127, 140, 141, 255), stop:1 rgba(108, 122, 137, 255)); "
        "}");

    // 创建布局
    QVBoxLayout *mainLayout = new QVBoxLayout(locationDialog);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // 添加标题
    QLabel *titleLabel = new QLabel("📍 请输入要定位的经纬度坐标", locationDialog);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // 创建输入区域
    QVBoxLayout *inputMainLayout = new QVBoxLayout();
    inputMainLayout->setSpacing(12);

    // 纬度输入区域
    QHBoxLayout *latLayout = new QHBoxLayout();
    QLabel *latLabel = new QLabel("🌐 纬度:", locationDialog);
    latLabel->setMinimumWidth(60);
    QLineEdit *latEdit = new QLineEdit(locationDialog);
    latEdit->setPlaceholderText("例: 39.9042 (北京)");
    latEdit->setValidator(new QDoubleValidator(-90.0, 90.0, 6, latEdit));
    latLayout->addWidget(latLabel);
    latLayout->addWidget(latEdit);

    // 经度输入区域
    QHBoxLayout *lngLayout = new QHBoxLayout();
    QLabel *lngLabel = new QLabel("🗺️ 经度:", locationDialog);
    lngLabel->setMinimumWidth(60);
    QLineEdit *lngEdit = new QLineEdit(locationDialog);
    lngEdit->setPlaceholderText("例: 116.4074 (北京)");
    lngEdit->setValidator(new QDoubleValidator(-180.0, 180.0, 6, lngEdit));
    lngLayout->addWidget(lngLabel);
    lngLayout->addWidget(lngEdit);

    inputMainLayout->addLayout(latLayout);
    inputMainLayout->addLayout(lngLayout);

    mainLayout->addLayout(inputMainLayout);

    // 创建按钮区域
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(12);
    QPushButton *locateBtn = new QPushButton("🎯 定位", locationDialog);
    QPushButton *cancelBtn = new QPushButton("❌ 取消", locationDialog);
    cancelBtn->setObjectName("cancelBtn");

    buttonLayout->addStretch();
    buttonLayout->addWidget(locateBtn);
    buttonLayout->addWidget(cancelBtn);

    mainLayout->addLayout(buttonLayout);

    // 连接按钮信号
    connect(locateBtn, &QPushButton::clicked, [this, locationDialog, latEdit, lngEdit]()
            {
        QString latText = latEdit->text().trimmed();
        QString lngText = lngEdit->text().trimmed();

        if (latText.isEmpty() || lngText.isEmpty()) {
            QMessageBox::warning(locationDialog, "输入错误", "请输入完整的经纬度坐标！");
            return;
        }

        bool latOk, lngOk;
        double latitude = latText.toDouble(&latOk);
        double longitude = lngText.toDouble(&lngOk);

        if (!latOk || !lngOk) {
            QMessageBox::warning(locationDialog, "输入错误", "请输入有效的数字格式！");
            return;
        }

        if (latitude < -90.0 || latitude > 90.0) {
            QMessageBox::warning(locationDialog, "输入错误", "纬度范围应在 -90 到 90 之间！");
            return;
        }

        if (longitude < -180.0 || longitude > 180.0) {
            QMessageBox::warning(locationDialog, "输入错误", "经度范围应在 -180 到 180 之间！");
            return;
        }

        // 执行定位
        locateToCoordinate(latitude, longitude);
        locationDialog->accept(); });

    connect(cancelBtn, &QPushButton::clicked, locationDialog, &QDialog::reject);

    // 显示对话框
    locationDialog->exec();
    locationDialog->deleteLater();
}

void QgisMapWidget::locateToCoordinate(double latitude, double longitude)
{
    qDebug() << "QgisMapWidget: 定位到坐标" << latitude << "," << longitude;

    // 移除之前的定位标记
    if (m_locationMarker)
    {
        m_locationMarker->deleteLater();
        m_locationMarker = nullptr;
    }

#ifdef QGIS_ENABLED
    // QGIS版本的定位实现
    if (m_mapCanvas)
    {
        // 创建点几何
        QgsPointXY point(longitude, latitude);

        // 设置地图中心
        m_mapCanvas->setCenter(point);
        m_mapCanvas->refresh();

        // 添加定位标记
        addLocationMarker(latitude, longitude);

        qDebug() << "QgisMapWidget: QGIS地图已定位到" << latitude << "," << longitude;
    }
#else
    // 瓦片地图版本的定位实现
    if (m_tileMapView)
    {
        // 设置地图中心
        m_tileMapView->setCenter(latitude, longitude);

        // 添加定位标记
        addLocationMarker(latitude, longitude);

        qDebug() << "QgisMapWidget: 瓦片地图已定位到" << latitude << "," << longitude;
    }
#endif

    // 更新地图信息面板
    updateMapInfoPanel();
}

void QgisMapWidget::addLocationMarker(double latitude, double longitude)
{
    // 创建定位标记
    m_locationMarker = new QWidget(m_mapContainer);
    m_locationMarker->setFixedSize(30, 30);

    // 设置标记样式
    m_locationMarker->setStyleSheet(
        "QWidget { "
        "background-color: rgba(255, 0, 0, 200); "
        "border: 3px solid white; "
        "border-radius: 15px; "
        "} "
        "QWidget:hover { "
        "background-color: rgba(255, 0, 0, 255); "
        "}");

    // 设置工具提示
    m_locationMarker->setToolTip(QString("定位点\n纬度: %1\n经度: %2")
                                     .arg(latitude, 0, 'f', 6)
                                     .arg(longitude, 0, 'f', 6));

#ifdef QGIS_ENABLED
    // QGIS版本的标记定位
    if (m_mapCanvas)
    {
        // 将经纬度坐标转换为屏幕坐标
        QgsPointXY point(longitude, latitude);
        QPoint screenPoint = m_mapCanvas->mapToPixel(point);

        // 调整标记位置（居中）
        int x = screenPoint.x() - m_locationMarker->width() / 2;
        int y = screenPoint.y() - m_locationMarker->height() / 2;

        m_locationMarker->move(x, y);
    }
#else
    // 瓦片地图版本的标记定位
    if (m_tileMapView)
    {
        // 将经纬度坐标转换为屏幕坐标
        QPointF screenPointF = m_tileMapView->latLngToViewPixel(latitude, longitude);
        QPoint screenPoint = screenPointF.toPoint();

        // 调整标记位置（居中）
        int x = screenPoint.x() - m_locationMarker->width() / 2;
        int y = screenPoint.y() - m_locationMarker->height() / 2;

        m_locationMarker->move(x, y);
    }
#endif

    // 显示标记
    m_locationMarker->show();
    m_locationMarker->raise();

    qDebug() << "QgisMapWidget: 定位标记已添加到" << latitude << "," << longitude;
}

void QgisMapWidget::onMapClicked(double lat, double lng)
{
    qDebug() << "QgisMapWidget: 地图被点击，坐标:" << lat << "," << lng;
    qDebug() << "QgisMapWidget: 测距模式:" << m_measureMode;

    // 发射地图点击信号
    emit mapClicked(lng, lat);

    // 如果处于测距模式，处理测量点
    if (m_measureMode)
    {
        if (!m_hasStartPoint)
        {
            // 设置起始点
            qDebug() << "QgisMapWidget: 设置起始点" << lat << "," << lng;
            m_measurePoints.clear();
            m_measurePoints.append(qMakePair(lat, lng));
            m_hasStartPoint = true;

            // 绘制起始点标记
            drawStartPoint(lat, lng);
        }
        else
        {
            // 设置结束点并完成测量
            qDebug() << "QgisMapWidget: 设置结束点" << lat << "," << lng;
            m_measurePoints.append(qMakePair(lat, lng));

            // 清除动态线条
            clearDynamicLine();

            // 计算距离并绘制最终线条
            calculateDistanceAndDrawLine();

            // 重置状态，准备下一次测量
            m_measurePoints.clear();
            m_hasStartPoint = false;
        }
    }

    qDebug() << "QgisMapWidget: 地图点击" << lat << "," << lng;
}

void QgisMapWidget::showBAreaLogs()
{
    qDebug() << "QgisMapWidget: 显示B区域log标记";

    // B区域log位置信息
    m_bAreaLogPositions = {
        {0.0, 143.5}, // (143.5E, 0)
        {0.0, 83.7},  // (83.7E, 0)
        {0.0, -98.0}  // (98W, 0)
    };

    // SVG图标内容
    QString svgContent = R"(<svg t="1753235325006" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7820" width="32" height="32"><path d="M34.463147 341.957744a59.582005 59.582005 0 0 0 81.449321 0L334.585628 123.490393a59.582005 59.582005 0 0 0 0-81.44932l-8.592569-8.798379a59.582005 59.582005 0 0 0-81.44932 0L25.870578 252.121664a59.68491 59.68491 0 0 0 0 81.449321zM47.532084 273.37155l218.673161-218.67316c12.863127-12.863127 29.996812-12.863127 38.589381 0l8.541116 8.541116c12.863127 12.863127 12.863127 30.048265 0 38.589381l-218.827518 218.673161c-8.592569 8.592569-25.726254 8.592569-38.589381 0L47.532084 311.960931c-8.798379-12.863127-8.798379-30.048265 0-38.589381z m634.563785 394.434929l-38.589381-42.859939 107.227028-107.227028-90.04189-90.041889 42.859939-42.85994-90.041889-90.041889 47.18195-47.18195 90.04189 90.041889L870.772217 217.597031a59.582005 59.582005 0 0 0 0-81.44932L750.527706 16.057556a59.736362 59.736362 0 0 0-81.500774 0l-120.038702 120.090155L639.23593 226.39541l-47.18195 47.130498-90.04189-90.04189L459.152151 226.39541 377.49702 149.216648 141.638722 380.752935l81.500773 81.243511-42.911392 42.859939 90.04189 90.04189-47.18195 47.18195-90.04189-90.04189-115.768144 115.768144a59.736362 59.736362 0 0 0 0 81.500773L137.368164 869.551764a59.582005 59.582005 0 0 0 81.44932 0l115.768144-115.768144-90.041889-90.041889 47.18195-47.18195L381.973388 706.39586l42.65413-42.654129 90.041889 90.041889 107.175575-107.227027 38.589381 38.589381 21.455696-17.133685zM587.783422 140.418269l98.428648-98.583006c12.863127-12.863127 29.996812-12.863127 38.589381 0L845.045963 161.873965c12.863127 12.863127 12.863127 29.996812 0 38.589381l-98.840268 98.583006zM291.725689 753.57781l-94.363901 94.312448c-12.863127 12.863127-29.996812 12.863127-38.537928 0L38.733705 727.851556c-12.863127-12.863127-12.863127-29.996812 0-38.589381l94.312448-94.3639 158.679536 158.679535z m205.810033-523.117652l158.628083 158.628083-21.404243 21.455696L476.080026 252.121664zM381.973388 663.741731L223.139495 504.856385 244.543739 483.657952l158.628083 158.628083L381.973388 663.741731z m132.901829 47.130498l-68.792003-68.792004-201.539475-201.488023L184.498661 380.752935l188.676348-188.882158L433.425897 252.121664l201.539475 201.488023 68.586193 68.637646z m390.164371 0a19.500501 19.500501 0 0 0-21.404243 0l-180.08378 180.083779a19.500501 19.500501 0 0 0 0 21.404243 13.223295 13.223295 0 0 0 8.541117 4.270558s8.592569 0 8.592569-4.270558l180.083779-180.083779c12.657317-4.424916 12.657317-17.082233 4.064748-21.610053z m-98.583006-42.85994a19.551953 19.551953 0 0 0-21.455696 0L664.962184 787.845181a19.551953 19.551953 0 0 0 0 21.455696 13.274747 13.274747 0 0 0 8.592569 4.270558s8.541116 0 8.541116-4.270558l119.884345-119.832892c8.541116-4.527821 8.541116-13.068937 4.270558-21.661506zM1016.331364 740.920493a19.294691 19.294691 0 0 0-21.404244 0l-257.262542 257.262542a19.294691 19.294691 0 0 0 0 21.404244 13.171842 13.171842 0 0 0 8.541117 4.32201s8.592569 0 8.592569-4.32201l257.262541-257.262542c13.068937-4.476368 13.068937-13.068937 4.270559-21.404244z" fill="#1296db" p-id="7821"></path></svg>)";

    for (int i = 0; i < m_bAreaLogPositions.size(); ++i)
    {
        double lat = m_bAreaLogPositions[i].first;
        double lng = m_bAreaLogPositions[i].second;

        // 创建log标记widget - 使用SVG图标
        QWidget *logMarker = new QWidget(m_mapContainer);
        logMarker->setFixedSize(32, 32);
        logMarker->setStyleSheet("QWidget { background: transparent; }");

        // 创建SVG图标标签
        QLabel *iconLabel = new QLabel(logMarker);
        iconLabel->setAlignment(Qt::AlignCenter);
        iconLabel->setGeometry(0, 0, 32, 32);

        // 加载SVG图标
        QString svgPath = ":/icons/satellite.svg";
        qDebug() << "QgisMapWidget: 尝试加载SVG图标:" << svgPath;

        // 检查资源是否存在
        QFile svgFile(svgPath);
        if (!svgFile.exists())
        {
            qDebug() << "QgisMapWidget: SVG资源文件不存在";
        }

        QSvgRenderer *svgRenderer = new QSvgRenderer(svgPath, logMarker);
        if (svgRenderer->isValid())
        {
            qDebug() << "QgisMapWidget: SVG图标加载成功";
            // 创建QPixmap并渲染SVG
            QPixmap svgPixmap(32, 32);
            svgPixmap.fill(Qt::transparent);
            QPainter painter(&svgPixmap);
            svgRenderer->render(&painter);
            iconLabel->setPixmap(svgPixmap);
        }
        else
        {
            qDebug() << "QgisMapWidget: SVG图标加载失败，使用备用图标";
            // 如果SVG加载失败，使用备用图标
            iconLabel->setText("🛰️");
            iconLabel->setStyleSheet("QLabel { background: transparent; color: #1296db; font-size: 24px; }");
        }

        // 设置工具提示显示位置信息
        QString posText;
        if (lng > 0)
        {
            posText = QString("B区域Log\n位置: %1°E, %2°").arg(lng).arg(lat);
        }
        else
        {
            posText = QString("B区域Log\n位置: %1°W, %2°").arg(-lng).arg(lat);
        }
        logMarker->setToolTip(posText);

        // 使用TileMapView的精确坐标转换
        if (m_tileMapView)
        {
            // 检查坐标是否在当前视野内
            bool isVisible = m_tileMapView->isCoordinateVisible(lat, lng);

            if (isVisible)
            {
                // 使用TileMapView的latLngToViewPixel方法进行视图坐标转换
                QPointF viewPos = m_tileMapView->latLngToViewPixel(lat, lng);

                // 调整位置，使标记中心对准坐标点
                int x = qRound(viewPos.x()) - 16; // 标记宽度32的一半
                int y = qRound(viewPos.y()) - 16; // 标记高度32的一半

                // 不限制标记位置，让它准确显示在地理坐标位置
                // 即使部分超出视图边界也没关系，这样能保证位置精确

                logMarker->move(x, y);
                logMarker->show();  // 显示标记
                logMarker->raise(); // 提升到最前面

                qDebug() << "QgisMapWidget: 显示标记" << i + 1 << "位置:" << x << "," << y
                         << "地理坐标:" << lat << "," << lng;
            }
            else
            {
                // 坐标不在视野内，隐藏标记
                logMarker->hide();
                qDebug() << "QgisMapWidget: 隐藏标记" << i + 1 << "地理坐标:" << lat << "," << lng;
            }
        }
        else
        {
            // 如果没有地图视图，隐藏标记
            logMarker->hide();
            qDebug() << "QgisMapWidget: 无地图视图，隐藏标记" << i + 1;
        }

        m_bAreaLogMarkers.append(logMarker);

        qDebug() << "QgisMapWidget: 创建B区域log标记" << i + 1 << "位置:" << posText;
    }
}

void QgisMapWidget::hideBAreaLogs()
{
    qDebug() << "QgisMapWidget: 隐藏B区域log标记";

    // 删除所有B区域log标记
    for (QWidget *marker : m_bAreaLogMarkers)
    {
        if (marker)
        {
            marker->hide();
            marker->deleteLater();
        }
    }
    m_bAreaLogMarkers.clear();
    m_bAreaLogPositions.clear();
}

void QgisMapWidget::updateBAreaLogPositions()
{
    // 只有当B区域log标记显示时才更新位置
    if (m_bAreaLogMarkers.isEmpty() || m_bAreaLogPositions.isEmpty())
        return;

    qDebug() << "QgisMapWidget: 更新B区域log标记位置";

    for (int i = 0; i < m_bAreaLogMarkers.size() && i < m_bAreaLogPositions.size(); ++i)
    {
        QWidget *logMarker = m_bAreaLogMarkers[i];
        if (!logMarker)
            continue;

        double lat = m_bAreaLogPositions[i].first;
        double lng = m_bAreaLogPositions[i].second;

        // 使用TileMapView的视图坐标转换
        if (m_tileMapView)
        {
            // 检查坐标是否在当前视野内
            bool isVisible = m_tileMapView->isCoordinateVisible(lat, lng);

            if (isVisible)
            {
                // 使用新的latLngToViewPixel方法进行视图坐标转换
                QPointF viewPos = m_tileMapView->latLngToViewPixel(lat, lng);

                // 调整位置，使标记中心对准坐标点
                int x = qRound(viewPos.x()) - 16; // 标记宽度32的一半
                int y = qRound(viewPos.y()) - 16; // 标记高度32的一半

                // 不限制标记位置，让它准确显示在地理坐标位置
                // 即使部分超出视图边界也没关系，这样能保证位置精确

                logMarker->move(x, y);
                logMarker->show(); // 显示标记

                qDebug() << "QgisMapWidget: 更新标记" << i + 1 << "位置:" << x << "," << y
                         << "地理坐标:" << lat << "," << lng;
            }
            else
            {
                // 坐标不在视野内，隐藏标记
                logMarker->hide();
                qDebug() << "QgisMapWidget: 隐藏标记" << i + 1 << "地理坐标:" << lat << "," << lng;
            }
        }
    }
}

void QgisMapWidget::calculateDistanceAndDrawLine()
{
    if (m_measurePoints.size() < 2)
        return;

    // 使用Haversine公式计算两点间距离
    double lat1 = m_measurePoints[m_measurePoints.size() - 2].first;
    double lng1 = m_measurePoints[m_measurePoints.size() - 2].second;
    double lat2 = m_measurePoints[m_measurePoints.size() - 1].first;
    double lng2 = m_measurePoints[m_measurePoints.size() - 1].second;

    double distance = calculateHaversineDistance(lat1, lng1, lat2, lng2);

    qDebug() << "QgisMapWidget: 计算距离" << distance << "米，从(" << lat1 << "," << lng1 << ")到(" << lat2 << "," << lng2 << ")";

    // 在地图上绘制测距线
    drawMeasureLine(lat1, lng1, lat2, lng2, distance);
}

double QgisMapWidget::calculateHaversineDistance(double lat1, double lng1, double lat2, double lng2)
{
    const double R = 6371000; // 地球半径（米）

    double dLat = qDegreesToRadians(lat2 - lat1);
    double dLng = qDegreesToRadians(lng2 - lng1);

    double a = qSin(dLat / 2) * qSin(dLat / 2) +
               qCos(qDegreesToRadians(lat1)) * qCos(qDegreesToRadians(lat2)) *
                   qSin(dLng / 2) * qSin(dLng / 2);
    double c = 2 * qAtan2(qSqrt(a), qSqrt(1 - a));

    return R * c;
}

void QgisMapWidget::drawMeasureLine(double lat1, double lng1, double lat2, double lng2, double distance)
{
    // 创建一个自定义widget来绘制测距线
    QWidget *lineWidget = new QWidget(m_mapContainer);
    lineWidget->setAttribute(Qt::WA_TransparentForMouseEvents); // 让鼠标事件穿透
    lineWidget->setStyleSheet("background-color: transparent;");

    // 将地理坐标转换为屏幕坐标
    QPointF startPixel = m_tileMapView->latLngToViewPixel(lat1, lng1);
    QPointF endPixel = m_tileMapView->latLngToViewPixel(lat2, lng2);

    // 计算线条的边界矩形，根据缩放级别调整边距
    int marginX = qMax(50, qMin(100, m_tileMapView->getZoomLevel() * 3 + 20)); // 留出文字空间
    int marginY = qMax(20, qMin(40, m_tileMapView->getZoomLevel() + 10));
    int minX = qMin(startPixel.x(), endPixel.x()) - marginX;
    int maxX = qMax(startPixel.x(), endPixel.x()) + marginX;
    int minY = qMin(startPixel.y(), endPixel.y()) - marginY;
    int maxY = qMax(startPixel.y(), endPixel.y()) + marginY;

    lineWidget->setGeometry(minX, minY, maxX - minX, maxY - minY);

    // 调整相对坐标
    QPointF relativeStart(startPixel.x() - minX, startPixel.y() - minY);
    QPointF relativeEnd(endPixel.x() - minX, endPixel.y() - minY);

    // 创建自定义绘制类
    class MeasureLineWidget : public QWidget
    {
    public:
        QPointF start, end;
        QString distanceText;
        // 添加地理坐标信息，用于地图移动时重新计算位置
        double lat1, lng1, lat2, lng2;

        MeasureLineWidget(QWidget *parent = nullptr) : QWidget(parent) {}

        void paintEvent(QPaintEvent *) override
        {
            QPainter painter(this);
            painter.setRenderHint(QPainter::Antialiasing);

            // 优先使用更新的坐标属性，确保缩放后坐标正确
            QPointF drawStart, drawEnd;
            bool hasUpdatedCoords = false;

            if (property("startX").isValid() && property("startY").isValid() &&
                property("endX").isValid() && property("endY").isValid())
            {
                drawStart = QPointF(property("startX").toDouble(), property("startY").toDouble());
                drawEnd = QPointF(property("endX").toDouble(), property("endY").toDouble());
                hasUpdatedCoords = true;
            }
            else
            {
                // 只有在没有属性时才使用初始坐标（这种情况不应该发生）
                drawStart = start;
                drawEnd = end;
                qWarning() << "QgisMapWidget: 测距线缺少坐标属性，使用初始坐标";
            }

            qDebug() << "QgisMapWidget: 测距线绘制坐标 - 使用更新坐标:" << hasUpdatedCoords
                     << "起点:" << drawStart << "终点:" << drawEnd;

            // 绘制红色线条，根据缩放级别调整粗细
            int zoomLevel = 10; // 默认缩放级别
            // 优先使用属性中保存的缩放级别
            if (property("currentZoomLevel").isValid())
            {
                zoomLevel = property("currentZoomLevel").toInt();
            }
            else if (parent() && parent()->parent())
            {
                QgisMapWidget *mapWidget = static_cast<QgisMapWidget *>(parent()->parent());
                if (mapWidget->getTileMapView())
                {
                    zoomLevel = mapWidget->getTileMapView()->getZoomLevel();
                }
            }
            // 优化线条粗细计算，避免在高缩放级别下过粗
            int lineWidth = qMax(1, qMin(3, (zoomLevel - 5) / 4 + 2)); // 更合理的线条粗细
            qDebug() << "QgisMapWidget: 测距线绘制 - 缩放级别:" << zoomLevel << "线条粗细:" << lineWidth;
            QPen linePen(Qt::red, lineWidth);
            painter.setPen(linePen);
            painter.drawLine(drawStart, drawEnd);

            // 绘制起点和终点圆圈，根据缩放级别调整大小
            painter.setBrush(Qt::red);
            // 优化圆圈大小计算，避免在高缩放级别下过大
            int circleSize = qMax(2, qMin(4, (zoomLevel - 5) / 4 + 3)); // 更合理的圆圈大小
            painter.drawEllipse(drawStart, circleSize, circleSize);
            painter.drawEllipse(drawEnd, circleSize, circleSize);

            // 计算文字位置（线条中点）
            QPointF textPos = (drawStart + drawEnd) / 2;

            // 绘制距离文字背景，根据缩放级别调整字体大小
            QFont font = painter.font();
            font.setBold(true);
            int fontSize = qMax(8, qMin(14, zoomLevel / 2 + 6)); // 缩放级别越高字体越大
            font.setPointSize(fontSize);
            painter.setFont(font);

            QFontMetrics fm(font);
            QRect textRect = fm.boundingRect(distanceText);
            textRect.moveCenter(textPos.toPoint());
            textRect.adjust(-4, -2, 4, 2);

            // 绘制白色背景
            painter.setPen(Qt::NoPen);
            painter.setBrush(QColor(255, 255, 255, 200));
            painter.drawRoundedRect(textRect, 3, 3);

            // 绘制黑色文字
            painter.setPen(Qt::black);
            painter.drawText(textRect, Qt::AlignCenter, distanceText);
        }
    };

    MeasureLineWidget *measureWidget = new MeasureLineWidget(lineWidget);
    measureWidget->start = relativeStart;
    measureWidget->end = relativeEnd;
    // 保存地理坐标，用于地图移动时重新计算位置
    measureWidget->lat1 = lat1;
    measureWidget->lng1 = lng1;
    measureWidget->lat2 = lat2;
    measureWidget->lng2 = lng2;

    // 设置对象名和属性，便于后续识别和更新
    measureWidget->setObjectName("MeasureLineWidget");
    measureWidget->setProperty("lat1", lat1);
    measureWidget->setProperty("lng1", lng1);
    measureWidget->setProperty("lat2", lat2);
    measureWidget->setProperty("lng2", lng2);
    // 设置初始缩放级别
    measureWidget->setProperty("currentZoomLevel", m_tileMapView->getZoomLevel());
    // 立即设置相对坐标属性，确保paintEvent能正确使用
    measureWidget->setProperty("startX", relativeStart.x());
    measureWidget->setProperty("startY", relativeStart.y());
    measureWidget->setProperty("endX", relativeEnd.x());
    measureWidget->setProperty("endY", relativeEnd.y());

    qDebug() << "QgisMapWidget: 创建测距线 - 地理坐标:"
             << "起点(" << lat1 << "," << lng1 << ")"
             << "终点(" << lat2 << "," << lng2 << ")"
             << "相对坐标: 起点" << relativeStart << "终点" << relativeEnd;

    // 格式化距离文字
    if (distance >= 1000)
    {
        measureWidget->distanceText = QString("%1 km").arg(QString::number(distance / 1000, 'f', 2));
    }
    else
    {
        measureWidget->distanceText = QString("%1 m").arg(QString::number(distance, 'f', 0));
    }

    measureWidget->setGeometry(0, 0, lineWidget->width(), lineWidget->height());
    measureWidget->show();

    lineWidget->show();
    lineWidget->raise();

    // 保存到列表中，以便后续清除
    m_measureLines.append(lineWidget);

    qDebug() << "QgisMapWidget: 绘制测距线完成，距离:" << measureWidget->distanceText;
}

void QgisMapWidget::clearMeasureLines()
{
    // 清除所有测距线条
    for (QWidget *lineWidget : m_measureLines)
    {
        if (lineWidget)
        {
            lineWidget->deleteLater();
        }
    }
    m_measureLines.clear();

    // 清除起始点标记
    if (m_startPointWidget)
    {
        m_startPointWidget->deleteLater();
        m_startPointWidget = nullptr;
    }

    qDebug() << "QgisMapWidget: 已清除所有测距线条";
}

void QgisMapWidget::updateMeasureLinePositions()
{
    qDebug() << "QgisMapWidget: 开始更新测距线位置，当前缩放级别:" << (m_tileMapView ? m_tileMapView->getZoomLevel() : -1);
    // 更新所有测距线的位置
    for (QWidget *lineWidget : m_measureLines)
    {
        if (!lineWidget)
            continue;

        // 查找MeasureLineWidget子组件
        QList<QWidget *> children = lineWidget->findChildren<QWidget *>();
        for (QWidget *child : children)
        {
            // 检查是否是MeasureLineWidget（通过对象名识别）
            if (child->objectName() == "MeasureLineWidget")
            {
                // 获取保存的地理坐标
                double lat1 = child->property("lat1").toDouble();
                double lng1 = child->property("lng1").toDouble();
                double lat2 = child->property("lat2").toDouble();
                double lng2 = child->property("lng2").toDouble();

                qDebug() << "QgisMapWidget: 更新测距线 - 地理坐标:"
                         << "起点(" << lat1 << "," << lng1 << ")"
                         << "终点(" << lat2 << "," << lng2 << ")";

                // 重新计算屏幕坐标
                QPointF startPixel = m_tileMapView->latLngToViewPixel(lat1, lng1);
                QPointF endPixel = m_tileMapView->latLngToViewPixel(lat2, lng2);

                qDebug() << "QgisMapWidget: 更新测距线 - 屏幕坐标:"
                         << "起点" << startPixel << "终点" << endPixel;

                // 计算包围矩形，根据缩放级别调整边距
                int marginX = qMax(50, qMin(100, m_tileMapView->getZoomLevel() * 3 + 20));
                int marginY = qMax(30, qMin(50, m_tileMapView->getZoomLevel() + 15));
                double minX = qMin(startPixel.x(), endPixel.x()) - marginX;
                double maxX = qMax(startPixel.x(), endPixel.x()) + marginX;
                double minY = qMin(startPixel.y(), endPixel.y()) - marginY;
                double maxY = qMax(startPixel.y(), endPixel.y()) + marginY;

                // 更新lineWidget的位置和大小
                lineWidget->setGeometry(minX, minY, maxX - minX, maxY - minY);

                // 更新相对坐标
                QPointF relativeStart(startPixel.x() - minX, startPixel.y() - minY);
                QPointF relativeEnd(endPixel.x() - minX, endPixel.y() - minY);

                // 更新坐标属性
                child->setProperty("startX", relativeStart.x());
                child->setProperty("startY", relativeStart.y());
                child->setProperty("endX", relativeEnd.x());
                child->setProperty("endY", relativeEnd.y());

                // 更新当前缩放级别属性
                int currentZoom = m_tileMapView->getZoomLevel();
                child->setProperty("currentZoomLevel", currentZoom);
                qDebug() << "QgisMapWidget: 更新测距线缩放级别到:" << currentZoom;

                // 触发重绘
                child->update();
                lineWidget->update(); // 同时更新父widget
                break;
            }
        }
    }
}

void QgisMapWidget::drawStartPoint(double lat, double lng)
{
    // 清除之前的起始点
    if (m_startPointWidget)
    {
        m_startPointWidget->deleteLater();
        m_startPointWidget = nullptr;
    }

    // 创建起始点标记widget
    m_startPointWidget = new QWidget(m_mapContainer);
    m_startPointWidget->setAttribute(Qt::WA_TransparentForMouseEvents);
    m_startPointWidget->setStyleSheet("background-color: transparent;");

    // 转换坐标
    QPointF startPixel = m_tileMapView->latLngToViewPixel(lat, lng);

    // 设置widget大小和位置
    int size = 20;
    m_startPointWidget->setGeometry(startPixel.x() - size / 2, startPixel.y() - size / 2, size, size);

    // 创建自定义绘制类
    class StartPointWidget : public QWidget
    {
    public:
        StartPointWidget(QWidget *parent = nullptr) : QWidget(parent) {}

        void paintEvent(QPaintEvent *) override
        {
            QPainter painter(this);
            painter.setRenderHint(QPainter::Antialiasing);

            // 绘制红色圆圈
            painter.setPen(QPen(Qt::red, 2));
            painter.setBrush(Qt::red);
            painter.drawEllipse(rect().center(), 6, 6);

            // 绘制外圈
            painter.setBrush(Qt::NoBrush);
            painter.drawEllipse(rect().center(), 8, 8);
        }
    };

    StartPointWidget *pointWidget = new StartPointWidget(m_startPointWidget);
    pointWidget->setGeometry(0, 0, size, size);
    pointWidget->show();

    m_startPointWidget->show();
    m_startPointWidget->raise();

    qDebug() << "QgisMapWidget: 绘制起始点完成";
}

void QgisMapWidget::updateDynamicLine(double lat, double lng)
{
    if (m_measurePoints.isEmpty())
        return;

    // 获取起始点坐标
    double startLat = m_measurePoints[0].first;
    double startLng = m_measurePoints[0].second;

    // 清除之前的动态线条
    clearDynamicLine();

    // 创建动态线条widget
    m_dynamicLineWidget = new QWidget(m_mapContainer);
    m_dynamicLineWidget->setAttribute(Qt::WA_TransparentForMouseEvents);
    m_dynamicLineWidget->setStyleSheet("background-color: transparent;");

    // 转换坐标
    QPointF startPixel = m_tileMapView->latLngToViewPixel(startLat, startLng);
    QPointF endPixel = m_tileMapView->latLngToViewPixel(lat, lng);

    // 计算边界矩形，根据缩放级别调整边距
    int marginX = qMax(50, qMin(100, m_tileMapView->getZoomLevel() * 3 + 20));
    int marginY = qMax(20, qMin(40, m_tileMapView->getZoomLevel() + 10));
    int minX = qMin(startPixel.x(), endPixel.x()) - marginX;
    int maxX = qMax(startPixel.x(), endPixel.x()) + marginX;
    int minY = qMin(startPixel.y(), endPixel.y()) - marginY;
    int maxY = qMax(startPixel.y(), endPixel.y()) + marginY;

    m_dynamicLineWidget->setGeometry(minX, minY, maxX - minX, maxY - minY);

    // 调整相对坐标
    QPointF relativeStart(startPixel.x() - minX, startPixel.y() - minY);
    QPointF relativeEnd(endPixel.x() - minX, endPixel.y() - minY);

    // 计算当前距离
    double distance = calculateHaversineDistance(startLat, startLng, lat, lng);

    // 创建动态线条绘制类
    class DynamicLineWidget : public QWidget
    {
    public:
        QPointF start, end;
        QString distanceText;
        QgisMapWidget *mapWidget;

        DynamicLineWidget(QWidget *parent = nullptr) : QWidget(parent), mapWidget(nullptr) {}

        void paintEvent(QPaintEvent *) override
        {
            QPainter painter(this);
            painter.setRenderHint(QPainter::Antialiasing);

            // 获取当前缩放级别
            int zoomLevel = 10; // 默认缩放级别
            if (mapWidget && mapWidget->getTileMapView())
            {
                zoomLevel = mapWidget->getTileMapView()->getZoomLevel();
            }

            // 绘制虚线（表示正在测量），根据缩放级别调整粗细
            int lineWidth = qMax(1, qMin(3, (zoomLevel - 5) / 4 + 2));
            QPen linePen(Qt::red, lineWidth, Qt::DashLine);
            painter.setPen(linePen);
            painter.drawLine(start, end);

            // 绘制终点圆圈（半透明），根据缩放级别调整大小
            int circleSize = qMax(2, qMin(4, (zoomLevel - 5) / 4 + 3));
            painter.setPen(QPen(Qt::red, 1));
            painter.setBrush(QColor(255, 0, 0, 100));
            painter.drawEllipse(end, circleSize, circleSize);

            // 绘制距离文字，根据缩放级别调整字体大小
            QPointF textPos = (start + end) / 2;

            QFont font = painter.font();
            font.setBold(true);
            int fontSize = qMax(6, qMin(9, (zoomLevel - 5) / 3 + 7));
            font.setPointSize(fontSize);
            painter.setFont(font);

            QFontMetrics fm(font);
            QRect textRect = fm.boundingRect(distanceText);
            textRect.moveCenter(textPos.toPoint());
            textRect.adjust(-3, -1, 3, 1);

            // 绘制半透明背景
            painter.setPen(Qt::NoPen);
            painter.setBrush(QColor(255, 255, 255, 180));
            painter.drawRoundedRect(textRect, 2, 2);

            // 绘制文字
            painter.setPen(Qt::darkRed);
            painter.drawText(textRect, Qt::AlignCenter, distanceText);
        }
    };

    DynamicLineWidget *lineWidget = new DynamicLineWidget(m_dynamicLineWidget);
    lineWidget->start = relativeStart;
    lineWidget->end = relativeEnd;
    lineWidget->mapWidget = this; // 设置地图组件指针

    // 格式化距离文字
    if (distance >= 1000)
    {
        lineWidget->distanceText = QString("%1 km").arg(QString::number(distance / 1000, 'f', 2));
    }
    else
    {
        lineWidget->distanceText = QString("%1 m").arg(QString::number(distance, 'f', 0));
    }

    lineWidget->setGeometry(0, 0, m_dynamicLineWidget->width(), m_dynamicLineWidget->height());
    lineWidget->show();

    m_dynamicLineWidget->show();
    m_dynamicLineWidget->raise();
}

void QgisMapWidget::clearDynamicLine()
{
    if (m_dynamicLineWidget)
    {
        m_dynamicLineWidget->deleteLater();
        m_dynamicLineWidget = nullptr;
    }
}

// 目标图标管理方法实现

QPixmap QgisMapWidget::createTargetIcon(TargetType type, int size)
{
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 设置紫色主题
    QColor fillColor(155, 89, 182, 200);   // 紫色，半透明
    QColor borderColor(155, 89, 182, 255); // 紫色边框

    // 绘制水滴形状背景 - 优化形状和比例
    QPainterPath dropPath;
    QPointF center(size / 2.0, size * 0.65); // 调整中心位置
    QPointF top(size / 2.0, size * 0.15);    // 调整顶部位置

    // 创建更标准的水滴形状
    dropPath.moveTo(top);
    // 右侧曲线
    dropPath.quadTo(QPointF(size * 0.75, size * 0.35), QPointF(size * 0.75, size * 0.6));
    dropPath.quadTo(QPointF(size * 0.75, size * 0.8), QPointF(size * 0.6, size * 0.9));
    dropPath.quadTo(QPointF(size * 0.55, size * 0.95), QPointF(size / 2.0, size * 0.95));
    // 左侧曲线
    dropPath.quadTo(QPointF(size * 0.45, size * 0.95), QPointF(size * 0.4, size * 0.9));
    dropPath.quadTo(QPointF(size * 0.25, size * 0.8), QPointF(size * 0.25, size * 0.6));
    dropPath.quadTo(QPointF(size * 0.25, size * 0.35), top);

    // 添加阴影效果
    painter.setPen(QPen(QColor(155, 89, 182, 100), 1));
    painter.setBrush(QBrush(QColor(155, 89, 182, 50)));
    painter.drawPath(dropPath.translated(1, 1)); // 阴影

    // 绘制主体
    painter.setPen(QPen(borderColor, 1.5));
    painter.setBrush(fillColor);
    painter.drawPath(dropPath);

    // 绘制内部图标 - 优化尺寸和位置
    painter.setPen(QPen(Qt::white, 1.5));
    painter.setBrush(Qt::white);

    // 调整图标区域，确保完全显示且有适当边距
    QRectF iconRect(size * 0.2, size * 0.25, size * 0.6, size * 0.4);

    switch (type)
    {
    case AIRCRAFT:
        // 绘制飞机图标 - 优化比例和形状
        {
            QPainterPath planePath;

            // 机身中心线
            double centerX = iconRect.center().x();
            double centerY = iconRect.center().y();
            double w = iconRect.width() * 0.8; // 缩小到80%
            double h = iconRect.height() * 0.8;

            // 飞机各部分坐标
            QPointF nose(centerX, centerY - h * 0.4);                 // 机头
            QPointF leftWing(centerX - w * 0.4, centerY);             // 左翼
            QPointF rightWing(centerX + w * 0.4, centerY);            // 右翼
            QPointF leftTail(centerX - w * 0.15, centerY + h * 0.3);  // 左尾翼
            QPointF rightTail(centerX + w * 0.15, centerY + h * 0.3); // 右尾翼
            QPointF tail(centerX, centerY + h * 0.4);                 // 尾部

            // 绘制飞机轮廓
            planePath.moveTo(nose);
            planePath.lineTo(leftWing);
            planePath.lineTo(leftTail);
            planePath.lineTo(tail);
            planePath.lineTo(rightTail);
            planePath.lineTo(rightWing);
            planePath.lineTo(nose);

            painter.fillPath(planePath, Qt::white);
            painter.drawPath(planePath);
        }
        break;

    case VEHICLE:
        // 绘制汽车图标 - 优化比例和细节
        {
            double centerX = iconRect.center().x();
            double centerY = iconRect.center().y();
            double w = iconRect.width() * 0.8;
            double h = iconRect.height() * 0.8;

            // 车身主体
            QRectF carBody(centerX - w * 0.4, centerY - h * 0.15, w * 0.8, h * 0.3);

            // 车顶
            QRectF carTop(centerX - w * 0.25, centerY - h * 0.4, w * 0.5, h * 0.35);

            // 绘制车身和车顶
            painter.fillRect(carBody, Qt::white);
            painter.drawRoundedRect(carBody, 1, 1);

            painter.fillRect(carTop, Qt::white);
            painter.drawRoundedRect(carTop, 1, 1);

            // 绘制车轮
            double wheelRadius = h * 0.08;
            QPointF leftWheel(centerX - w * 0.25, centerY + h * 0.25);
            QPointF rightWheel(centerX + w * 0.25, centerY + h * 0.25);

            painter.setBrush(Qt::white);
            painter.drawEllipse(leftWheel, wheelRadius, wheelRadius);
            painter.drawEllipse(rightWheel, wheelRadius, wheelRadius);

            // 绘制前后灯
            painter.setBrush(Qt::white);
            QRectF frontLight(centerX + w * 0.35, centerY - h * 0.05, w * 0.05, h * 0.1);
            QRectF rearLight(centerX - w * 0.4, centerY - h * 0.05, w * 0.05, h * 0.1);
            painter.fillRect(frontLight, Qt::white);
            painter.fillRect(rearLight, Qt::white);
        }
        break;

    case SHIP:
        // 绘制船舶图标 - 优化比例和细节
        {
            double centerX = iconRect.center().x();
            double centerY = iconRect.center().y();
            double w = iconRect.width() * 0.8;
            double h = iconRect.height() * 0.8;

            QPainterPath shipPath;

            // 船体轮廓 - 更像船的形状
            QPointF bow(centerX + w * 0.4, centerY);                   // 船头
            QPointF stern(centerX - w * 0.4, centerY);                 // 船尾
            QPointF bottomBow(centerX + w * 0.3, centerY + h * 0.3);   // 船头底部
            QPointF bottomStern(centerX - w * 0.3, centerY + h * 0.3); // 船尾底部
            QPointF keel(centerX, centerY + h * 0.4);                  // 龙骨

            // 绘制船体
            shipPath.moveTo(bow);
            shipPath.lineTo(QPointF(centerX + w * 0.1, centerY - h * 0.2)); // 船头上部
            shipPath.lineTo(QPointF(centerX - w * 0.1, centerY - h * 0.2)); // 船尾上部
            shipPath.lineTo(stern);
            shipPath.lineTo(bottomStern);
            shipPath.lineTo(keel);
            shipPath.lineTo(bottomBow);
            shipPath.lineTo(bow);

            painter.fillPath(shipPath, Qt::white);
            painter.drawPath(shipPath);

            // 绘制船舱
            QRectF cabin(centerX - w * 0.15, centerY - h * 0.35, w * 0.3, h * 0.2);
            painter.fillRect(cabin, Qt::white);
            painter.drawRect(cabin);

            // 绘制桅杆
            painter.drawLine(QPointF(centerX, centerY - h * 0.35),
                             QPointF(centerX, centerY - h * 0.5));
        }
        break;
    }

    return pixmap;
}

void QgisMapWidget::addTargetIcon(double lat, double lng, TargetType type, const QString &label)
{
    // 生成唯一ID
    QString iconId = QString("target_%1_%2_%3").arg(lat, 0, 'f', 6).arg(lng, 0, 'f', 6).arg(QDateTime::currentMSecsSinceEpoch());

    // 创建图标widget
    QWidget *iconWidget = new QWidget(m_mapContainer);
    iconWidget->setAttribute(Qt::WA_TransparentForMouseEvents, false); // 允许鼠标交互
    iconWidget->setStyleSheet("background-color: transparent;");

    // 转换坐标
    QPointF pixelPos = m_tileMapView->latLngToViewPixel(lat, lng);

    // 设置widget大小和位置
    int iconSize = 32;
    iconWidget->setGeometry(pixelPos.x() - iconSize / 2, pixelPos.y() - iconSize, iconSize, iconSize + 20);

    // 创建自定义绘制类
    class TargetIconWidget : public QWidget
    {
    public:
        QPixmap iconPixmap;
        QString labelText;
        QString targetId;
        QgisMapWidget *mapWidget;

        TargetIconWidget(QWidget *parent = nullptr) : QWidget(parent), mapWidget(nullptr) {}

        void paintEvent(QPaintEvent *) override
        {
            QPainter painter(this);
            painter.setRenderHint(QPainter::Antialiasing);

            // 绘制图标
            if (!iconPixmap.isNull())
            {
                painter.drawPixmap(0, 0, iconPixmap);
            }

            // 绘制标签
            if (!labelText.isEmpty())
            {
                QFont font = painter.font();
                font.setPointSize(8);
                font.setBold(true);
                painter.setFont(font);

                QFontMetrics fm(font);
                QRect textRect = fm.boundingRect(labelText);
                textRect.moveCenter(QPoint(width() / 2, height() - 10));
                textRect.adjust(-2, -1, 2, 1);

                // 绘制文字背景
                painter.setPen(Qt::NoPen);
                painter.setBrush(QColor(255, 255, 255, 200));
                painter.drawRoundedRect(textRect, 2, 2);

                // 绘制文字
                painter.setPen(Qt::black);
                painter.drawText(textRect, Qt::AlignCenter, labelText);
            }
        }

        void mousePressEvent(QMouseEvent *event) override
        {
            if (event->button() == Qt::LeftButton)
            {
                qDebug() << "目标图标被左键点击:" << labelText;
            }
            else if (event->button() == Qt::RightButton && mapWidget)
            {
                // 右键点击显示上下文菜单
                QPoint globalPos = mapToGlobal(event->pos());
                mapWidget->showTargetContextMenu(targetId, globalPos);
            }
            QWidget::mousePressEvent(event);
        }
    };

    TargetIconWidget *targetWidget = new TargetIconWidget(iconWidget);
    targetWidget->iconPixmap = createTargetIcon(type, iconSize);
    targetWidget->labelText = label;
    targetWidget->targetId = iconId;
    targetWidget->mapWidget = this;
    targetWidget->setGeometry(0, 0, iconSize, iconSize + 20);
    targetWidget->show();

    iconWidget->show();
    iconWidget->raise();

    // 保存到管理容器中
    m_targetIcons[iconId] = iconWidget;
    m_targetPositions[iconId] = qMakePair(lat, lng);
    m_targetTypes[iconId] = type;

    // 创建目标详细信息
    TargetInfo targetInfo;
    targetInfo.id = iconId;
    targetInfo.type = type;
    targetInfo.label = label;
    targetInfo.lat = lat;
    targetInfo.lng = lng;
    targetInfo.createTime = QDateTime::currentDateTime();
    targetInfo.status = "活跃";
    targetInfo.relatedMessages = QStringList() << QString("目标创建于 %1").arg(targetInfo.createTime.toString("yyyy-MM-dd hh:mm:ss"));

    m_targetInfos[iconId] = targetInfo;

    qDebug() << "QgisMapWidget: 添加目标图标" << iconId << "类型:" << type << "位置:" << lat << "," << lng;
}

void QgisMapWidget::removeTargetIcon(const QString &iconId)
{
    if (m_targetIcons.contains(iconId))
    {
        QWidget *widget = m_targetIcons[iconId];
        if (widget)
        {
            widget->deleteLater();
        }

        m_targetIcons.remove(iconId);
        m_targetPositions.remove(iconId);
        m_targetTypes.remove(iconId);

        qDebug() << "QgisMapWidget: 移除目标图标" << iconId;
    }
}

void QgisMapWidget::clearAllTargetIcons()
{
    // 清除所有目标图标
    for (auto it = m_targetIcons.begin(); it != m_targetIcons.end(); ++it)
    {
        QWidget *widget = it.value();
        if (widget)
        {
            widget->deleteLater();
        }
    }

    m_targetIcons.clear();
    m_targetPositions.clear();
    m_targetTypes.clear();

    qDebug() << "QgisMapWidget: 已清除所有目标图标";
}

void QgisMapWidget::updateTargetIconPositions()
{
    // 更新所有目标图标的位置
    for (auto it = m_targetIcons.begin(); it != m_targetIcons.end(); ++it)
    {
        QString iconId = it.key();
        QWidget *iconWidget = it.value();

        if (iconWidget && m_targetPositions.contains(iconId))
        {
            auto pos = m_targetPositions[iconId];
            double lat = pos.first;
            double lng = pos.second;

            // 转换坐标
            QPointF pixelPos = m_tileMapView->latLngToViewPixel(lat, lng);

            // 更新widget位置
            int iconSize = 32;
            iconWidget->setGeometry(pixelPos.x() - iconSize / 2, pixelPos.y() - iconSize, iconSize, iconSize + 20);
        }
    }

    qDebug() << "QgisMapWidget: 已更新" << m_targetIcons.size() << "个目标图标位置";
}

void QgisMapWidget::repositionFloatingToolPanel()
{
    if (m_floatingToolPanel && m_mapContainer)
    {
        // 重新计算工具栏位置，确保完全显示在窗口内且不与地图信息面板重叠
        int panelWidth = m_floatingToolPanel->width();
        int containerWidth = m_mapContainer->width();
        int xPos = qMax(300, containerWidth - panelWidth - 10); // 确保不与左上角的地图信息面板重叠
        m_floatingToolPanel->move(xPos, 10);

        qDebug() << "QgisMapWidget: 重新定位浮动工具面板到" << xPos << ", 10";
    }
}

void QgisMapWidget::showTargetSelectionMenu(QPushButton *sourceButton)
{
    // 创建上下文菜单
    QMenu *targetMenu = new QMenu(this);
    targetMenu->setStyleSheet(
        "QMenu {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "    stop:0 rgba(255, 255, 255, 255), stop:1 rgba(248, 250, 252, 255));"
        "    border: 2px solid rgba(52, 152, 219, 150);"
        "    border-radius: 8px;"
        "    padding: 8px;"
        "    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);"
        "}"
        "QMenu::item {"
        "    padding: 10px 24px;"
        "    margin: 2px;"
        "    border-radius: 6px;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    color: #2c3e50;"
        "}"
        "QMenu::item:selected {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "    stop:0 rgba(52, 152, 219, 100), stop:1 rgba(41, 128, 185, 120));"
        "    color: white;"
        "    font-weight: 600;"
        "}"
        "QMenu::item:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "    stop:0 rgba(31, 97, 141, 150), stop:1 rgba(21, 67, 96, 170));"
        "}"
        "QMenu::separator {"
        "    height: 2px;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "    stop:0 transparent, stop:0.5 rgba(52, 152, 219, 100), stop:1 transparent);"
        "    margin: 4px 12px;"
        "}");

    // 添加飞机选项
    QAction *aircraftAction = targetMenu->addAction("✈️ 添加飞机");
    aircraftAction->setToolTip("在地图中心添加飞机目标");
    connect(aircraftAction, &QAction::triggered, [this]()
            { addTargetAtMapCenter(AIRCRAFT); });

    // 添加车辆选项
    QAction *vehicleAction = targetMenu->addAction("🚗 添加车辆");
    vehicleAction->setToolTip("在地图中心添加车辆目标");
    connect(vehicleAction, &QAction::triggered, [this]()
            { addTargetAtMapCenter(VEHICLE); });

    // 添加船舶选项
    QAction *shipAction = targetMenu->addAction("🚢 添加船舶");
    shipAction->setToolTip("在地图中心添加船舶目标");
    connect(shipAction, &QAction::triggered, [this]()
            { addTargetAtMapCenter(SHIP); });

    // 添加分隔线
    targetMenu->addSeparator();

    // 添加清除所有目标选项
    QAction *clearAction = targetMenu->addAction("🗑️ 清除所有目标");
    clearAction->setToolTip("清除地图上的所有目标图标");
    connect(clearAction, &QAction::triggered, [this]()
            { clearAllTargetIcons(); });

    // 在按钮左侧显示菜单，避免与控制面板重叠
    QPoint buttonPos = sourceButton->mapToGlobal(QPoint(-targetMenu->sizeHint().width(), 0));
    targetMenu->exec(buttonPos);

    // 菜单会自动删除
    targetMenu->deleteLater();
}

void QgisMapWidget::addTargetAtMapCenter(TargetType type)
{
    // 获取地图中心坐标
    auto center = m_tileMapView->getCenter();
    double lat = center.first;
    double lng = center.second;

    // 生成目标标签
    QString typeStr;
    QString prefix;
    switch (type)
    {
    case AIRCRAFT:
        typeStr = "飞机";
        prefix = "AC";
        break;
    case VEHICLE:
        typeStr = "车辆";
        prefix = "VH";
        break;
    case SHIP:
        typeStr = "船舶";
        prefix = "SH";
        break;
    }

    // 生成唯一编号
    static int targetCounter = 1;
    QString label = QString("%1-%2").arg(prefix).arg(targetCounter++, 3, 10, QChar('0'));

    // 在地图中心添加目标
    addTargetIcon(lat, lng, type, label);

    qDebug() << "QgisMapWidget: 添加了" << typeStr << "目标:" << label << "在位置" << lat << "," << lng;
}

void QgisMapWidget::showTargetContextMenu(const QString &targetId, const QPoint &position)
{
    if (!m_targetInfos.contains(targetId))
        return;

    // 创建上下文菜单
    QMenu *contextMenu = new QMenu(this);
    contextMenu->setStyleSheet(
        "QMenu {"
        "    background-color: white;"
        "    border: 1px solid #ccc;"
        "    border-radius: 4px;"
        "    padding: 4px;"
        "}"
        "QMenu::item {"
        "    padding: 8px 20px;"
        "    margin: 1px;"
        "    border-radius: 2px;"
        "}"
        "QMenu::item:selected {"
        "    background-color: #e3f2fd;"
        "    color: #1976d2;"
        "}"
        "QMenu::item:pressed {"
        "    background-color: #bbdefb;"
        "}");

    // 获取目标信息
    const TargetInfo &targetInfo = m_targetInfos[targetId];

    // 添加菜单标题
    QAction *titleAction = contextMenu->addAction(QString("目标: %1").arg(targetInfo.label));
    titleAction->setEnabled(false);
    contextMenu->addSeparator();

    // 1. 展示足迹
    QAction *trajectoryAction = contextMenu->addAction("📍 展示足迹");
    trajectoryAction->setToolTip("显示相同类型目标的移动轨迹");
    connect(trajectoryAction, &QAction::triggered, [this, targetId]()
            { showTargetTrajectory(targetId); });

    // 2. 详细信息
    QAction *detailsAction = contextMenu->addAction("ℹ️ 详细信息");
    detailsAction->setToolTip("查看目标的详细信息");
    connect(detailsAction, &QAction::triggered, [this, targetId]()
            { showTargetDetails(targetId); });

    // 3. 查找相关报文
    QAction *messagesAction = contextMenu->addAction("📄 查找相关报文");
    messagesAction->setToolTip("查看与该目标相关的报文信息");
    connect(messagesAction, &QAction::triggered, [this, targetId]()
            { showTargetMessages(targetId); });

    contextMenu->addSeparator();

    // 如果当前有轨迹显示，添加隐藏轨迹选项
    if (m_trajectoryVisible && m_currentTrajectoryTargetId == targetId)
    {
        QAction *hideTrajectoryAction = contextMenu->addAction("❌ 隐藏足迹");
        hideTrajectoryAction->setToolTip("隐藏当前显示的轨迹");
        connect(hideTrajectoryAction, &QAction::triggered, [this]()
                { hideTargetTrajectory(); });
    }

    // 显示菜单
    contextMenu->exec(position);
    contextMenu->deleteLater();
}

void QgisMapWidget::showTargetTrajectory(const QString &targetId)
{
    if (!m_targetInfos.contains(targetId))
        return;

    // 先清除之前的轨迹
    hideTargetTrajectory();

    // 获取目标类型
    TargetType targetType = m_targetInfos[targetId].type;

    // 收集相同类型的所有目标，按创建时间排序
    QList<QPair<QDateTime, QString>> sameTypeTargets;
    for (auto it = m_targetInfos.begin(); it != m_targetInfos.end(); ++it)
    {
        if (it.value().type == targetType)
        {
            sameTypeTargets.append(qMakePair(it.value().createTime, it.key()));
        }
    }

    // 按时间排序
    std::sort(sameTypeTargets.begin(), sameTypeTargets.end());

    if (sameTypeTargets.size() < 2)
    {
        QMessageBox::information(this, "轨迹显示", "需要至少2个相同类型的目标才能显示轨迹");
        return;
    }

    // 绘制轨迹线
    for (int i = 0; i < sameTypeTargets.size() - 1; ++i)
    {
        QString fromId = sameTypeTargets[i].second;
        QString toId = sameTypeTargets[i + 1].second;

        if (m_targetPositions.contains(fromId) && m_targetPositions.contains(toId))
        {
            auto fromPos = m_targetPositions[fromId];
            auto toPos = m_targetPositions[toId];

            drawTrajectoryLine(fromPos.first, fromPos.second, toPos.first, toPos.second, i + 1);
        }
    }

    m_trajectoryVisible = true;
    m_currentTrajectoryTargetId = targetId;

    qDebug() << "QgisMapWidget: 显示目标轨迹，类型:" << targetType << "目标数量:" << sameTypeTargets.size();
}

void QgisMapWidget::hideTargetTrajectory()
{
    // 清除所有轨迹线
    for (QWidget *lineWidget : m_trajectoryLines)
    {
        if (lineWidget)
        {
            lineWidget->deleteLater();
        }
    }
    m_trajectoryLines.clear();

    m_trajectoryVisible = false;
    m_currentTrajectoryTargetId = "";

    qDebug() << "QgisMapWidget: 已隐藏目标轨迹";
}

void QgisMapWidget::drawTrajectoryLine(double lat1, double lng1, double lat2, double lng2, int segmentIndex)
{
    // 创建轨迹线widget
    QWidget *lineWidget = new QWidget(m_mapContainer);
    lineWidget->setAttribute(Qt::WA_TransparentForMouseEvents);
    lineWidget->setStyleSheet("background-color: transparent;");

    // 转换坐标
    QPointF startPixel = m_tileMapView->latLngToViewPixel(lat1, lng1);
    QPointF endPixel = m_tileMapView->latLngToViewPixel(lat2, lng2);

    // 计算边界矩形，根据缩放级别调整边距
    int margin = qMax(20, qMin(50, m_tileMapView->getZoomLevel() * 2 + 10));
    int minX = qMin(startPixel.x(), endPixel.x()) - margin;
    int maxX = qMax(startPixel.x(), endPixel.x()) + margin;
    int minY = qMin(startPixel.y(), endPixel.y()) - margin;
    int maxY = qMax(startPixel.y(), endPixel.y()) + margin;

    lineWidget->setGeometry(minX, minY, maxX - minX, maxY - minY);

    // 调整相对坐标
    QPointF relativeStart(startPixel.x() - minX, startPixel.y() - minY);
    QPointF relativeEnd(endPixel.x() - minX, endPixel.y() - minY);

    // 创建动态轨迹线绘制类
    class TrajectoryLineWidget : public QWidget
    {
    public:
        QPointF start, end;
        int segment;
        QTimer *animationTimer;
        int animationOffset;
        // 添加地理坐标信息，用于地图移动时重新计算位置
        double lat1, lng1, lat2, lng2;

        TrajectoryLineWidget(QWidget *parent = nullptr) : QWidget(parent), segment(0), animationOffset(0)
        {
            // 创建动画定时器
            animationTimer = new QTimer(this);
            connect(animationTimer, &QTimer::timeout, this, &TrajectoryLineWidget::updateAnimation);
            animationTimer->start(100); // 100ms间隔
        }

        void updateAnimation()
        {
            animationOffset = (animationOffset + 2) % 20; // 虚线动画偏移
            update();
        }

        void paintEvent(QPaintEvent *) override
        {
            QPainter painter(this);
            painter.setRenderHint(QPainter::Antialiasing);

            // 优先使用更新的坐标属性，确保缩放后坐标正确
            QPointF drawStart, drawEnd;
            bool hasUpdatedCoords = false;

            if (property("startX").isValid() && property("startY").isValid() &&
                property("endX").isValid() && property("endY").isValid())
            {
                drawStart = QPointF(property("startX").toDouble(), property("startY").toDouble());
                drawEnd = QPointF(property("endX").toDouble(), property("endY").toDouble());
                hasUpdatedCoords = true;
            }
            else
            {
                // 只有在没有属性时才使用初始坐标（这种情况不应该发生）
                drawStart = start;
                drawEnd = end;
                qWarning() << "QgisMapWidget: 轨迹线缺少坐标属性，使用初始坐标";
            }

            qDebug() << "QgisMapWidget: 轨迹线绘制坐标 - 使用更新坐标:" << hasUpdatedCoords
                     << "起点:" << drawStart << "终点:" << drawEnd;

            // 绘制红色动态虚线，根据缩放级别调整粗细
            int zoomLevel = 10; // 默认缩放级别
            // 优先使用属性中保存的缩放级别
            if (property("currentZoomLevel").isValid())
            {
                zoomLevel = property("currentZoomLevel").toInt();
            }
            else if (parent() && parent()->parent())
            {
                QgisMapWidget *mapWidget = static_cast<QgisMapWidget *>(parent()->parent());
                if (mapWidget->getTileMapView())
                {
                    zoomLevel = mapWidget->getTileMapView()->getZoomLevel();
                }
            }
            // 优化线条粗细计算，避免在高缩放级别下过粗
            int lineWidth = qMax(1, qMin(3, (zoomLevel - 5) / 4 + 2)); // 更合理的线条粗细
            qDebug() << "QgisMapWidget: 轨迹线绘制 - 缩放级别:" << zoomLevel << "线条粗细:" << lineWidth;
            QPen linePen(Qt::red, lineWidth, Qt::DashLine);
            linePen.setDashOffset(animationOffset);
            painter.setPen(linePen);
            painter.drawLine(drawStart, drawEnd);

            // 绘制方向箭头
            drawArrow(painter, drawStart, drawEnd, zoomLevel);

            // 绘制段号，根据缩放级别调整大小
            QPointF midPoint = (drawStart + drawEnd) / 2;
            painter.setPen(QPen(Qt::white, 1));
            painter.setBrush(Qt::red);
            // 优化圆圈大小计算，避免在高缩放级别下过大
            int circleSize = qMax(4, qMin(8, (zoomLevel - 5) / 3 + 5)); // 更合理的圆圈大小
            painter.drawEllipse(midPoint, circleSize, circleSize);

            painter.setPen(Qt::white);
            QFont font = painter.font();
            // 优化字体大小计算
            int fontSize = qMax(5, qMin(8, (zoomLevel - 5) / 3 + 6)); // 更合理的字体大小
            font.setPointSize(fontSize);
            font.setBold(true);
            painter.setFont(font);
            painter.drawText(QRect(midPoint.x() - circleSize, midPoint.y() - circleSize,
                                   circleSize * 2, circleSize * 2),
                             Qt::AlignCenter, QString::number(segment));
        }

        void drawArrow(QPainter &painter, const QPointF &start, const QPointF &end, int zoomLevel = 10)
        {
            // 计算箭头方向
            QPointF direction = end - start;
            double length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
            if (length < 1)
                return;

            direction /= length;
            QPointF perpendicular(-direction.y(), direction.x());

            // 箭头位置（线段的3/4处）
            QPointF arrowPos = start + direction * length * 0.75;

            // 箭头大小，根据缩放级别调整
            double arrowSize = qMax(6.0, qMin(12.0, zoomLevel / 2.0 + 4.0));
            QPointF arrowHead1 = arrowPos - direction * arrowSize + perpendicular * arrowSize * 0.5;
            QPointF arrowHead2 = arrowPos - direction * arrowSize - perpendicular * arrowSize * 0.5;

            // 绘制箭头，根据缩放级别调整粗细
            int arrowLineWidth = qMax(1, qMin(3, zoomLevel / 4 + 1));
            painter.setPen(QPen(Qt::red, arrowLineWidth));
            painter.setBrush(Qt::red);
            QPolygonF arrow;
            arrow << arrowPos << arrowHead1 << arrowHead2;
            painter.drawPolygon(arrow);
        }
    };

    TrajectoryLineWidget *trajectoryWidget = new TrajectoryLineWidget(lineWidget);
    trajectoryWidget->start = relativeStart;
    trajectoryWidget->end = relativeEnd;
    trajectoryWidget->segment = segmentIndex;
    // 保存地理坐标，用于地图移动时重新计算位置
    trajectoryWidget->lat1 = lat1;
    trajectoryWidget->lng1 = lng1;
    trajectoryWidget->lat2 = lat2;
    trajectoryWidget->lng2 = lng2;

    // 设置对象名和属性，便于后续识别和更新
    trajectoryWidget->setObjectName("TrajectoryLineWidget");
    trajectoryWidget->setProperty("lat1", lat1);
    trajectoryWidget->setProperty("lng1", lng1);
    trajectoryWidget->setProperty("lat2", lat2);
    trajectoryWidget->setProperty("lng2", lng2);
    // 设置初始缩放级别
    trajectoryWidget->setProperty("currentZoomLevel", m_tileMapView->getZoomLevel());
    // 立即设置相对坐标属性，确保paintEvent能正确使用
    trajectoryWidget->setProperty("startX", relativeStart.x());
    trajectoryWidget->setProperty("startY", relativeStart.y());
    trajectoryWidget->setProperty("endX", relativeEnd.x());
    trajectoryWidget->setProperty("endY", relativeEnd.y());

    qDebug() << "QgisMapWidget: 创建轨迹线 - 地理坐标:"
             << "起点(" << lat1 << "," << lng1 << ")"
             << "终点(" << lat2 << "," << lng2 << ")"
             << "相对坐标: 起点" << relativeStart << "终点" << relativeEnd;

    trajectoryWidget->setGeometry(0, 0, lineWidget->width(), lineWidget->height());
    trajectoryWidget->show();

    lineWidget->show();
    lineWidget->raise();

    // 保存到轨迹线列表
    m_trajectoryLines.append(lineWidget);
}

void QgisMapWidget::updateTrajectoryLinePositions()
{
    qDebug() << "QgisMapWidget: 开始更新轨迹线位置，当前缩放级别:" << (m_tileMapView ? m_tileMapView->getZoomLevel() : -1);
    // 更新所有轨迹线的位置
    for (QWidget *lineWidget : m_trajectoryLines)
    {
        if (!lineWidget)
            continue;

        // 查找TrajectoryLineWidget子组件
        QList<QWidget *> children = lineWidget->findChildren<QWidget *>();
        for (QWidget *child : children)
        {
            // 检查是否是TrajectoryLineWidget（通过对象名识别）
            if (child->objectName() == "TrajectoryLineWidget")
            {
                // 获取保存的地理坐标
                double lat1 = child->property("lat1").toDouble();
                double lng1 = child->property("lng1").toDouble();
                double lat2 = child->property("lat2").toDouble();
                double lng2 = child->property("lng2").toDouble();

                qDebug() << "QgisMapWidget: 更新轨迹线 - 地理坐标:"
                         << "起点(" << lat1 << "," << lng1 << ")"
                         << "终点(" << lat2 << "," << lng2 << ")";

                // 重新计算屏幕坐标
                QPointF startPixel = m_tileMapView->latLngToViewPixel(lat1, lng1);
                QPointF endPixel = m_tileMapView->latLngToViewPixel(lat2, lng2);

                qDebug() << "QgisMapWidget: 更新轨迹线 - 屏幕坐标:"
                         << "起点" << startPixel << "终点" << endPixel;

                // 计算包围矩形，根据缩放级别调整边距
                int margin = qMax(30, qMin(60, m_tileMapView->getZoomLevel() * 2 + 15));
                double minX = qMin(startPixel.x(), endPixel.x()) - margin;
                double maxX = qMax(startPixel.x(), endPixel.x()) + margin;
                double minY = qMin(startPixel.y(), endPixel.y()) - margin;
                double maxY = qMax(startPixel.y(), endPixel.y()) + margin;

                // 更新lineWidget的位置和大小
                lineWidget->setGeometry(minX, minY, maxX - minX, maxY - minY);

                // 更新相对坐标
                QPointF relativeStart(startPixel.x() - minX, startPixel.y() - minY);
                QPointF relativeEnd(endPixel.x() - minX, endPixel.y() - minY);

                // 更新坐标属性
                child->setProperty("startX", relativeStart.x());
                child->setProperty("startY", relativeStart.y());
                child->setProperty("endX", relativeEnd.x());
                child->setProperty("endY", relativeEnd.y());

                // 更新当前缩放级别属性
                int currentZoom = m_tileMapView->getZoomLevel();
                child->setProperty("currentZoomLevel", currentZoom);
                qDebug() << "QgisMapWidget: 更新轨迹线缩放级别到:" << currentZoom;

                // 触发重绘
                child->update();
                lineWidget->update(); // 同时更新父widget
                break;
            }
        }
    }
}

void QgisMapWidget::showTargetDetails(const QString &targetId)
{
    if (!m_targetInfos.contains(targetId))
        return;

    const TargetInfo &targetInfo = m_targetInfos[targetId];

    // 创建详细信息对话框
    QDialog *detailDialog = new QDialog(this);
    detailDialog->setWindowTitle("目标详细信息");
    detailDialog->setModal(true);
    detailDialog->setFixedSize(500, 400);
    detailDialog->setStyleSheet(
        "QDialog {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #ffffff, stop:1 #f8f9fa);"
        "    border: 2px solid #e3f2fd;"
        "    border-radius: 12px;"
        "}"
        "QLabel {"
        "    color: #333333;"
        "    font-family: 'Microsoft YaHei', sans-serif;"
        "    margin: 2px 0px;"
        "    padding: 4px;"
        "}"
        "QLabel#titleLabel {"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "    color: #1976d2;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "        stop:0 #e3f2fd, stop:1 #bbdefb);"
        "    border-radius: 8px;"
        "    padding: 12px;"
        "    margin: 0px 0px 15px 0px;"
        "}"
        "QLabel#typeLabel {"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    color: #2e7d32;"
        "    background-color: #e8f5e8;"
        "    border-left: 4px solid #4caf50;"
        "    padding: 8px 12px;"
        "    border-radius: 4px;"
        "}"
        "QLabel#infoLabel {"
        "    font-size: 13px;"
        "    color: #555555;"
        "    background-color: #fafafa;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 6px;"
        "    padding: 10px 15px;"
        "    margin: 4px 0px;"
        "    min-height: 20px;"
        "    word-wrap: break-word;"
        "}"
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px 20px;"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #42a5f5, stop:1 #1e88e5);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "}");

    QVBoxLayout *layout = new QVBoxLayout(detailDialog);
    layout->setSpacing(15);
    layout->setContentsMargins(20, 20, 20, 20);

    // 标题
    QLabel *titleLabel = new QLabel(QString("🎯 目标信息: %1").arg(targetInfo.label));
    titleLabel->setObjectName("titleLabel");
    layout->addWidget(titleLabel);

    // 目标类型
    QString typeStr;
    QString typeIcon;
    switch (targetInfo.type)
    {
    case AIRCRAFT:
        typeStr = "飞机";
        typeIcon = "✈️";
        break;
    case VEHICLE:
        typeStr = "车辆";
        typeIcon = "🚗";
        break;
    case SHIP:
        typeStr = "船舶";
        typeIcon = "🚢";
        break;
    }

    QLabel *typeLabel = new QLabel(QString("%1 %2").arg(typeIcon).arg(typeStr));
    typeLabel->setObjectName("typeLabel");
    layout->addWidget(typeLabel);

    // 信息区域容器
    QWidget *infoContainer = new QWidget();
    QVBoxLayout *infoLayout = new QVBoxLayout(infoContainer);
    infoLayout->setSpacing(10);
    infoLayout->setContentsMargins(5, 5, 5, 5);

    // 目标编号
    QLabel *idLabel = new QLabel(QString("📋 目标编号: %1").arg(targetInfo.label));
    idLabel->setObjectName("infoLabel");
    idLabel->setWordWrap(true);
    idLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    infoLayout->addWidget(idLabel);

    // 创建时间
    QLabel *timeLabel = new QLabel(QString("🕒 创建时间: %1").arg(targetInfo.createTime.toString("yyyy-MM-dd hh:mm:ss")));
    timeLabel->setObjectName("infoLabel");
    timeLabel->setWordWrap(true);
    timeLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    infoLayout->addWidget(timeLabel);

    // 坐标位置
    QLabel *posLabel = new QLabel(QString("📍 坐标位置: %1°N, %2°E")
                                      .arg(QString::number(targetInfo.lat, 'f', 6))
                                      .arg(QString::number(targetInfo.lng, 'f', 6)));
    posLabel->setObjectName("infoLabel");
    posLabel->setWordWrap(true);
    posLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    infoLayout->addWidget(posLabel);

    // 目标状态
    QLabel *statusLabel = new QLabel(QString("⚡ 目标状态: %1").arg(targetInfo.status));
    statusLabel->setObjectName("infoLabel");
    statusLabel->setWordWrap(true);
    statusLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    infoLayout->addWidget(statusLabel);

    layout->addWidget(infoContainer);

    // 添加少量弹性空间
    layout->addStretch(1);

    // 关闭按钮
    QPushButton *closeBtn = new QPushButton("✅ 关闭");
    connect(closeBtn, &QPushButton::clicked, detailDialog, &QDialog::accept);
    layout->addWidget(closeBtn);

    detailDialog->exec();
    detailDialog->deleteLater();
}

void QgisMapWidget::showTargetMessages(const QString &targetId)
{
    if (!m_targetInfos.contains(targetId))
        return;

    const TargetInfo &targetInfo = m_targetInfos[targetId];

    // 创建报文信息对话框
    QDialog *messageDialog = new QDialog(this);
    messageDialog->setWindowTitle("相关报文信息");
    messageDialog->setModal(true);
    messageDialog->setFixedSize(650, 450);
    messageDialog->setStyleSheet(
        "QDialog {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #ffffff, stop:1 #f8f9fa);"
        "    border: 2px solid #e3f2fd;"
        "    border-radius: 12px;"
        "}"
        "QLabel {"
        "    color: #333333;"
        "    font-family: 'Microsoft YaHei', sans-serif;"
        "    margin: 2px 0px;"
        "    padding: 4px;"
        "}"
        "QLabel#titleLabel {"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "    color: #1976d2;"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "        stop:0 #e3f2fd, stop:1 #bbdefb);"
        "    border-radius: 8px;"
        "    padding: 12px;"
        "    margin: 0px 0px 15px 0px;"
        "}"
        "QListWidget {"
        "    border: 2px solid #e0e0e0;"
        "    border-radius: 8px;"
        "    background-color: #fafafa;"
        "    font-size: 12px;"
        "    selection-background-color: #e3f2fd;"
        "    alternate-background-color: #f5f5f5;"
        "}"
        "QListWidget::item {"
        "    padding: 8px 12px;"
        "    border-bottom: 1px solid #eeeeee;"
        "    margin: 1px;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #e3f2fd;"
        "    color: #1976d2;"
        "    border-radius: 4px;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #f0f8ff;"
        "    border-radius: 4px;"
        "}"
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    padding: 10px 20px;"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    min-width: 80px;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #42a5f5, stop:1 #1e88e5);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "}"
        "QPushButton#refreshBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #4caf50, stop:1 #388e3c);"
        "}"
        "QPushButton#refreshBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #66bb6a, stop:1 #43a047);"
        "}");

    QVBoxLayout *layout = new QVBoxLayout(messageDialog);
    layout->setSpacing(15);
    layout->setContentsMargins(25, 25, 25, 25);

    // 标题
    QLabel *titleLabel = new QLabel(QString("📄 目标 %1 的相关报文").arg(targetInfo.label));
    titleLabel->setObjectName("titleLabel");
    layout->addWidget(titleLabel);

    // 报文列表
    QListWidget *messageList = new QListWidget();
    messageList->setAlternatingRowColors(true);

    // 添加示例报文数据
    for (const QString &message : targetInfo.relatedMessages)
    {
        QListWidgetItem *item = new QListWidgetItem(QString("📝 %1").arg(message));
        messageList->addItem(item);
    }

    // 添加一些模拟的报文数据
    QListWidgetItem *detectItem = new QListWidgetItem(QString("🔍 [%1] 目标检测报文: 发现%2目标").arg(targetInfo.createTime.toString("hh:mm:ss")).arg(targetInfo.label));
    messageList->addItem(detectItem);

    QListWidgetItem *posItem = new QListWidgetItem(QString("📍 [%1] 位置更新报文: 坐标 %2, %3").arg(targetInfo.createTime.addSecs(30).toString("hh:mm:ss")).arg(targetInfo.lat, 0, 'f', 6).arg(targetInfo.lng, 0, 'f', 6));
    messageList->addItem(posItem);

    QListWidgetItem *statusItem = new QListWidgetItem(QString("⚡ [%1] 状态报文: 目标状态为%2").arg(targetInfo.createTime.addSecs(60).toString("hh:mm:ss")).arg(targetInfo.status));
    messageList->addItem(statusItem);

    layout->addWidget(messageList);

    // 按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(15);

    QPushButton *refreshBtn = new QPushButton("🔄 刷新");
    refreshBtn->setObjectName("refreshBtn");
    connect(refreshBtn, &QPushButton::clicked, [messageList, targetInfo]()
            {
        QListWidgetItem *refreshItem = new QListWidgetItem(QString("🔄 [%1] 手动刷新报文").arg(QDateTime::currentDateTime().toString("hh:mm:ss")));
        messageList->addItem(refreshItem);
        messageList->scrollToBottom(); });

    QPushButton *closeBtn = new QPushButton("❌ 关闭");
    connect(closeBtn, &QPushButton::clicked, messageDialog, &QDialog::accept);

    buttonLayout->addWidget(refreshBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeBtn);

    layout->addLayout(buttonLayout);

    messageDialog->exec();
    messageDialog->deleteLater();
}
