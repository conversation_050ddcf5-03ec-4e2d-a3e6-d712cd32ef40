#include "waterfallplot_simple.h"
#include <QApplication>
#include <QContextMenuEvent>
#include <QDebug>
#include <algorithm>

// 静态颜色定义 - 采用现代深色主题
const QColor WaterfallPlotSimple::BACKGROUND_COLOR = QColor(20, 25, 35); // 深蓝灰色
const QColor WaterfallPlotSimple::AXIS_COLOR = QColor(180, 180, 180);    // 浅灰色
const QColor WaterfallPlotSimple::TEXT_COLOR = QColor(200, 200, 200);    // 白灰色

WaterfallPlotSimple::WaterfallPlotSimple(QWidget *parent)
    : QWidget(parent), m_startFreq(0.0), m_endFreq(1000000.0), m_timeWindow(10.0), m_minAmplitude(-120.0), m_maxAmplitude(0.0), m_colorScheme(PurpleGreen), m_maxHistorySize(100), m_marginLeft(60), m_marginRight(10), m_marginTop(10), m_marginBottom(30), m_contextMenu(nullptr)
{
    setMinimumSize(200, 100);                                      // 降低最小尺寸要求
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置为可扩展
    setMouseTracking(true);

    // 创建右键菜单
    m_contextMenu = new QMenu(this);
    m_clearDataAction = m_contextMenu->addAction("清除数据", this, &WaterfallPlotSimple::clearData);

    m_contextMenu->addSeparator();

    // 颜色方案子菜单
    m_colorSchemeMenu = m_contextMenu->addMenu("颜色方案");
    m_purpleGreenAction = m_colorSchemeMenu->addAction("紫绿渐变", this, &WaterfallPlotSimple::setPurpleGreenScheme);
    m_blueRedAction = m_colorSchemeMenu->addAction("蓝红渐变", this, &WaterfallPlotSimple::setBlueRedScheme);
    m_grayscaleAction = m_colorSchemeMenu->addAction("灰度渐变", this, &WaterfallPlotSimple::setGrayscaleScheme);

    // 设置颜色方案动作为可选择的
    m_purpleGreenAction->setCheckable(true);
    m_blueRedAction->setCheckable(true);
    m_grayscaleAction->setCheckable(true);
    m_purpleGreenAction->setChecked(true); // 默认选中
}

WaterfallPlotSimple::~WaterfallPlotSimple()
{
    // Qt会自动清理子对象
}

void WaterfallPlotSimple::setFrequencyRange(double startFreq, double endFreq)
{
    if (startFreq != m_startFreq || endFreq != m_endFreq)
    {
        m_startFreq = startFreq;
        m_endFreq = endFreq;
        update();
        emit frequencyRangeChanged(startFreq, endFreq);
    }
}

void WaterfallPlotSimple::setTimeWindow(double timeWindow)
{
    if (timeWindow != m_timeWindow)
    {
        m_timeWindow = timeWindow;
        m_maxHistorySize = static_cast<int>(timeWindow * 10); // 假设10fps

        // 限制历史数据大小
        while (m_historyData.size() > m_maxHistorySize)
        {
            m_historyData.removeFirst();
            m_timeStamps.removeFirst();
        }

        update();
    }
}

void WaterfallPlotSimple::setAmplitudeRange(double minAmplitude, double maxAmplitude)
{
    if (minAmplitude != m_minAmplitude || maxAmplitude != m_maxAmplitude)
    {
        m_minAmplitude = minAmplitude;
        m_maxAmplitude = maxAmplitude;
        update();
    }
}

void WaterfallPlotSimple::addSpectrumData(const QVector<double> &frequencies, const QVector<double> &amplitudes)
{
    if (frequencies.size() != amplitudes.size() || frequencies.isEmpty())
    {
        return;
    }

    // 更新频率数据（如果需要）
    if (m_frequencies != frequencies)
    {
        m_frequencies = frequencies;
    }

    // 添加新数据到历史记录
    m_historyData.append(amplitudes);
    m_timeStamps.append(QDateTime::currentMSecsSinceEpoch() / 1000.0);

    // 如果超过最大历史大小，移除最旧的数据
    if (m_historyData.size() > m_maxHistorySize)
    {
        m_historyData.removeFirst();
        m_timeStamps.removeFirst();
    }

    update();
    emit dataUpdated();
}

void WaterfallPlotSimple::setColorScheme(ColorScheme scheme)
{
    if (m_colorScheme != scheme)
    {
        m_colorScheme = scheme;

        // 更新菜单状态
        m_purpleGreenAction->setChecked(scheme == PurpleGreen);
        m_blueRedAction->setChecked(scheme == BlueRed);
        m_grayscaleAction->setChecked(scheme == Grayscale);

        update();
    }
}

void WaterfallPlotSimple::clearHistory()
{
    m_historyData.clear();
    m_timeStamps.clear();
    update();
}

QPair<double, double> WaterfallPlotSimple::frequencyRange() const
{
    return QPair<double, double>(m_startFreq, m_endFreq);
}

QPair<double, double> WaterfallPlotSimple::amplitudeRange() const
{
    return QPair<double, double>(m_minAmplitude, m_maxAmplitude);
}

void WaterfallPlotSimple::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 计算绘图区域
    m_plotRect = QRect(m_marginLeft, m_marginTop,
                       width() - m_marginLeft - m_marginRight,
                       height() - m_marginTop - m_marginBottom);

    m_colorScaleRect = QRect(15, m_marginTop,
                             35, m_plotRect.height());

    // 绘制背景和坐标轴
    drawBackground(painter);

    // 绘制瀑布图数据
    drawWaterfall(painter);

    // 绘制颜色标尺
    drawColorScale(painter);
}

void WaterfallPlotSimple::drawBackground(QPainter &painter)
{
    // 绘制背景
    painter.fillRect(rect(), BACKGROUND_COLOR);

    // 绘制绘图区域背景
    painter.fillRect(m_plotRect, BACKGROUND_COLOR.lighter(110));

    // 绘制坐标轴框架
    painter.setPen(QPen(AXIS_COLOR, 2));
    painter.drawRect(m_plotRect);

    // 绘制频率轴标签
    painter.setPen(QPen(TEXT_COLOR, 1));
    painter.setFont(QFont("Microsoft YaHei", 8));

    int numFreqLabels = 10; // 与频谱图网格线数量对应
    for (int i = 0; i <= numFreqLabels; ++i)
    {
        // 使用与频谱图网格线完全相同的X坐标计算方法
        int x = m_plotRect.left() + i * m_plotRect.width() / numFreqLabels;

        // 只在偶数位置显示标签，避免过于密集
        if (i % 2 == 0)
        {
            double freq = m_startFreq + i * (m_endFreq - m_startFreq) / numFreqLabels;
            QString label = formatFrequency(freq);
            QFontMetrics fm(painter.font());
            QRect textRect = fm.boundingRect(label);
            painter.drawText(x - textRect.width() / 2, m_plotRect.bottom() + 15, label);
        }

        // 绘制刻度线
        painter.drawLine(x, m_plotRect.bottom(), x, m_plotRect.bottom() + 5);
    }

    // 移除底部"频率"标题，避免重复显示
    // 移除左侧"时间"标题，改为显示颜色图例
}

void WaterfallPlotSimple::drawWaterfall(QPainter &painter)
{
    if (m_historyData.isEmpty() || m_frequencies.isEmpty())
    {
        return;
    }

    // 计算时间范围
    double currentTime = m_timeStamps.isEmpty() ? 0.0 : m_timeStamps.last();
    double startTime = currentTime - m_timeWindow;

    // 计算有效数据行数
    int validDataCount = 0;
    for (int t = 0; t < m_historyData.size(); ++t)
    {
        if (t < m_timeStamps.size() && m_timeStamps[t] >= startTime)
        {
            validDataCount++;
        }
    }

    if (validDataCount == 0)
        return;

    // 计算每行数据应占的高度，确保填满整个绘图区域
    int rowHeight = qMax(1, m_plotRect.height() / validDataCount);

    // 绘制每一行数据
    int currentRow = 0;
    for (int t = 0; t < m_historyData.size(); ++t)
    {
        if (t >= m_timeStamps.size())
            continue;

        double time = m_timeStamps[t];
        if (time < startTime)
            continue;

        const QVector<double> &amplitudes = m_historyData[t];

        // 计算当前行的Y位置，从顶部开始向下排列
        int y = m_plotRect.top() + currentRow * rowHeight;

        // 确保不超出绘图区域
        int actualRowHeight = qMin(rowHeight, m_plotRect.bottom() - y + 1);

        // 绘制这一行的频谱数据
        for (int f = 0; f < qMin(m_frequencies.size(), amplitudes.size()) - 1; ++f)
        {
            int x1 = frequencyToX(m_frequencies[f]);
            int x2 = frequencyToX(m_frequencies[f + 1]);

            if (x1 >= m_plotRect.left() && x1 <= m_plotRect.right())
            {
                QColor color = amplitudeToColor(amplitudes[f]);
                painter.fillRect(x1, y, x2 - x1, actualRowHeight, color);
            }
        }

        currentRow++;
    }
}

void WaterfallPlotSimple::drawColorScale(QPainter &painter)
{
    // 绘制颜色标尺背景
    painter.fillRect(m_colorScaleRect, BACKGROUND_COLOR.lighter(110));
    painter.setPen(QPen(AXIS_COLOR, 1));
    painter.drawRect(m_colorScaleRect);

    // 绘制颜色渐变
    int steps = m_colorScaleRect.height();
    for (int i = 0; i < steps; ++i)
    {
        double ratio = static_cast<double>(i) / steps;
        double amplitude = m_minAmplitude + ratio * (m_maxAmplitude - m_minAmplitude);
        QColor color = amplitudeToColor(amplitude);

        int y = m_colorScaleRect.bottom() - i;
        painter.fillRect(m_colorScaleRect.left(), y, m_colorScaleRect.width(), 1, color);
    }

    // 只保留颜色标尺，不显示数值标签和标题
}

QColor WaterfallPlotSimple::amplitudeToColor(double amplitude) const
{
    // 将幅度值归一化到0-1范围
    double ratio = (amplitude - m_minAmplitude) / (m_maxAmplitude - m_minAmplitude);
    ratio = qBound(0.0, ratio, 1.0);

    QColor color;

    switch (m_colorScheme)
    {
    case PurpleGreen:
        if (ratio < 0.5)
        {
            // 从深紫色到蓝紫色
            double localRatio = ratio * 2.0;
            color = QColor::fromRgb(
                static_cast<int>(75 + localRatio * (138 - 75)),
                static_cast<int>(0 + localRatio * (43 - 0)),
                static_cast<int>(130 + localRatio * (226 - 130)));
        }
        else
        {
            // 从蓝紫色到浅绿色
            double localRatio = (ratio - 0.5) * 2.0;
            color = QColor::fromRgb(
                static_cast<int>(138 - localRatio * (138 - 144)),
                static_cast<int>(43 + localRatio * (238 - 43)),
                static_cast<int>(226 - localRatio * (226 - 144)));
        }
        break;

    case BlueRed:
        if (ratio < 0.5)
        {
            // 从深蓝色到黄色
            double localRatio = ratio * 2.0;
            color = QColor::fromRgb(
                static_cast<int>(0 + localRatio * 255),
                static_cast<int>(0 + localRatio * 255),
                static_cast<int>(139 - localRatio * 139));
        }
        else
        {
            // 从黄色到红色
            double localRatio = (ratio - 0.5) * 2.0;
            color = QColor::fromRgb(
                255,
                static_cast<int>(255 - localRatio * 255),
                0);
        }
        break;

    case Grayscale:
    {
        int gray = static_cast<int>(ratio * 255);
        color = QColor::fromRgb(gray, gray, gray);
    }
    break;

    default:
        color = QColor::fromRgb(static_cast<int>(ratio * 255), 0, static_cast<int>((1.0 - ratio) * 255));
        break;
    }

    return color;
}

void WaterfallPlotSimple::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_plotRect.contains(event->pos()))
    {
        double freq = xToFrequency(event->pos().x());
        double time = yToTime(event->pos().y());
        double amplitude = (m_minAmplitude + m_maxAmplitude) / 2.0; // 简化实现

        emit timeFrequencyClicked(freq, time, amplitude);
    }

    QWidget::mousePressEvent(event);
}

void WaterfallPlotSimple::wheelEvent(QWheelEvent *event)
{
    // 简单的缩放功能
    if (m_plotRect.contains(event->position().toPoint()))
    {
        double scaleFactor = event->angleDelta().y() > 0 ? 0.9 : 1.1;
        double centerFreq = (m_startFreq + m_endFreq) / 2.0;
        double bandwidth = m_endFreq - m_startFreq;
        double newBandwidth = bandwidth * scaleFactor;

        setFrequencyRange(centerFreq - newBandwidth / 2.0, centerFreq + newBandwidth / 2.0);
    }

    QWidget::wheelEvent(event);
}

void WaterfallPlotSimple::contextMenuEvent(QContextMenuEvent *event)
{
    if (m_contextMenu)
    {
        m_contextMenu->exec(event->globalPos());
    }
}

void WaterfallPlotSimple::clearData()
{
    clearHistory();
}

void WaterfallPlotSimple::setPurpleGreenScheme()
{
    setColorScheme(PurpleGreen);
}

void WaterfallPlotSimple::setBlueRedScheme()
{
    setColorScheme(BlueRed);
}

void WaterfallPlotSimple::setGrayscaleScheme()
{
    setColorScheme(Grayscale);
}

int WaterfallPlotSimple::frequencyToX(double frequency) const
{
    if (m_endFreq == m_startFreq)
        return m_plotRect.left();

    double ratio = (frequency - m_startFreq) / (m_endFreq - m_startFreq);
    return m_plotRect.left() + static_cast<int>(ratio * m_plotRect.width());
}

int WaterfallPlotSimple::timeToY(double time) const
{
    if (m_timeWindow == 0.0)
        return m_plotRect.top();

    double ratio = time / m_timeWindow;
    return m_plotRect.top() + static_cast<int>(ratio * m_plotRect.height());
}

double WaterfallPlotSimple::xToFrequency(int x) const
{
    if (m_plotRect.width() == 0)
        return m_startFreq;

    double ratio = static_cast<double>(x - m_plotRect.left()) / m_plotRect.width();
    return m_startFreq + ratio * (m_endFreq - m_startFreq);
}

double WaterfallPlotSimple::yToTime(int y) const
{
    if (m_plotRect.height() == 0)
        return 0.0;

    double ratio = static_cast<double>(y - m_plotRect.top()) / m_plotRect.height();
    return ratio * m_timeWindow;
}

QString WaterfallPlotSimple::formatFrequency(double frequency) const
{
    if (frequency >= 1e9)
    {
        return QString("%1G").arg(frequency / 1e9, 0, 'f', 1);
    }
    else if (frequency >= 1e6)
    {
        return QString("%1M").arg(frequency / 1e6, 0, 'f', 1);
    }
    else if (frequency >= 1e3)
    {
        return QString("%1k").arg(frequency / 1e3, 0, 'f', 1);
    }
    else
    {
        return QString("%1").arg(frequency, 0, 'f', 0);
    }
}

QString WaterfallPlotSimple::formatTime(double time) const
{
    if (time >= 60)
    {
        int minutes = static_cast<int>(time / 60);
        int seconds = static_cast<int>(time - minutes * 60);
        return QString("%1:%2").arg(minutes).arg(seconds, 2, 10, QChar('0'));
    }
    else
    {
        return QString("%1s").arg(time, 0, 'f', 1);
    }
}
