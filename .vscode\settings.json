{"files.associations": {"*.qrc": "xml", "*.pro": "makefile", "*.pri": "makefile", "*.qss": "css"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.includePath": ["${workspaceFolder}/include", "${workspaceFolder}/src"], "cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/build", "cmake.generator": "Visual Studio 17 2022", "cmake.platform": "x64", "cmake.configureArgs": [], "cmake.buildArgs": ["--config", "Debug"], "files.exclude": {"**/build/**": true, "**/bin/**": false, "**/.qmake.stash": true, "**/Makefile": true, "**/moc_*.cpp": true, "**/moc_*.h": true, "**/qrc_*.cpp": true, "**/ui_*.h": true, "**/*.o": true, "**/*.obj": true}, "search.exclude": {"**/build/**": true, "**/bin/**": true}, "editor.formatOnSave": true, "editor.tabSize": 4, "editor.insertSpaces": true, "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "[c]": {"editor.defaultFormatter": "ms-vscode.cpptools"}}