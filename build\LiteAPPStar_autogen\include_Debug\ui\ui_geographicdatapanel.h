/********************************************************************************
** Form generated from reading UI file 'geographicdatapanel.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_GEOGRAPHICDATAPANEL_H
#define UI_GEOGRAPHICDATAPANEL_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_GeographicDataPanel
{
public:
    QVBoxLayout *mainLayout;
    QTabWidget *dataTabWidget;
    QWidget *userInfoTab;
    QVBoxLayout *userInfoLayout;
    QLabel *statusLabel;
    QTableWidget *userInfoTable;
    QWidget *statisticsTab;
    QVBoxLayout *statisticsLayout;
    QLabel *totalRecordsLabel;
    QProgressBar *dataLoadProgress;
    QLabel *regionDistributionLabel;
    QLabel *coordinateRangeLabel;
    QLabel *timeDistributionLabel;
    QSpacerItem *statisticsSpacer;

    void setupUi(QWidget *GeographicDataPanel)
    {
        if (GeographicDataPanel->objectName().isEmpty())
            GeographicDataPanel->setObjectName(QString::fromUtf8("GeographicDataPanel"));
        GeographicDataPanel->resize(800, 300);
        mainLayout = new QVBoxLayout(GeographicDataPanel);
        mainLayout->setSpacing(5);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        mainLayout->setContentsMargins(8, 8, 8, 8);
        dataTabWidget = new QTabWidget(GeographicDataPanel);
        dataTabWidget->setObjectName(QString::fromUtf8("dataTabWidget"));
        userInfoTab = new QWidget();
        userInfoTab->setObjectName(QString::fromUtf8("userInfoTab"));
        userInfoLayout = new QVBoxLayout(userInfoTab);
        userInfoLayout->setSpacing(5);
        userInfoLayout->setObjectName(QString::fromUtf8("userInfoLayout"));
        userInfoLayout->setContentsMargins(5, 5, 5, 5);
        statusLabel = new QLabel(userInfoTab);
        statusLabel->setObjectName(QString::fromUtf8("statusLabel"));
        statusLabel->setAlignment(Qt::AlignCenter);

        userInfoLayout->addWidget(statusLabel);

        userInfoTable = new QTableWidget(userInfoTab);
        if (userInfoTable->columnCount() < 8)
            userInfoTable->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        userInfoTable->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        userInfoTable->setObjectName(QString::fromUtf8("userInfoTable"));
        userInfoTable->setAlternatingRowColors(true);
        userInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        userInfoTable->setSelectionMode(QAbstractItemView::SingleSelection);
        userInfoTable->setSortingEnabled(true);

        userInfoLayout->addWidget(userInfoTable);

        dataTabWidget->addTab(userInfoTab, QString());
        statisticsTab = new QWidget();
        statisticsTab->setObjectName(QString::fromUtf8("statisticsTab"));
        statisticsLayout = new QVBoxLayout(statisticsTab);
        statisticsLayout->setSpacing(8);
        statisticsLayout->setObjectName(QString::fromUtf8("statisticsLayout"));
        statisticsLayout->setContentsMargins(10, 10, 10, 10);
        totalRecordsLabel = new QLabel(statisticsTab);
        totalRecordsLabel->setObjectName(QString::fromUtf8("totalRecordsLabel"));
        QFont font;
        font.setPointSize(11);
        font.setBold(true);
        font.setWeight(75);
        totalRecordsLabel->setFont(font);

        statisticsLayout->addWidget(totalRecordsLabel);

        dataLoadProgress = new QProgressBar(statisticsTab);
        dataLoadProgress->setObjectName(QString::fromUtf8("dataLoadProgress"));
        dataLoadProgress->setValue(0);

        statisticsLayout->addWidget(dataLoadProgress);

        regionDistributionLabel = new QLabel(statisticsTab);
        regionDistributionLabel->setObjectName(QString::fromUtf8("regionDistributionLabel"));
        regionDistributionLabel->setWordWrap(true);

        statisticsLayout->addWidget(regionDistributionLabel);

        coordinateRangeLabel = new QLabel(statisticsTab);
        coordinateRangeLabel->setObjectName(QString::fromUtf8("coordinateRangeLabel"));
        coordinateRangeLabel->setWordWrap(true);

        statisticsLayout->addWidget(coordinateRangeLabel);

        timeDistributionLabel = new QLabel(statisticsTab);
        timeDistributionLabel->setObjectName(QString::fromUtf8("timeDistributionLabel"));
        timeDistributionLabel->setWordWrap(true);

        statisticsLayout->addWidget(timeDistributionLabel);

        statisticsSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        statisticsLayout->addItem(statisticsSpacer);

        dataTabWidget->addTab(statisticsTab, QString());

        mainLayout->addWidget(dataTabWidget);


        retranslateUi(GeographicDataPanel);

        dataTabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(GeographicDataPanel);
    } // setupUi

    void retranslateUi(QWidget *GeographicDataPanel)
    {
        GeographicDataPanel->setWindowTitle(QCoreApplication::translate("GeographicDataPanel", "\345\234\260\347\220\206\346\225\260\346\215\256\351\235\242\346\235\277", nullptr));
        GeographicDataPanel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "QWidget#GeographicDataPanel {\n"
"    background-color: rgba(255, 255, 255, 240);\n"
"    border: 2px solid rgba(70, 130, 180, 200);\n"
"    border-radius: 8px;\n"
"}\n"
"\n"
"QTabWidget::pane {\n"
"    border: 1px solid rgba(200, 200, 200, 180);\n"
"    background-color: rgba(255, 255, 255, 200);\n"
"    border-radius: 4px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: rgba(240, 240, 240, 200);\n"
"    border: 1px solid rgba(200, 200, 200, 180);\n"
"    padding: 8px 16px;\n"
"    margin-right: 2px;\n"
"    font-weight: bold;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background-color: rgba(255, 255, 255, 220);\n"
"    border-bottom: 1px solid rgba(255, 255, 255, 220);\n"
"}\n"
"\n"
"QTabBar::tab:hover {\n"
"    background-color: rgba(230, 240, 250, 200);\n"
"}\n"
"\n"
"QTableWidget {\n"
"    background-color: rgba(255, 255, 255, 220);\n"
"    alternate-background-color: rgba(248, 248, 248, 200);\n"
"    gridline-color: rg"
                        "ba(220, 220, 220, 180);\n"
"    border: 1px solid rgba(200, 200, 200, 150);\n"
"    border-radius: 4px;\n"
"    selection-background-color: rgba(70, 130, 180, 100);\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"    padding: 4px 8px;\n"
"    border: none;\n"
"}\n"
"\n"
"QTableWidget::item:selected {\n"
"    background-color: rgba(70, 130, 180, 120);\n"
"    color: white;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: rgba(240, 240, 240, 200);\n"
"    padding: 6px 8px;\n"
"    border: 1px solid rgba(200, 200, 200, 150);\n"
"    font-weight: bold;\n"
"    color: #333;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #333;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QProgressBar {\n"
"    border: 1px solid rgba(200, 200, 200, 180);\n"
"    border-radius: 3px;\n"
"    background-color: rgba(240, 240, 240, 180);\n"
"    text-align: center;\n"
"}\n"
"\n"
"QProgressBar::chunk {\n"
"    background-color: rgba(76, 175, 80, 200);\n"
"    border-radius: 2px;\n"
"}", nullptr));
        statusLabel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "color: #27ae60; font-size: 11px; padding: 3px;", nullptr));
        statusLabel->setText(QCoreApplication::translate("GeographicDataPanel", "\346\255\243\345\234\250\345\212\240\350\275\275\347\224\250\346\210\267\344\277\241\346\201\257...", nullptr));
        QTableWidgetItem *___qtablewidgetitem = userInfoTable->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("GeographicDataPanel", "ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = userInfoTable->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("GeographicDataPanel", "\347\263\273\347\273\237\347\261\273\345\236\213", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = userInfoTable->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("GeographicDataPanel", "UE\347\261\273\345\210\253", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = userInfoTable->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("GeographicDataPanel", "\351\207\215\350\246\201\347\272\247\345\210\253", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = userInfoTable->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("GeographicDataPanel", "\345\274\200\345\247\213\346\227\266\351\227\264", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = userInfoTable->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("GeographicDataPanel", "\347\273\217\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = userInfoTable->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("GeographicDataPanel", "\347\272\254\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = userInfoTable->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("GeographicDataPanel", "\345\210\233\345\273\272\346\227\266\351\227\264", nullptr));
        dataTabWidget->setTabText(dataTabWidget->indexOf(userInfoTab), QCoreApplication::translate("GeographicDataPanel", "\347\224\250\346\210\267\344\277\241\346\201\257", nullptr));
        totalRecordsLabel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "color: #2c3e50;", nullptr));
        totalRecordsLabel->setText(QCoreApplication::translate("GeographicDataPanel", "\346\200\273\350\256\260\345\275\225\346\225\260: 0", nullptr));
        dataLoadProgress->setFormat(QCoreApplication::translate("GeographicDataPanel", "\346\225\260\346\215\256\345\212\240\350\275\275\350\277\233\345\272\246: %p%", nullptr));
        regionDistributionLabel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "color: #555; font-size: 11px; padding: 6px; \n"
"border: 1px solid rgba(220, 220, 220, 180); \n"
"border-radius: 4px; \n"
"background-color: rgba(250, 250, 250, 180);", nullptr));
        regionDistributionLabel->setText(QCoreApplication::translate("GeographicDataPanel", "\345\234\260\347\220\206\345\214\272\345\237\237\345\210\206\345\270\203: \346\232\202\346\227\240\346\225\260\346\215\256", nullptr));
        coordinateRangeLabel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "color: #555; font-size: 11px; padding: 6px; \n"
"border: 1px solid rgba(220, 220, 220, 180); \n"
"border-radius: 4px; \n"
"background-color: rgba(250, 250, 250, 180);", nullptr));
        coordinateRangeLabel->setText(QCoreApplication::translate("GeographicDataPanel", "\345\235\220\346\240\207\350\214\203\345\233\264: \346\232\202\346\227\240\346\225\260\346\215\256", nullptr));
        timeDistributionLabel->setStyleSheet(QCoreApplication::translate("GeographicDataPanel", "color: #555; font-size: 11px; padding: 6px; \n"
"border: 1px solid rgba(220, 220, 220, 180); \n"
"border-radius: 4px; \n"
"background-color: rgba(250, 250, 250, 180);", nullptr));
        timeDistributionLabel->setText(QCoreApplication::translate("GeographicDataPanel", "\346\227\266\351\227\264\345\210\206\345\270\203\347\273\237\350\256\241: \346\232\202\346\227\240\346\225\260\346\215\256", nullptr));
        dataTabWidget->setTabText(dataTabWidget->indexOf(statisticsTab), QCoreApplication::translate("GeographicDataPanel", "\347\273\237\350\256\241\344\277\241\346\201\257", nullptr));
    } // retranslateUi

};

namespace Ui {
    class GeographicDataPanel: public Ui_GeographicDataPanel {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_GEOGRAPHICDATAPANEL_H
