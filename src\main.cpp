#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QPixmap>
#include <QPainter>
#include <QFont>
#include <QFileInfo>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("LiteAPPStar");
    app.setApplicationVersion("0.4.0");
    app.setOrganizationName("LiteAPPStar Team");
    app.setOrganizationDomain("liteappstar.com");

    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/app_icon.png"));

    // 检查瓦片目录
    QString projectDir = QApplication::applicationDirPath();
    QString tilesDir = projectDir + "/../../resources/map/tiles";

    qDebug() << "Main: 应用程序目录:" << projectDir;
    qDebug() << "Main: 瓦片目录:" << tilesDir;

    QDir dir(tilesDir);
    if (dir.exists())
    {
        qDebug() << "Main: 瓦片目录存在，准备加载地图";
    }
    else
    {
        qDebug() << "Main: 瓦片目录不存在，尝试当前目录";
        tilesDir = QDir::currentPath() + "/resources/map/tiles";
        QDir currentDir(tilesDir);
        if (currentDir.exists())
        {
            qDebug() << "Main: 在当前目录找到瓦片:" << tilesDir;
        }
        else
        {
            qDebug() << "Main: 警告 - 未找到瓦片目录";
        }
    }

    // 创建主窗口
    MainWindow window;
    window.show();

    return app.exec();
}
