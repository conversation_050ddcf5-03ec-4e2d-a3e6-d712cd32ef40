{"version": "1.0.0", "description": "LiteAPPStar数据库配置文件", "current_environment": "development", "environments": {"development": {"name": "开发环境", "database": {"type": "QMYSQL", "host": "127.0.0.1", "port": 3306, "database_name": "test", "username": "root", "password": "123456", "table_name": "target_position", "charset": "utf8mb4", "timezone": "+08:00"}, "connection_pool": {"max_connections": 10, "min_connections": 2, "connection_timeout": 30, "idle_timeout": 300, "retry_attempts": 3, "retry_delay": 1000}, "options": {"auto_reconnect": true, "ssl_mode": "disabled", "connect_timeout": 10, "read_timeout": 30, "write_timeout": 30, "enable_logging": true, "log_level": "debug"}}, "testing": {"name": "测试环境", "database": {"type": "QMYSQL", "host": "*************", "port": 3306, "database_name": "test_db", "username": "test_user", "password": "test_password", "table_name": "target_position_test", "charset": "utf8mb4", "timezone": "+08:00"}, "connection_pool": {"max_connections": 5, "min_connections": 1, "connection_timeout": 20, "idle_timeout": 180, "retry_attempts": 2, "retry_delay": 2000}, "options": {"auto_reconnect": true, "ssl_mode": "preferred", "connect_timeout": 15, "read_timeout": 25, "write_timeout": 25, "enable_logging": true, "log_level": "info"}}, "sqlite_test": {"name": "SQLite测试环境", "database": {"type": "QSQLITE", "host": "", "port": 0, "database_name": "test.db", "username": "", "password": "", "table_name": "target_position", "charset": "utf8", "timezone": "+08:00"}, "connection_pool": {"max_connections": 5, "min_connections": 1, "connection_timeout": 10, "idle_timeout": 60, "retry_attempts": 2, "retry_delay": 1000}, "options": {"auto_reconnect": true, "ssl_mode": "disabled", "connect_timeout": 5, "read_timeout": 10, "write_timeout": 10, "enable_logging": true, "log_level": "debug"}}, "production": {"name": "生产环境", "database": {"type": "QMYSQL", "host": "prod-db.company.com", "port": 3306, "database_name": "liteappstar_prod", "username": "prod_user", "password": "secure_prod_password", "table_name": "target_position", "charset": "utf8mb4", "timezone": "+08:00"}, "connection_pool": {"max_connections": 20, "min_connections": 5, "connection_timeout": 60, "idle_timeout": 600, "retry_attempts": 5, "retry_delay": 3000}, "options": {"auto_reconnect": true, "ssl_mode": "required", "connect_timeout": 30, "read_timeout": 60, "write_timeout": 60, "enable_logging": true, "log_level": "warning"}}}, "fallback": {"name": "默认配置", "description": "当配置文件读取失败时使用的默认配置", "database": {"type": "QMYSQL", "host": "127.0.0.1", "port": 3306, "database_name": "test", "username": "root", "password": "123456", "table_name": "target_position", "charset": "utf8mb4", "timezone": "+08:00"}, "connection_pool": {"max_connections": 5, "min_connections": 1, "connection_timeout": 30, "idle_timeout": 300, "retry_attempts": 3, "retry_delay": 1000}, "options": {"auto_reconnect": true, "ssl_mode": "disabled", "connect_timeout": 10, "read_timeout": 30, "write_timeout": 30, "enable_logging": false, "log_level": "error"}}, "validation": {"required_fields": ["database.type", "database.host", "database.port", "database.database_name", "database.username", "database.table_name"], "port_range": {"min": 1, "max": 65535}, "supported_database_types": ["QMYSQL", "QPSQL", "QSQLITE", "QODBC"], "supported_ssl_modes": ["disabled", "preferred", "required"], "supported_log_levels": ["debug", "info", "warning", "error"]}, "metadata": {"created_by": "LiteAPPStar Database Refactoring", "created_date": "2024-12-26", "last_modified": "2024-12-26", "schema_version": "1.0", "compatible_versions": ["1.0.0", "1.0.1"]}}