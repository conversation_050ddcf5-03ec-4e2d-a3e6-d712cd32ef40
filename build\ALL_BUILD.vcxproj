﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2EA0DB3C-3794-3BC3-AB4D-F19D69CA62C7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar2\include;E:\code\LiteAPPStar2\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar2\include;E:\code\LiteAPPStar2\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar2\include;E:\code\LiteAPPStar2\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar2\include;E:\code\LiteAPPStar2\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar2\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/code/LiteAPPStar2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-file E:/code/LiteAPPStar2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar2\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/code/LiteAPPStar2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-file E:/code/LiteAPPStar2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar2\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/code/LiteAPPStar2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-file E:/code/LiteAPPStar2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar2\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/code/LiteAPPStar2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-file E:/code/LiteAPPStar2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar2\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar2\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\code\LiteAPPStar2\build\ZERO_CHECK.vcxproj">
      <Project>{A8D17F1E-3CB0-3307-91D5-3138B4A3F90E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\code\LiteAPPStar2\build\LiteAPPStar.vcxproj">
      <Project>{7887CFA9-C773-33FF-8513-E01335E4B69D}</Project>
      <Name>LiteAPPStar</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>