/****************************************************************************
** Meta object code from reading C++ file 'waterfallplot_simple.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/waterfallplot_simple.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'waterfallplot_simple.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_WaterfallPlotSimple_t {
    QByteArrayData data[14];
    char stringdata0[186];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_WaterfallPlotSimple_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_WaterfallPlotSimple_t qt_meta_stringdata_WaterfallPlotSimple = {
    {
QT_MOC_LITERAL(0, 0, 19), // "WaterfallPlotSimple"
QT_MOC_LITERAL(1, 20, 21), // "frequencyRangeChanged"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 9), // "startFreq"
QT_MOC_LITERAL(4, 53, 7), // "endFreq"
QT_MOC_LITERAL(5, 61, 20), // "timeFrequencyClicked"
QT_MOC_LITERAL(6, 82, 9), // "frequency"
QT_MOC_LITERAL(7, 92, 4), // "time"
QT_MOC_LITERAL(8, 97, 9), // "amplitude"
QT_MOC_LITERAL(9, 107, 11), // "dataUpdated"
QT_MOC_LITERAL(10, 119, 9), // "clearData"
QT_MOC_LITERAL(11, 129, 20), // "setPurpleGreenScheme"
QT_MOC_LITERAL(12, 150, 16), // "setBlueRedScheme"
QT_MOC_LITERAL(13, 167, 18) // "setGrayscaleScheme"

    },
    "WaterfallPlotSimple\0frequencyRangeChanged\0"
    "\0startFreq\0endFreq\0timeFrequencyClicked\0"
    "frequency\0time\0amplitude\0dataUpdated\0"
    "clearData\0setPurpleGreenScheme\0"
    "setBlueRedScheme\0setGrayscaleScheme"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_WaterfallPlotSimple[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   49,    2, 0x06 /* Public */,
       5,    3,   54,    2, 0x06 /* Public */,
       9,    0,   61,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    0,   62,    2, 0x08 /* Private */,
      11,    0,   63,    2, 0x08 /* Private */,
      12,    0,   64,    2, 0x08 /* Private */,
      13,    0,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double, QMetaType::Double,    6,    7,    8,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void WaterfallPlotSimple::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<WaterfallPlotSimple *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->frequencyRangeChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->timeFrequencyClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3]))); break;
        case 2: _t->dataUpdated(); break;
        case 3: _t->clearData(); break;
        case 4: _t->setPurpleGreenScheme(); break;
        case 5: _t->setBlueRedScheme(); break;
        case 6: _t->setGrayscaleScheme(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (WaterfallPlotSimple::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaterfallPlotSimple::frequencyRangeChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (WaterfallPlotSimple::*)(double , double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaterfallPlotSimple::timeFrequencyClicked)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (WaterfallPlotSimple::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaterfallPlotSimple::dataUpdated)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject WaterfallPlotSimple::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_WaterfallPlotSimple.data,
    qt_meta_data_WaterfallPlotSimple,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *WaterfallPlotSimple::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WaterfallPlotSimple::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_WaterfallPlotSimple.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int WaterfallPlotSimple::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void WaterfallPlotSimple::frequencyRangeChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void WaterfallPlotSimple::timeFrequencyClicked(double _t1, double _t2, double _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void WaterfallPlotSimple::dataUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
