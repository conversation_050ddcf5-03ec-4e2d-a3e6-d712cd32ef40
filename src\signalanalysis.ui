<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SignalAnalysisWidget</class>
 <widget class="QWidget" name="SignalAnalysisWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>频谱分析</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #1a1a2e;
    color: #eee;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    font-size: 9pt;
}

QGroupBox {
    border: 2px solid #16213e;
    border-radius: 8px;
    margin-top: 1ex;
    font-weight: bold;
    color: #eee;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 0 8px 0 8px;
    color: #64a0ff;
}

QLabel {
    color: #eee;
    font-weight: normal;
}

QSpinBox, QDoubleSpinBox {
    background-color: #0f3460;
    border: 2px solid #16213e;
    border-radius: 4px;
    color: #eee;
    padding: 4px;
    min-width: 80px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #64a0ff;
}

QLineEdit {
    background-color: #0f3460;
    border: 2px solid #16213e;
    border-radius: 4px;
    color: #eee;
    padding: 4px;
    min-width: 80px;
}

QLineEdit:focus {
    border-color: #64a0ff;
}

QComboBox {
    background-color: #0f3460;
    border: 2px solid #16213e;
    border-radius: 4px;
    color: #eee;
    padding: 4px;
    min-width: 60px;
}

QComboBox:focus {
    border-color: #64a0ff;
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #eee;
    margin-right: 5px;
}

QPushButton {
    background-color: #533483;
    border: none;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    padding: 8px 16px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #6a4c93;
}

QPushButton:pressed {
    background-color: #4a2c73;
}

QPushButton:disabled {
    background-color: #2a2a3e;
    color: #666;
}

QSplitter::handle {
    background-color: #16213e;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 2px;
}</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>8</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <widget class="QGroupBox" name="frequencyControlGroup">
     <property name="title">
      <string>频率控制</string>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>9</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    color: rgb(200, 200, 200);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 5px;
    margin-top: 0.5ex;
    font-weight: bold;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 1px 5px 2px 5px;
    color: rgb(220, 220, 220);
}</string>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>130</height>
      </size>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>85</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="frequencyControlLayout">
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>12</number>
      </property>
      <property name="rightMargin">
       <number>12</number>
      </property>
      <property name="spacing">
       <number>8</number>
      </property>
      <item>
       <widget class="QLabel" name="centerFreqLabel">
        <property name="text">
         <string>中心频率:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="centerFreqLineEdit">
        <property name="minimumSize">
         <size>
          <width>90</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>130</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>100.000</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
        <property name="styleSheet">
         <string>QLineEdit {
    background-color: #0f3460;
    border: 2px solid #16213e;
    border-radius: 4px;
    color: #eee;
    padding: 4px;
}
QLineEdit:focus {
    border-color: #64a0ff;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="centerFreqUnitCombo">
        <property name="minimumSize">
         <size>
          <width>55</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>75</width>
          <height>28</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QComboBox {
    background-color: rgb(45, 50, 65);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 4px;
    padding: 4px 8px;
    color: rgb(220, 220, 220);
    font-family: Microsoft YaHei;
    font-size: 9pt;
    font-weight: bold;
}

QComboBox:hover {
    border-color: rgb(120, 120, 140);
    background-color: rgb(50, 55, 70);
}

QComboBox:focus {
    border-color: rgb(140, 140, 160);
    background-color: rgb(55, 60, 75);
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid rgb(100, 100, 120);
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: rgb(60, 65, 80);
}

QComboBox::drop-down:hover {
    background-color: rgb(70, 75, 90);
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid rgb(200, 200, 200);
    width: 0px;
    height: 0px;
}

QComboBox QAbstractItemView {
    background-color: rgb(45, 50, 65);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 4px;
    color: rgb(220, 220, 220);
    selection-background-color: rgb(80, 85, 100);
    outline: none;
}

QComboBox QAbstractItemView::item {
    padding: 6px 8px;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background-color: rgb(70, 75, 90);
}

QComboBox QAbstractItemView::item:selected {
    background-color: rgb(80, 85, 100);
}</string>
        </property>
        <item>
         <property name="text">
          <string>Hz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>kHz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>MHz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>GHz</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="spanLabel">
        <property name="text">
         <string>Span:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="spanLineEdit">
        <property name="minimumSize">
         <size>
          <width>90</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>130</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>10.000</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
        <property name="styleSheet">
         <string>QLineEdit {
    background-color: #0f3460;
    border: 2px solid #16213e;
    border-radius: 4px;
    color: #eee;
    padding: 4px;
}
QLineEdit:focus {
    border-color: #64a0ff;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="spanUnitCombo">
        <property name="minimumSize">
         <size>
          <width>55</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>75</width>
          <height>28</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QComboBox {
    background-color: rgb(45, 50, 65);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 4px;
    padding: 4px 8px;
    color: rgb(220, 220, 220);
    font-family: Microsoft YaHei;
    font-size: 9pt;
    font-weight: bold;
}

QComboBox:hover {
    border-color: rgb(120, 120, 140);
    background-color: rgb(50, 55, 70);
}

QComboBox:focus {
    border-color: rgb(140, 140, 160);
    background-color: rgb(55, 60, 75);
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid rgb(100, 100, 120);
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: rgb(60, 65, 80);
}

QComboBox::drop-down:hover {
    background-color: rgb(70, 75, 90);
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid rgb(200, 200, 200);
    width: 0px;
    height: 0px;
}

QComboBox QAbstractItemView {
    background-color: rgb(45, 50, 65);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 4px;
    color: rgb(220, 220, 220);
    selection-background-color: rgb(80, 85, 100);
    outline: none;
}

QComboBox QAbstractItemView::item {
    padding: 6px 8px;
    border: none;
}

QComboBox QAbstractItemView::item:hover {
    background-color: rgb(70, 75, 90);
}

QComboBox QAbstractItemView::item:selected {
    background-color: rgb(80, 85, 100);
}</string>
        </property>
        <item>
         <property name="text">
          <string>Hz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>kHz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>MHz</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>GHz</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="rangeLabel">
        <property name="text">
         <string>频率范围:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="frequencyRangeLabel">
        <property name="text">
         <string>95.000 - 105.000 MHz</string>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    color: #64a0ff;
    font-weight: bold;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="presetISMBtn">
        <property name="text">
         <string>ISM 2.4G</string>
        </property>
        <property name="toolTip">
         <string>设置为ISM 2.4GHz频段 (2.4-2.5GHz)</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>75</width>
          <height>30</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="presetWiFiBtn">
        <property name="text">
         <string>WiFi 5G</string>
        </property>
        <property name="toolTip">
         <string>设置为WiFi 5GHz频段 (5.15-5.85GHz)</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>75</width>
          <height>30</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="presetFMBtn">
        <property name="text">
         <string>FM</string>
        </property>
        <property name="toolTip">
         <string>设置为FM广播频段 (88-108MHz)</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>55</width>
          <height>30</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="separatorLabel">
        <property name="text">
         <string>|</string>
        </property>
        <property name="styleSheet">
         <string>color: rgb(100, 100, 120); font-weight: bold;</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="dynamicDataBtn">
        <property name="text">
         <string>启动动态数据</string>
        </property>
        <property name="toolTip">
         <string>开启/关闭动态数据生成</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <property name="minimumSize">
         <size>
          <width>110</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: rgb(45, 50, 65);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 6px;
    padding: 6px 12px;
    color: rgb(220, 220, 220);
    font-family: Microsoft YaHei;
    font-size: 10pt;
    font-weight: bold;
}

QPushButton:hover {
    border-color: rgb(120, 120, 140);
    background-color: rgb(50, 55, 70);
}

QPushButton:checked {
    background-color: rgb(34, 139, 34);
    border-color: rgb(50, 205, 50);
    color: rgb(255, 255, 255);
}

QPushButton:checked:hover {
    background-color: rgb(50, 205, 50);
}</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QGroupBox" name="spectrumGroup">
      <property name="title">
       <string>频谱图</string>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>200</height>
       </size>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>3</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    color: rgb(200, 200, 200);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 5px;
    margin-top: 1.5ex;
    font-weight: bold;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 2px 5px 2px 5px;
    color: rgb(220, 220, 220);
}</string>
      </property>
      <layout class="QVBoxLayout" name="spectrumLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <item>
        <widget class="QWidget" name="spectrumDisplay">
         <property name="styleSheet">
          <string notr="true">QWidget {
    background-color: #0f1419;
    border: 2px solid #16213e;
    border-radius: 4px;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="waterfallGroup">
      <property name="title">
       <string>瀑布图</string>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>150</height>
       </size>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>2</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>9</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    color: rgb(200, 200, 200);
    border: 2px solid rgb(100, 100, 120);
    border-radius: 5px;
    margin-top: 1.5ex;
    font-weight: bold;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 2px 5px 2px 5px;
    color: rgb(220, 220, 220);
}</string>
      </property>
      <layout class="QVBoxLayout" name="waterfallLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <item>
        <widget class="QWidget" name="waterfallDisplay">
         <property name="styleSheet">
          <string notr="true">QWidget {
    background-color: #0f1419;
    border: 2px solid #16213e;
    border-radius: 4px;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="dataGroup">
      <property name="title">
       <string>信号列表</string>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>120</height>
       </size>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>2</verstretch>
       </sizepolicy>
      </property>
      <layout class="QVBoxLayout" name="dataLayout">
       <item>
        <widget class="QTableWidget" name="dataTable">
         <property name="styleSheet">
          <string notr="true">QTableWidget {
    background-color: #0f3460;
    border: 1px solid #16213e;
    border-radius: 4px;
    gridline-color: #2a3a5e;
    selection-background-color: #64a0ff;
    font-size: 10pt;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #2a3a5e;
    color: #f0f0f0;
    min-height: 20px;
}

QTableWidget::item:selected {
    background-color: #64a0ff;
    color: white;
}

QTableWidget::item:alternate {
    background-color: #1a2a4e;
}

QHeaderView::section {
    background-color: #1a1a2e;
    color: #64a0ff;
    padding: 10px;
    border: 1px solid #16213e;
    font-weight: bold;
    font-size: 10pt;
    min-height: 25px;
}

QHeaderView::section:horizontal {
    border-top: none;
}

QHeaderView::section:vertical {
    border-left: none;
}</string>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
