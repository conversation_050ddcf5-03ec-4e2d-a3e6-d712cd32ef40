# LiteAPPStar v0.4.0

一个基于Qt5的跨平台地理信息系统应用程序，专注于地图显示、测距功能和地理数据分析。

## 🎯 主要功能

### 地图功能
- **瓦片地图显示**: 支持多种地图瓦片格式
- **地图缩放**: 智能缩放控制，支持1-18级缩放
- **地图平移**: 流畅的地图拖拽和平移
- **坐标定位**: 精确的经纬度定位功能

### 测距功能 ⭐ v0.4.0核心功能
- **智能测距**: 点击地图进行距离测量
- **自适应显示**: 线条粗细根据缩放级别智能调整
- **坐标一致性**: 确保测距线在任何缩放级别下都连接相同地理位置
- **动态标签**: 距离标签字体大小自动适应缩放级别

### 目标管理
- **目标添加**: 支持多种类型目标标记
- **轨迹显示**: 目标间轨迹线显示
- **图层管理**: 灵活的图层显示控制

### 地理数据
- **数据面板**: 浮动式地理数据面板
- **数据查询**: 支持地理数据的查询和筛选
- **数据定位**: 点击数据行自动定位到地图

## 🚀 v0.4.0 新特性

### 地图缩放自适应
- 完全解决了测距线和轨迹线在地图缩放时的显示问题
- 智能线条粗细算法：`lineWidth = qMax(1, qMin(3, (zoomLevel - 5) / 4 + 2))`
- 动态字体调整：`fontSize = qMax(8, qMin(14, zoomLevel / 2 + 6))`
- 自适应包围矩形边距，防止显示裁剪

### 坐标系统优化
- 修复了地图缩放后测距线坐标点变化的问题
- 属性优先机制确保paintEvent使用最新坐标
- 地理坐标与屏幕坐标的精确转换

### UI现代化
- 定位功能窗口全面现代化设计
- 目标菜单样式优化，使用渐变效果
- 改进的视觉反馈和交互体验

## 📋 系统要求

### 开发环境
- **Qt版本**: Qt 5.15 或更高版本
- **编译器**: 
  - Windows: MSVC 2019/2022 或 MinGW
  - Linux: GCC 9+ 或 Clang 10+
  - macOS: Xcode 12+
- **CMake**: 3.20 或更高版本
- **C++标准**: C++17

### 运行环境
- **操作系统**: Windows 10/11, Ubuntu 20.04+, macOS 10.15+
- **内存**: 最少 4GB RAM
- **存储**: 至少 500MB 可用空间
- **显卡**: 支持OpenGL 3.3+

## 🔧 构建说明

### 使用CMake构建（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd LiteAPPStar

# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
cmake --build . --config Release

# 运行
./bin/LiteAPPStar
```

### 使用qmake构建

```bash
# 生成Makefile
qmake LiteAPPStar.pro CONFIG+=release

# 编译
make -j4

# 运行
./bin/LiteAPPStar
```

### Windows批处理构建

```cmd
# 调试版本
build.bat

# 发布版本
build.bat -r

# 清理构建文件
build.bat -c
```

## 📁 项目结构

```
LiteAPPStar/
├── src/                    # 源代码
│   ├── main.cpp           # 程序入口
│   ├── mainwindow.*       # 主窗口
│   ├── qgismapwidget.*    # 地图组件
│   ├── tilemapview.*      # 瓦片地图视图
│   ├── localtilemanager.* # 本地瓦片管理
│   └── ...
├── include/               # 头文件
├── resources/             # 资源文件
│   ├── icons/            # 图标
│   ├── images/           # 图片
│   ├── map/              # 地图瓦片
│   └── resources.qrc     # Qt资源文件
├── config/               # 配置文件
├── third_party/          # 第三方库
├── CMakeLists.txt        # CMake配置
├── LiteAPPStar.pro       # qmake配置
└── README.md             # 项目说明
```

## 🎮 使用说明

### 基本操作
1. **地图导航**: 使用鼠标拖拽平移地图，滚轮缩放
2. **测距功能**: 点击工具栏测距按钮，然后在地图上点击两点进行测距
3. **目标添加**: 使用目标工具添加不同类型的标记点
4. **定位功能**: 点击定位按钮输入经纬度坐标进行精确定位

### 高级功能
- **图层管理**: 通过图层面板控制不同图层的显示/隐藏
- **地理数据**: 使用地理数据面板查看和查询地理信息
- **轨迹显示**: 查看目标间的连接轨迹

## 🐛 已知问题

- 某些高DPI显示器上可能存在界面缩放问题
- 大量瓦片加载时可能出现内存占用较高的情况

## 🔄 版本历史

- **v0.4.0** (2025-01-27): 地图缩放自适应和线条显示优化
- **v0.3** (2024-07-18): 综合阅览界面完善
- **v0.2** (2024-07-18): 简化界面布局
- **v0.1** (2024-07-18): 初始版本

详细更新日志请查看 [RELEASE_NOTES_v0.4.md](RELEASE_NOTES_v0.4.md)

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

---

**LiteAPPStar** - 让地理信息系统更简单、更强大！
