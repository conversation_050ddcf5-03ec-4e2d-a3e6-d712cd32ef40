<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ComprehensiveViewWidget</class>
 <widget class="QWidget" name="ComprehensiveViewWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>综合阅览 - 数据查询与展示</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="childrenCollapsible">
      <bool>false</bool>
     </property>
     <property name="handleWidth">
      <number>1</number>
     </property>
     <widget class="QGroupBox" name="queryGroupBox">
      <property name="title">
       <string/>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>180</height>
       </size>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    border: none;
    background-color: #f8f9fa;
    margin: 0px;
    padding: 0px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="queryMainLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWidget" name="queryTitleBar">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>35</height>
          </size>
         </property>
         <property name="styleSheet">
          <string>QWidget {
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}</string>
         </property>
         <layout class="QHBoxLayout" name="queryTitleLayout">
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="queryTitleLabel">
            <property name="text">
             <string>查询条件设置</string>
            </property>
            <property name="styleSheet">
             <string>QLabel {
    font-weight: bold;
    font-size: 13px;
    color: #495057;
    background: transparent;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="queryTitleSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="queryContentArea">
         <property name="styleSheet">
          <string>QWidget { background-color: #ffffff; }</string>
         </property>
         <layout class="QVBoxLayout" name="queryContentLayout">
          <property name="spacing">
           <number>10</number>
          </property>
          <property name="leftMargin">
           <number>12</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>12</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="firstRowLayout">
            <property name="spacing">
             <number>10</number>
            </property>
            <item>
             <widget class="QLabel" name="timeLabel">
              <property name="text">
               <string>时间范围:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDateTimeEdit" name="startTimeEdit">
              <property name="displayFormat">
               <string>yyyy-MM-dd hh:mm:ss</string>
              </property>
              <property name="calendarPopup">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="toLabel">
              <property name="text">
               <string>至</string>
              </property>
              <property name="styleSheet">
               <string>font-weight: bold; color: #7f8c8d; font-size: 11px;</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDateTimeEdit" name="endTimeEdit">
              <property name="displayFormat">
               <string>yyyy-MM-dd hh:mm:ss</string>
              </property>
              <property name="calendarPopup">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="commLabel">
              <property name="text">
               <string>通讯体制:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="commBBtn">
              <property name="text">
               <string>B</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }
QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #229954; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="commGBtn">
              <property name="text">
               <string>G</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }
QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #229954; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="commIBtn">
              <property name="text">
               <string>I</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }
QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #229954; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="commTBtn">
              <property name="text">
               <string>T</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 20px; font-weight: bold; }
QPushButton:checked { background: #27ae60; color: white; border-color: #229954; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #229954; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="beamLabel">
              <property name="text">
               <string>波束范围:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="beamEdit">
              <property name="placeholderText">
               <string>输入波束范围</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>140</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="mapSelectorBtn">
              <property name="text">
               <string>地图选择器</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>110</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background: #E3F2FD; color: #1976D2; border: 1px solid #BBDEFB; border-radius: 4px; padding: 4px 8px; font-size: 11px; font-weight: bold; }
QPushButton:hover { background: #BBDEFB; }
QPushButton:pressed { background: #90CAF9; }</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="firstRowSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="secondRowLayout">
            <property name="spacing">
             <number>10</number>
            </property>
            <item>
             <widget class="QLabel" name="terminalTypeLabel">
              <property name="text">
               <string>终端类型:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="aircraftBtn">
              <property name="text">
               <string>飞机</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }
QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #2980b9; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="shipBtn">
              <property name="text">
               <string>船载</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }
QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #2980b9; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="vehicleBtn">
              <property name="text">
               <string>车载</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 30px; font-weight: bold; }
QPushButton:checked { background: #3498db; color: white; border-color: #2980b9; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #2980b9; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="imsxLabel">
              <property name="text">
               <string>IMSX:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="targetIdEdit">
              <property name="placeholderText">
               <string>输入IMSX</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="imexLabel">
              <property name="text">
               <string>IMEX:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="imexEdit">
              <property name="placeholderText">
               <string>输入IMEX</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="numberLabel">
              <property name="text">
               <string>号码:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 50px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="numberEdit">
              <property name="placeholderText">
               <string>输入号码</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>110</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="importanceLabel">
              <property name="text">
               <string>重要性级别:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 80px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="statusCombo">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>110</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QComboBox {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 10px;
    background-color: white;
    font-weight: normal;
}
QComboBox:hover {
    border-color: #adb5bd;
}
QComboBox:focus {
    border-color: #007bff;
}
QComboBox::drop-down {
    border: none;
    width: 16px;
}
QComboBox::down-arrow {
    image: none;
    border: none;
    width: 0px;
    height: 0px;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 3px solid #495057;
}</string>
              </property>
              <item>
               <property name="text">
                <string>全部</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>级别1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>级别2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>级别3</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <spacer name="secondRowSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="thirdRowLayout">
            <property name="spacing">
             <number>10</number>
            </property>
            <item>
             <widget class="QLabel" name="businessLabel">
              <property name="text">
               <string>业务类型:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; font-weight: bold; color: #34495e; padding: 0px; min-width: 60px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="voiceBtn">
              <property name="text">
               <string>话音</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }
QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #d35400; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="ipBtn">
              <property name="text">
               <string>IP</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }
QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #d35400; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="smsBtn">
              <property name="text">
               <string>短消息</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="styleSheet">
               <string>QPushButton { font-size: 10px; padding: 3px 8px; border: 1px solid #bdc3c7; border-radius: 3px; background: #ecf0f1; color: #2c3e50; min-width: 40px; font-weight: bold; }
QPushButton:checked { background: #e67e22; color: white; border-color: #d35400; }
QPushButton:hover { background: #d5dbdb; }
QPushButton:checked:hover { background: #d35400; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="callTypeLabel">
              <property name="text">
               <string>呼叫类型:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="callTypeCombo">
              <property name="minimumSize">
               <size>
                <width>75</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>95</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QComboBox {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 10px;
    background-color: white;
    font-weight: normal;
}
QComboBox:hover {
    border-color: #adb5bd;
}
QComboBox:focus {
    border-color: #007bff;
}
QComboBox::drop-down {
    border: none;
    width: 16px;
}
QComboBox::down-arrow {
    image: none;
    border: none;
    width: 0px;
    height: 0px;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 3px solid #495057;
}</string>
              </property>
              <item>
               <property name="text">
                <string>全部</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>呼入</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>呼出</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>未知</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="phoneLabel">
              <property name="text">
               <string>号码:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="phoneEdit">
              <property name="placeholderText">
               <string>输入号码</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>110</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="ipLabel">
              <property name="text">
               <string>IP地址:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 11px; font-weight: bold; color: #34495e; padding: 0px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="ipAddressEdit">
              <property name="placeholderText">
               <string>输入IP地址</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>140</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QLineEdit { border: 1px solid #dee2e6; border-radius: 4px; padding: 4px; font-size: 11px; }
QLineEdit::placeholder { color: #6c757d; font-size: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="thirdRowSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="buttonRowLayout">
            <property name="spacing">
             <number>8</number>
            </property>
            <item>
             <widget class="QPushButton" name="queryButton">
              <property name="text">
               <string>查询</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background-color: #28a745; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }
QPushButton:hover { background-color: #218838; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="resetButton">
              <property name="text">
               <string>重置</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background-color: #fd7e14; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }
QPushButton:hover { background-color: #e8690b; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="refreshButton">
              <property name="text">
               <string>刷新</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background-color: #007bff; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }
QPushButton:hover { background-color: #0056b3; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="columnSettingsButton">
              <property name="text">
               <string>列设置</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background-color: #6f42c1; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }
QPushButton:hover { background-color: #5a32a3; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="exportButton">
              <property name="text">
               <string>导出</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton { background-color: #6c757d; color: white; font-weight: bold; padding: 8px 16px; border: none; border-radius: 4px; font-size: 10px; }
QPushButton:hover { background-color: #545b62; }</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="buttonRowSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="resultGroupBox">
      <property name="title">
       <string/>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    border: none;
    background-color: #ffffff;
    margin: 0px;
    padding: 0px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="resultMainLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWidget" name="resultContentArea">
         <property name="styleSheet">
          <string>QWidget { background-color: #ffffff; }</string>
         </property>
         <layout class="QVBoxLayout" name="resultLayout">
          <property name="spacing">
           <number>10</number>
          </property>
          <property name="leftMargin">
           <number>15</number>
          </property>
          <property name="topMargin">
           <number>15</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>15</number>
          </property>
          <item>
           <widget class="QProgressBar" name="progressBar">
            <property name="visible">
             <bool>false</bool>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>20</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QTableView" name="tableView">
            <property name="styleSheet">
             <string>QTableView {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
    gridline-color: #f1f3f4;
    font-size: 11px;
    selection-background-color: #e3f2fd;
    alternate-background-color: #fafafa;
    outline: none;
}
QTableView::item {
    padding: 10px 8px;
    border-bottom: 1px solid #f1f3f4;
    border-right: 1px solid #f1f3f4;
}
QTableView::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4fc3f7, stop:1 #29b6f6);
    color: white;
    border: none;
}
QTableView::item:hover {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
}
QTableView::item:alternate {
    background-color: #fafafa;
}
QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f5f5f5);
    border: 1px solid #e0e0e0;
    border-left: none;
    padding: 10px 8px;
    font-weight: 600;
    font-size: 11px;
    color: #424242;
    text-align: left;
}
QHeaderView::section:first {
    border-left: 1px solid #e0e0e0;
    border-top-left-radius: 8px;
}
QHeaderView::section:last {
    border-top-right-radius: 8px;
}
QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f5f5f5, stop:1 #eeeeee);
}
/* 垂直滚动条样式 */
QScrollBar:vertical {
    background-color: #f5f5f5;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}
QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #d0d0d0, stop:1 #c0c0c0);
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}
QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #a0a0a0, stop:1 #909090);
}
QScrollBar::handle:vertical:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #808080, stop:1 #707070);
}
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}
QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: transparent;
}
/* 水平滚动条样式 */
QScrollBar:horizontal {
    background-color: #f5f5f5;
    height: 12px;
    border-radius: 6px;
    margin: 0px;
}
QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d0d0d0, stop:1 #c0c0c0);
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}
QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a0a0a0, stop:1 #909090);
}
QScrollBar::handle:horizontal:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #808080, stop:1 #707070);
}
QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
    background: transparent;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="paginationLayout">
            <item>
             <widget class="QPushButton" name="firstPageButton">
              <property name="text">
               <string>首页</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 10px;
    font-weight: 500;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
    border-color: #adb5bd;
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);
    transform: translateY(0px);
}
QPushButton:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="previousPageButton">
              <property name="text">
               <string>上一页</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 10px;
    font-weight: 500;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
    border-color: #adb5bd;
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);
    transform: translateY(0px);
}
QPushButton:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="nextPageButton">
              <property name="text">
               <string>下一页</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 10px;
    font-weight: 500;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
    border-color: #adb5bd;
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);
    transform: translateY(0px);
}
QPushButton:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="lastPageButton">
              <property name="text">
               <string>末页</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 10px;
    font-weight: 500;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
    border-color: #adb5bd;
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e9ecef, stop:1 #dee2e6);
    transform: translateY(0px);
}
QPushButton:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="separator1">
              <property name="maximumSize">
               <size>
                <width>1</width>
                <height>20</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QFrame { background-color: #dee2e6; margin: 0px 10px; }</string>
              </property>
              <property name="frameShape">
               <enum>QFrame::VLine</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="goToPageLabel">
              <property name="text">
               <string>跳转到:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; color: #495057; margin-left: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="pageNumberEdit">
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="placeholderText">
               <string>页码</string>
              </property>
              <property name="styleSheet">
               <string>QLineEdit {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 11px;
    background-color: white;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}
QLineEdit:focus {
    border-color: #007bff;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1), 0 0 0 2px rgba(0,123,255,0.25);
}
QLineEdit::placeholder {
    color: #6c757d;
    font-size: 10px;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="goToPageButton">
              <property name="text">
               <string>跳转</string>
              </property>
              <property name="styleSheet">
               <string>QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4dabf7, stop:1 #007bff);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 10px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #339af0, stop:1 #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,123,255,0.4);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1c7ed6, stop:1 #004085);
    transform: translateY(0px);
    box-shadow: 0 1px 2px rgba(0,123,255,0.2);
}
QPushButton:disabled {
    background-color: #6c757d;
    box-shadow: none;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="pageInfoLabel">
              <property name="text">
               <string>第 1 页，共 1 页</string>
              </property>
              <property name="styleSheet">
               <string>QLabel {
    font-size: 11px;
    color: #495057;
    margin: 0px 15px;
    padding: 4px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-weight: 500;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="recordCountLabel">
              <property name="text">
               <string>共 0 条记录</string>
              </property>
              <property name="styleSheet">
               <string>QLabel {
    font-size: 11px;
    color: #28a745;
    margin: 0px 15px;
    padding: 4px 8px;
    background-color: #d4edda;
    border-radius: 4px;
    font-weight: 600;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="pageSizeLabel">
              <property name="text">
               <string>每页显示:</string>
              </property>
              <property name="styleSheet">
               <string>QLabel { font-size: 12px; color: #495057; margin-left: 10px; }</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="pageSizeCombo">
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string>QComboBox {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 10px;
    background-color: white;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
QComboBox:hover {
    border-color: #adb5bd;
}
QComboBox:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}
QComboBox::drop-down {
    border: none;
    width: 20px;
}
QComboBox::down-arrow {
    image: none;
    border: none;
    width: 0px;
    height: 0px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #495057;
}</string>
              </property>
              <item>
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>20</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>50</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>100</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <spacer name="paginationSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
