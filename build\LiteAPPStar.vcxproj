﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8B9F59A2-C2D7-3402-B4CB-A6C9AE732FEA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>LiteAPPStar</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">LiteAPPStar.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">LiteAPPStar</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <LocalDebuggerEnvironment Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PATH=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin;%PATH%</LocalDebuggerEnvironment>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">LiteAPPStar.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">LiteAPPStar</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <LocalDebuggerEnvironment Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PATH=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin;%PATH%</LocalDebuggerEnvironment>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">LiteAPPStar.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">LiteAPPStar</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <LocalDebuggerEnvironment Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PATH=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin;%PATH%</LocalDebuggerEnvironment>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">LiteAPPStar.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">LiteAPPStar</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
    <LocalDebuggerEnvironment Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PATH=D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin;%PATH%</LocalDebuggerEnvironment>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /utf-8 /FS</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target LiteAPPStar</Message>
      <Command>setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autogen E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E touch E:/code/LiteAPPStar/build/LiteAPPStar_autogen/autouic_Debug.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QuickWidgetsd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Svgd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Sqld.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Quickd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QmlModelsd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Qmld.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Networkd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Widgetsd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Guid.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Cored.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmaind.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/code/LiteAPPStar/build/Debug/LiteAPPStar.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/code/LiteAPPStar/build/bin/Debug/LiteAPPStar.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /utf-8 /FS</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target LiteAPPStar</Message>
      <Command>setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autogen E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E touch E:/code/LiteAPPStar/build/LiteAPPStar_autogen/autouic_Release.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QuickWidgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Svg.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Sql.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Quick.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QmlModels.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Qml.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Network.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Widgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Gui.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Core.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmain.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/code/LiteAPPStar/build/Release/LiteAPPStar.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/code/LiteAPPStar/build/bin/Release/LiteAPPStar.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /utf-8 /FS</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target LiteAPPStar</Message>
      <Command>setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autogen E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E touch E:/code/LiteAPPStar/build/LiteAPPStar_autogen/autouic_MinSizeRel.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QuickWidgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Svg.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Sql.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Quick.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QmlModels.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Qml.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Network.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Widgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Gui.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Core.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmain.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/code/LiteAPPStar/build/MinSizeRel/LiteAPPStar.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/code/LiteAPPStar/build/bin/MinSizeRel/LiteAPPStar.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuick" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQmlModels" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQml" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtQuickWidgets" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSvg" /external:I "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /utf-8 /FS</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING;_SILENCE_ALL_MS_EXT_DEPRECATION_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_QUICK_LIB;QT_QMLMODELS_LIB;QT_QML_LIB;QT_QUICKWIDGETS_LIB;QT_SVG_LIB;QT_SQL_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo;E:\code\LiteAPPStar\include;E:\code\LiteAPPStar\build;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtNetwork;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuick;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQmlModels;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQml;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtQuickWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSvg;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target LiteAPPStar</Message>
      <Command>setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autogen E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E touch E:/code/LiteAPPStar/build/LiteAPPStar_autogen/autouic_RelWithDebInfo.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QuickWidgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Svg.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Sql.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Quick.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5QmlModels.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Qml.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Network.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Widgets.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Gui.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Core.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmain.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/code/LiteAPPStar/build/RelWithDebInfo/LiteAPPStar.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/code/LiteAPPStar/build/bin/RelWithDebInfo/LiteAPPStar.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar\build\CMakeFiles\6ced8156163e1d06b8bc9dabf4ea54b5\autouic_(CONFIG).stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\src\comprehensive_view.ui;E:\code\LiteAPPStar\src\geographicdatapanel.ui;E:\code\LiteAPPStar\src\mainwindow.ui;E:\code\LiteAPPStar\src\signalanalysis.ui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Debug.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\src\comprehensive_view.ui;E:\code\LiteAPPStar\src\geographicdatapanel.ui;E:\code\LiteAPPStar\src\mainwindow.ui;E:\code\LiteAPPStar\src\signalanalysis.ui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Release.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\src\comprehensive_view.ui;E:\code\LiteAPPStar\src\geographicdatapanel.ui;E:\code\LiteAPPStar\src\mainwindow.ui;E:\code\LiteAPPStar\src\signalanalysis.ui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_MinSizeRel.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\src\comprehensive_view.ui;E:\code\LiteAPPStar\src\geographicdatapanel.ui;E:\code\LiteAPPStar\src\mainwindow.ui;E:\code\LiteAPPStar\src\signalanalysis.ui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_RelWithDebInfo.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar\build\CMakeFiles\a949e9403c7fc38777131ca2bcb1be2d\qrc_resources.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\resources\resources.qrc;E:\code\LiteAPPStar\build\CMakeFiles\LiteAPPStar_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;E:\code\LiteAPPStar\resources\MapComponent.qml;E:\code\LiteAPPStar\resources\images\logo.png;E:\code\LiteAPPStar\resources\images\splash.png;E:\code\LiteAPPStar\resources\styles\default.qss;E:\code\LiteAPPStar\resources\styles\dark.qss;E:\code\LiteAPPStar\resources\translations\liteappstar_zh_CN.qm;E:\code\LiteAPPStar\resources\translations\liteappstar_en_US.qm;E:\code\LiteAPPStar\resources\icons\save.png;E:\code\LiteAPPStar\resources\icons\open.png;E:\code\LiteAPPStar\resources\icons\cut.png;E:\code\LiteAPPStar\resources\icons\app_icon.png;E:\code\LiteAPPStar\resources\icons\copy.png;E:\code\LiteAPPStar\resources\icons\exit.png;E:\code\LiteAPPStar\resources\icons\satellite.svg;E:\code\LiteAPPStar\resources\icons\new.png;E:\code\LiteAPPStar\resources\icons\paste.png;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\resources\resources.qrc;E:\code\LiteAPPStar\build\CMakeFiles\LiteAPPStar_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;E:\code\LiteAPPStar\resources\MapComponent.qml;E:\code\LiteAPPStar\resources\images\logo.png;E:\code\LiteAPPStar\resources\images\splash.png;E:\code\LiteAPPStar\resources\styles\default.qss;E:\code\LiteAPPStar\resources\styles\dark.qss;E:\code\LiteAPPStar\resources\translations\liteappstar_zh_CN.qm;E:\code\LiteAPPStar\resources\translations\liteappstar_en_US.qm;E:\code\LiteAPPStar\resources\icons\save.png;E:\code\LiteAPPStar\resources\icons\open.png;E:\code\LiteAPPStar\resources\icons\cut.png;E:\code\LiteAPPStar\resources\icons\app_icon.png;E:\code\LiteAPPStar\resources\icons\copy.png;E:\code\LiteAPPStar\resources\icons\exit.png;E:\code\LiteAPPStar\resources\icons\satellite.svg;E:\code\LiteAPPStar\resources\icons\new.png;E:\code\LiteAPPStar\resources\icons\paste.png;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\resources\resources.qrc;E:\code\LiteAPPStar\build\CMakeFiles\LiteAPPStar_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;E:\code\LiteAPPStar\resources\MapComponent.qml;E:\code\LiteAPPStar\resources\images\logo.png;E:\code\LiteAPPStar\resources\images\splash.png;E:\code\LiteAPPStar\resources\styles\default.qss;E:\code\LiteAPPStar\resources\styles\dark.qss;E:\code\LiteAPPStar\resources\translations\liteappstar_zh_CN.qm;E:\code\LiteAPPStar\resources\translations\liteappstar_en_US.qm;E:\code\LiteAPPStar\resources\icons\save.png;E:\code\LiteAPPStar\resources\icons\open.png;E:\code\LiteAPPStar\resources\icons\cut.png;E:\code\LiteAPPStar\resources\icons\app_icon.png;E:\code\LiteAPPStar\resources\icons\copy.png;E:\code\LiteAPPStar\resources\icons\exit.png;E:\code\LiteAPPStar\resources\icons\satellite.svg;E:\code\LiteAPPStar\resources\icons\new.png;E:\code\LiteAPPStar\resources\icons\paste.png;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\resources\resources.qrc;E:\code\LiteAPPStar\build\CMakeFiles\LiteAPPStar_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;E:\code\LiteAPPStar\resources\MapComponent.qml;E:\code\LiteAPPStar\resources\images\logo.png;E:\code\LiteAPPStar\resources\images\splash.png;E:\code\LiteAPPStar\resources\styles\default.qss;E:\code\LiteAPPStar\resources\styles\dark.qss;E:\code\LiteAPPStar\resources\translations\liteappstar_zh_CN.qm;E:\code\LiteAPPStar\resources\translations\liteappstar_en_US.qm;E:\code\LiteAPPStar\resources\icons\save.png;E:\code\LiteAPPStar\resources\icons\open.png;E:\code\LiteAPPStar\resources\icons\cut.png;E:\code\LiteAPPStar\resources\icons\app_icon.png;E:\code\LiteAPPStar\resources\icons\copy.png;E:\code\LiteAPPStar\resources\icons\exit.png;E:\code\LiteAPPStar\resources\icons\satellite.svg;E:\code\LiteAPPStar\resources\icons\new.png;E:\code\LiteAPPStar\resources\icons\paste.png;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\code\LiteAPPStar\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/code/LiteAPPStar/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-file E:/code/LiteAPPStar/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\code\LiteAPPStar\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/code/LiteAPPStar/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-file E:/code/LiteAPPStar/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\code\LiteAPPStar\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/code/LiteAPPStar/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-file E:/code/LiteAPPStar/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\code\LiteAPPStar\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/code/LiteAPPStar/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-file E:/code/LiteAPPStar/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeParseArguments.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeRCInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPack.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\CPackComponent.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Compiler\MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\Windows.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;D:\Program Files\cmake-3.28.1-windows-x86_64\share\cmake-3.28\Templates\CPackConfig.cmake.in;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5Config.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5\Qt5ModuleLocation.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigExtrasMkspecDir.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Core\Qt5CoreMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5GuiConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QGifPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICNSPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QICOPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QJpegPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QMinimalIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QOffscreenIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgIconPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QSvgPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTgaPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTiffPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QTuioTouchPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QVirtualKeyboardPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWbmpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebGLIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWebpPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QWindowsIntegrationPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Gui\Qt5Gui_QXdgDesktopPortalThemePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5NetworkConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Network\Qt5Network_QGenericEnginePlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5QmlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QDebugMessageServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QLocalClientConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebugServerFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlDebuggerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlInspectorServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugConnectorFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlNativeDebugServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlPreviewServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQmlProfilerServiceFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QQuickProfilerAdapterFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Qml\Qt5Qml_QTcpServerConnectionFactory.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QmlModels\Qt5QmlModelsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Quick\Qt5QuickConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5QuickWidgets\Qt5QuickWidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5SqlConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QODBCDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QPSQLDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Sql\Qt5Sql_QSQLiteDriverPlugin.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Svg\Qt5SvgConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfig.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigExtras.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsConfigVersion.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5WidgetsMacros.cmake;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\cmake\Qt5Widgets\Qt5Widgets_QWindowsVistaStylePlugin.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeCXXCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeRCCompiler.cmake;E:\code\LiteAPPStar\build\CMakeFiles\3.28.1\CMakeSystem.cmake;E:\code\LiteAPPStar\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\code\LiteAPPStar\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="E:\code\LiteAPPStar\src\comprehensiveviewwidget.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\configmanager.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\databasemanager.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\geographicdatapanel.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\geoinfowidget.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\localtilemanager.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\main.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\mainwindow.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\qgismapwidget.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\qtgeoinfowidget.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\qtlocationmapview.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\signalanalysiswidget.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\spectrumplot_simple.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\tilemapview.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\src\waterfallplot_simple.cpp" />
    <ClInclude Include="E:\code\LiteAPPStar\include\comprehensiveviewwidget.h" />
    <ClInclude Include="E:\code\LiteAPPStar\include\configmanager.h" />
    <ClInclude Include="E:\code\LiteAPPStar\include\databasemanager.h" />
    <ClInclude Include="E:\code\LiteAPPStar\include\geographicdatapanel.h" />
    <ClInclude Include="E:\code\LiteAPPStar\include\mainwindow.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\geoinfowidget.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\localtilemanager.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\qgismapwidget.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\qtgeoinfowidget.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\qtlocationmapview.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\signalanalysiswidget.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\spectrumplot_simple.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\tilemapview.h" />
    <ClInclude Include="E:\code\LiteAPPStar\src\waterfallplot_simple.h" />
    <None Include="E:\code\LiteAPPStar\src\comprehensive_view.ui">
    </None>
    <None Include="E:\code\LiteAPPStar\src\geographicdatapanel.ui">
    </None>
    <None Include="E:\code\LiteAPPStar\src\mainwindow.ui">
    </None>
    <None Include="E:\code\LiteAPPStar\src\signalanalysis.ui">
    </None>
    <None Include="E:\code\LiteAPPStar\resources\resources.qrc">
    </None>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_comprehensive_view.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_geographicdatapanel.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_mainwindow.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Debug\src\ui_signalanalysis.h" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Debug.stamp">
    </None>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\3YJK5W5UP7\qrc_resources.cpp" />
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_comprehensive_view.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_geographicdatapanel.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_mainwindow.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_Release\src\ui_signalanalysis.h" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_Release.stamp">
    </None>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_comprehensive_view.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_geographicdatapanel.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_mainwindow.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_MinSizeRel\src\ui_signalanalysis.h" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_MinSizeRel.stamp">
    </None>
    <ClCompile Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_comprehensive_view.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_geographicdatapanel.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_mainwindow.h" />
    <ClInclude Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\include_RelWithDebInfo\src\ui_signalanalysis.h" />
    <None Include="E:\code\LiteAPPStar\build\LiteAPPStar_autogen\autouic_RelWithDebInfo.stamp">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\code\LiteAPPStar\build\ZERO_CHECK.vcxproj">
      <Project>{2F6E34DC-373A-3151-8A7D-BD49B9C6DFC2}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>