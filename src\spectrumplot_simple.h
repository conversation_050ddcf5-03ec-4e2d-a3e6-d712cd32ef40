#ifndef SPECTRUMPLOT_SIMPLE_H
#define SPECTRUMPLOT_SIMPLE_H

#include <QWidget>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QVector>
#include <QColor>
#include <QFont>
#include <QFontMetrics>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QRandomGenerator>
#include <cmath>

/**
 * @brief 简化版频谱图显示组件
 *
 * 基于QPainter实现的轻量级频谱分析显示组件
 * 采用现代蓝紫色配色方案
 */
class SpectrumPlotSimple : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 数据保持模式枚举
     */
    enum HoldMode
    {
        NoHold = 0,  ///< 无保持
        MaxHold = 1, ///< 最大值保持
        MinHold = 2, ///< 最小值保持
        AvgHold = 3  ///< 平均值保持
    };

    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit SpectrumPlotSimple(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SpectrumPlotSimple();

    /**
     * @brief 设置频率范围
     * @param startFreq 起始频率 (Hz)
     * @param endFreq 结束频率 (Hz)
     */
    void setFrequencyRange(double startFreq, double endFreq);

    /**
     * @brief 设置幅度范围
     * @param minAmplitude 最小幅度 (dBm)
     * @param maxAmplitude 最大幅度 (dBm)
     */
    void setAmplitudeRange(double minAmplitude, double maxAmplitude);

    /**
     * @brief 更新频谱数据
     * @param frequencies 频率数组
     * @param amplitudes 幅度数组
     */
    void updateSpectrumData(const QVector<double> &frequencies, const QVector<double> &amplitudes);

    /**
     * @brief 设置数据保持模式
     * @param mode 保持模式
     */
    void setHoldMode(HoldMode mode);

    /**
     * @brief 清除保持数据
     */
    void clearHoldData();

    /**
     * @brief 获取当前频率范围
     * @return 频率范围 (起始频率, 结束频率)
     */
    QPair<double, double> frequencyRange() const;

    /**
     * @brief 获取当前幅度范围
     * @return 幅度范围 (最小幅度, 最大幅度)
     */
    QPair<double, double> amplitudeRange() const;

signals:
    /**
     * @brief 频率范围改变信号
     * @param startFreq 起始频率
     * @param endFreq 结束频率
     */
    void frequencyRangeChanged(double startFreq, double endFreq);

    /**
     * @brief 数据点选择信号
     * @param frequency 选择的频率
     * @param amplitude 选择的幅度
     */
    void dataPointSelected(double frequency, double amplitude);

protected:
    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;

    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标移动事件
     */
    void mouseMoveEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标释放事件
     */
    void mouseReleaseEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标滚轮事件
     */
    void wheelEvent(QWheelEvent *event) override;

    /**
     * @brief 右键菜单事件
     */
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    /**
     * @brief 自动缩放槽函数
     */
    void autoScale();

    /**
     * @brief 重置缩放槽函数
     */
    void resetZoom();

    /**
     * @brief 清除保持数据槽函数
     */
    void clearHold();

    /**
     * @brief 设置最大值保持
     */
    void setMaxHold();

    /**
     * @brief 设置最小值保持
     */
    void setMinHold();

    /**
     * @brief 设置平均值保持
     */
    void setAvgHold();

    /**
     * @brief 关闭保持功能
     */
    void setNoHold();

private:
    /**
     * @brief 绘制背景和网格
     * @param painter 绘制器
     */
    void drawBackground(QPainter &painter);

    /**
     * @brief 绘制坐标轴
     * @param painter 绘制器
     */
    void drawAxes(QPainter &painter);

    /**
     * @brief 绘制频谱曲线
     * @param painter 绘制器
     */
    void drawSpectrum(QPainter &painter);

    /**
     * @brief 绘制保持曲线
     * @param painter 绘制器
     */
    void drawHoldData(QPainter &painter);

    /**
     * @brief 频率转换为X坐标
     * @param frequency 频率
     * @return X坐标
     */
    int frequencyToX(double frequency) const;

    /**
     * @brief 幅度转换为Y坐标
     * @param amplitude 幅度
     * @return Y坐标
     */
    int amplitudeToY(double amplitude) const;

    /**
     * @brief X坐标转换为频率
     * @param x X坐标
     * @return 频率
     */
    double xToFrequency(int x) const;

    /**
     * @brief Y坐标转换为幅度
     * @param y Y坐标
     * @return 幅度
     */
    double yToAmplitude(int y) const;

    /**
     * @brief 格式化频率显示
     * @param frequency 频率值
     * @return 格式化的频率字符串
     */
    QString formatFrequency(double frequency) const;

    /**
     * @brief 更新保持数据
     * @param amplitudes 当前幅度数据
     */
    void updateHoldData(const QVector<double> &amplitudes);

    /**
     * @brief 更新保持功能菜单状态
     */
    void updateHoldMenuState();

private:
    // 数据存储
    QVector<double> m_frequencies; ///< 频率数据
    QVector<double> m_amplitudes;  ///< 幅度数据
    QVector<double> m_holdData;    ///< 保持数据
    QVector<double> m_avgBuffer;   ///< 平均值缓冲区

    // 显示范围
    double m_startFreq;    ///< 起始频率
    double m_endFreq;      ///< 结束频率
    double m_minAmplitude; ///< 最小幅度
    double m_maxAmplitude; ///< 最大幅度

    // 配置参数
    HoldMode m_holdMode; ///< 数据保持模式
    int m_avgCount;      ///< 平均计数

    // 绘图区域
    QRect m_plotRect;   ///< 绘图区域
    int m_marginLeft;   ///< 左边距
    int m_marginRight;  ///< 右边距
    int m_marginTop;    ///< 上边距
    int m_marginBottom; ///< 下边距

    // 右键菜单
    QMenu *m_contextMenu;       ///< 右键菜单
    QAction *m_autoScaleAction; ///< 自动缩放动作
    QAction *m_resetZoomAction; ///< 重置缩放动作
    QAction *m_clearHoldAction; ///< 清除保持动作
    QAction *m_maxHoldAction;   ///< 最大值保持动作
    QAction *m_minHoldAction;   ///< 最小值保持动作
    QAction *m_avgHoldAction;   ///< 平均值保持动作
    QAction *m_noHoldAction;    ///< 关闭保持动作

    // 鼠标交互
    bool m_dragging;           ///< 是否正在拖拽
    QPoint m_lastMousePos;     ///< 上次鼠标位置
    double m_dragStartFreq;    ///< 拖拽开始时的起始频率
    double m_dragStartEndFreq; ///< 拖拽开始时的结束频率

    // 样式配置
    static const QColor SPECTRUM_COLOR;   ///< 频谱线颜色
    static const QColor HOLD_COLOR;       ///< 保持线颜色
    static const QColor GRID_COLOR;       ///< 网格颜色
    static const QColor BACKGROUND_COLOR; ///< 背景颜色
    static const QColor TEXT_COLOR;       ///< 文本颜色
    static const QColor AXIS_COLOR;       ///< 坐标轴颜色
};

#endif // SPECTRUMPLOT_SIMPLE_H
