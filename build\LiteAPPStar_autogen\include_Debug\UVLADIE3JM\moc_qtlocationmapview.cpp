/****************************************************************************
** Meta object code from reading C++ file 'qtlocationmapview.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../src/qtlocationmapview.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qtlocationmapview.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QtLocationMapView_t {
    QByteArrayData data[28];
    char stringdata0[365];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QtLocationMapView_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QtLocationMapView_t qt_meta_stringdata_QtLocationMapView = {
    {
QT_MOC_LITERAL(0, 0, 17), // "QtLocationMapView"
QT_MOC_LITERAL(1, 18, 10), // "mapClicked"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 8), // "latitude"
QT_MOC_LITERAL(4, 39, 9), // "longitude"
QT_MOC_LITERAL(5, 49, 16), // "mapDoubleClicked"
QT_MOC_LITERAL(6, 66, 13), // "centerChanged"
QT_MOC_LITERAL(7, 80, 11), // "zoomChanged"
QT_MOC_LITERAL(8, 92, 4), // "zoom"
QT_MOC_LITERAL(9, 97, 14), // "mapTypeChanged"
QT_MOC_LITERAL(10, 112, 7), // "mapType"
QT_MOC_LITERAL(11, 120, 13), // "markerClicked"
QT_MOC_LITERAL(12, 134, 8), // "markerId"
QT_MOC_LITERAL(13, 143, 6), // "zoomIn"
QT_MOC_LITERAL(14, 150, 7), // "zoomOut"
QT_MOC_LITERAL(15, 158, 9), // "resetView"
QT_MOC_LITERAL(16, 168, 12), // "fitToMarkers"
QT_MOC_LITERAL(17, 181, 20), // "setOpenStreetMapType"
QT_MOC_LITERAL(18, 202, 19), // "setSatelliteMapType"
QT_MOC_LITERAL(19, 222, 16), // "setHybridMapType"
QT_MOC_LITERAL(20, 239, 10), // "onMapReady"
QT_MOC_LITERAL(21, 250, 19), // "onZoomSliderChanged"
QT_MOC_LITERAL(22, 270, 5), // "value"
QT_MOC_LITERAL(23, 276, 21), // "onMapTypeComboChanged"
QT_MOC_LITERAL(24, 298, 4), // "text"
QT_MOC_LITERAL(25, 303, 25), // "onCenterCoordinateChanged"
QT_MOC_LITERAL(26, 329, 18), // "onQmlCenterChanged"
QT_MOC_LITERAL(27, 348, 16) // "onQmlZoomChanged"

    },
    "QtLocationMapView\0mapClicked\0\0latitude\0"
    "longitude\0mapDoubleClicked\0centerChanged\0"
    "zoomChanged\0zoom\0mapTypeChanged\0mapType\0"
    "markerClicked\0markerId\0zoomIn\0zoomOut\0"
    "resetView\0fitToMarkers\0setOpenStreetMapType\0"
    "setSatelliteMapType\0setHybridMapType\0"
    "onMapReady\0onZoomSliderChanged\0value\0"
    "onMapTypeComboChanged\0text\0"
    "onCenterCoordinateChanged\0onQmlCenterChanged\0"
    "onQmlZoomChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QtLocationMapView[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      19,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,  109,    2, 0x06 /* Public */,
       5,    2,  114,    2, 0x06 /* Public */,
       6,    2,  119,    2, 0x06 /* Public */,
       7,    1,  124,    2, 0x06 /* Public */,
       9,    1,  127,    2, 0x06 /* Public */,
      11,    3,  130,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    0,  137,    2, 0x0a /* Public */,
      14,    0,  138,    2, 0x0a /* Public */,
      15,    0,  139,    2, 0x0a /* Public */,
      16,    0,  140,    2, 0x0a /* Public */,
      17,    0,  141,    2, 0x0a /* Public */,
      18,    0,  142,    2, 0x0a /* Public */,
      19,    0,  143,    2, 0x0a /* Public */,
      20,    0,  144,    2, 0x08 /* Private */,
      21,    1,  145,    2, 0x08 /* Private */,
      23,    1,  148,    2, 0x08 /* Private */,
      25,    0,  151,    2, 0x08 /* Private */,
      26,    2,  152,    2, 0x08 /* Private */,
      27,    1,  157,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double,    8,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int, QMetaType::Double, QMetaType::Double,   12,    3,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   22,
    QMetaType::Void, QMetaType::QString,   24,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double, QMetaType::Double,    3,    4,
    QMetaType::Void, QMetaType::Double,    8,

       0        // eod
};

void QtLocationMapView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<QtLocationMapView *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->mapClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 1: _t->mapDoubleClicked((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 2: _t->centerChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 3: _t->zoomChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 4: _t->mapTypeChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->markerClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3]))); break;
        case 6: _t->zoomIn(); break;
        case 7: _t->zoomOut(); break;
        case 8: _t->resetView(); break;
        case 9: _t->fitToMarkers(); break;
        case 10: _t->setOpenStreetMapType(); break;
        case 11: _t->setSatelliteMapType(); break;
        case 12: _t->setHybridMapType(); break;
        case 13: _t->onMapReady(); break;
        case 14: _t->onZoomSliderChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 15: _t->onMapTypeComboChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onCenterCoordinateChanged(); break;
        case 17: _t->onQmlCenterChanged((*reinterpret_cast< double(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2]))); break;
        case 18: _t->onQmlZoomChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (QtLocationMapView::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::mapClicked)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (QtLocationMapView::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::mapDoubleClicked)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (QtLocationMapView::*)(double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::centerChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (QtLocationMapView::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::zoomChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (QtLocationMapView::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::mapTypeChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (QtLocationMapView::*)(int , double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&QtLocationMapView::markerClicked)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject QtLocationMapView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_QtLocationMapView.data,
    qt_meta_data_QtLocationMapView,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *QtLocationMapView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QtLocationMapView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QtLocationMapView.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int QtLocationMapView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 19)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 19;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 19)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 19;
    }
    return _id;
}

// SIGNAL 0
void QtLocationMapView::mapClicked(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void QtLocationMapView::mapDoubleClicked(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void QtLocationMapView::centerChanged(double _t1, double _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void QtLocationMapView::zoomChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void QtLocationMapView::mapTypeChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void QtLocationMapView::markerClicked(int _t1, double _t2, double _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
