#ifndef COMPREHENSIVEVIEWWIDGET_H
#define COMPREHENSIVEVIEWWIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class ComprehensiveViewWidget;
}

class GeographicInfoPanel;
QT_END_NAMESPACE
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QComboBox>
#include <QDateTimeEdit>
#include <QPushButton>
#include <QTableView>
#include <QTableWidget>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QSplitter>
#include <QProgressBar>
#include <QStatusBar>
#include <QCheckBox>
#include <QSpinBox>
#include <QSortFilterProxyModel>
#include "databasemanager.h"
#include <QMessageBox>
#include <QApplication>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QDialog>
#include <QListWidget>
#include <QCheckBox>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QSettings>

/**
 * @brief 综合阅览窗口类
 *
 * 提供数据库查询、结果展示、分页、排序、列管理等功能
 */
class ComprehensiveViewWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ComprehensiveViewWidget(QWidget *parent = nullptr);
    ~ComprehensiveViewWidget();

private slots:
    // 查询相关槽函数
    void onQueryButtonClicked();
    void onResetButtonClicked();
    void onRefreshButtonClicked();
    void onExportButtonClicked();

    // 分页相关槽函数
    void onFirstPageClicked();
    void onPreviousPageClicked();
    void onNextPageClicked();
    void onLastPageClicked();
    void onPageSizeChanged(int pageSize);
    void onGoToPageClicked();

    // 表格相关槽函数
    void onHeaderClicked(int logicalIndex);
    void onTableContextMenu(const QPoint &pos);
    void onColumnVisibilityChanged();
    void onColumnOrderChanged();

    // 数据库相关槽函数
    void onDatabaseConnected();
    void onDatabaseError(const QString &error);

private:
    // 初始化函数
    void initializeUI();
    void initializeUIComponents();
    void initializeUIReferences();
    void initializeDatabase();
    // void setupQueryArea();      // 不再需要，使用UI文件
    // void setupResultArea();     // 不再需要，使用UI文件
    // void setupPaginationArea(); // 不再需要，使用UI文件
    void connectSignals();

    // 数据库操作函数
    bool connectToDatabase();
    bool testDirectMySQLConnection();
    void disconnectFromDatabase();
    void executeQuery();
    void updateTableModel(const QueryResult &result);
    void updatePaginationInfo();
    void insertTestData();
    void enableUI(bool enabled);
    void setupOfflineMode();

    // 数据库配置函数
    void setDatabaseConfig(const QString &host, int port, const QString &database,
                           const QString &user, const QString &password);
    void showDatabaseConfigDialog();
    void createMockData();

    // UI更新函数
    void updateTableView();
    void updateStatusBar();
    void showLoadingState(bool loading);
    void resetQueryConditions();

    // 列管理函数
    void showColumnSettingsDialog();
    void saveColumnSettings();
    void loadColumnSettings();
    void resetColumnSettings();

    // 工具函数
    QString formatRecordCount(int count);
    void showErrorMessage(const QString &title, const QString &message);
    void showSuccessMessage(const QString &message);

private:
    // UI对象
    Ui::ComprehensiveViewWidget *ui;

    // UI组件（从UI文件中获取的引用）
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;

    // 查询条件区域
    QGroupBox *m_queryGroupBox;
    QGridLayout *m_queryLayout;
    QLineEdit *m_targetIdEdit;  // IMSX
    QLineEdit *m_imexEdit;      // IMEX
    QLineEdit *m_numberEdit;    // 号码
    QPushButton *m_commBBtn;    // 通讯体制B
    QPushButton *m_commGBtn;    // 通讯体制G
    QPushButton *m_commIBtn;    // 通讯体制I
    QPushButton *m_commTBtn;    // 通讯体制T
    QPushButton *m_aircraftBtn; // 飞机
    QPushButton *m_shipBtn;     // 船载
    QPushButton *m_vehicleBtn;  // 车载
    QPushButton *m_voiceBtn;    // 话音
    QPushButton *m_ipBtn;       // IP
    QPushButton *m_smsBtn;      // 短消息
    QComboBox *m_callTypeCombo; // 呼叫类型
    QLineEdit *m_phoneEdit;     // 号码
    QLineEdit *m_ipAddressEdit; // IP地址
    QComboBox *m_targetTypeCombo;
    QComboBox *m_statusCombo;
    QDateTimeEdit *m_startTimeEdit;
    QDateTimeEdit *m_endTimeEdit;
    QLineEdit *m_beamEdit;         // 波束条件
    QPushButton *m_mapSelectorBtn; // 地图位置选择器
    QPushButton *m_queryButton;
    QPushButton *m_resetButton;
    QPushButton *m_refreshButton;

    // 结果显示区域
    QGroupBox *m_resultGroupBox;
    QVBoxLayout *m_resultLayout;
    QTableView *m_tableView;
    QStandardItemModel *m_tableModel; // 改用QStandardItemModel
    QSortFilterProxyModel *m_proxyModel;

    // 分页控件
    QHBoxLayout *m_paginationLayout;
    QPushButton *m_firstPageButton;
    QPushButton *m_previousPageButton;
    QPushButton *m_nextPageButton;
    QPushButton *m_lastPageButton;
    QLabel *m_pageInfoLabel;
    QLabel *m_recordCountLabel;
    QComboBox *m_pageSizeCombo;
    QLineEdit *m_pageNumberEdit;
    QPushButton *m_goToPageButton;

    // 工具栏
    QHBoxLayout *m_toolbarLayout;
    QPushButton *m_columnSettingsButton;
    QPushButton *m_exportButton;
    QProgressBar *m_progressBar;

    // 数据库相关 - 使用新的DatabaseManager
    QString m_currentQuery;
    QString m_tableName; // 表名

    // 分页相关
    int m_currentPage;
    int m_pageSize;
    int m_totalRecords;
    int m_totalPages;

    // 排序相关
    int m_sortColumn;
    Qt::SortOrder m_sortOrder;

    // 列设置相关
    QStringList m_availableColumns;
    QStringList m_visibleColumns;
    QMap<QString, int> m_columnOrder;
    QMap<QString, bool> m_columnVisibility;

    // 状态相关
    bool m_isLoading;
    bool m_isDatabaseConnected;
    QMessageBox *m_statusBox;
};

#endif // COMPREHENSIVEVIEWWIDGET_H
