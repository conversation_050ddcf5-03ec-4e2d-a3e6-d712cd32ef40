# Qt5 MySQL插件自动安装脚本
param(
    [string]$QtPath = ""
)

Write-Host "🔧 Qt5 MySQL插件自动安装工具" -ForegroundColor Green
Write-Host ""

# 查找Qt安装目录
$qtDirs = @()
if ($QtPath -ne "") {
    $qtDirs += $QtPath
}

# 常见Qt安装路径
$commonPaths = @(
    "C:\Qt",
    "D:\Qt", 
    "C:\Program Files\Qt",
    "C:\Program Files (x86)\Qt"
)

foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        $qtDirs += Get-ChildItem $path -Directory | ForEach-Object { $_.FullName }
    }
}

if ($qtDirs.Count -eq 0) {
    Write-Host "❌ 未找到Qt安装目录" -ForegroundColor Red
    Write-Host "请手动指定Qt路径: .\auto_install_mysql_plugin.ps1 -QtPath 'C:\Qt\Qt5.14.2'"
    exit 1
}

Write-Host "📋 找到Qt安装目录:" -ForegroundColor Yellow
$qtDirs | ForEach-Object { Write-Host "  $_" }
Write-Host ""

# 查找Qt5版本目录
$qt5Versions = @()
foreach ($qtDir in $qtDirs) {
    if (Test-Path $qtDir) {
        $versions = Get-ChildItem $qtDir -Directory | Where-Object { $_.Name -match "^5\." }
        foreach ($version in $versions) {
            $compilers = Get-ChildItem $version.FullName -Directory | Where-Object { $_.Name -match "msvc" }
            foreach ($compiler in $compilers) {
                $qt5Versions += @{
                    Path = $compiler.FullName
                    Version = $version.Name
                    Compiler = $compiler.Name
                }
            }
        }
    }
}

if ($qt5Versions.Count -eq 0) {
    Write-Host "❌ 未找到Qt5 MSVC版本" -ForegroundColor Red
    exit 1
}

Write-Host "🔍 找到Qt5版本:" -ForegroundColor Yellow
$qt5Versions | ForEach-Object { 
    Write-Host "  $($_.Version) - $($_.Compiler) - $($_.Path)"
}
Write-Host ""

# 下载MySQL驱动文件
$driverUrls = @{
    "qsqlmysql.dll" = "https://github.com/thecodemonkey86/qt_mysql_driver/releases/download/v1.0/qsqlmysql.dll"
    "qsqlmysqld.dll" = "https://github.com/thecodemonkey86/qt_mysql_driver/releases/download/v1.0/qsqlmysqld.dll"
}

$libmysqlUrl = "https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-winx64.zip"

Write-Host "📥 开始下载MySQL驱动文件..." -ForegroundColor Cyan

# 创建临时目录
$tempDir = "$env:TEMP\qt_mysql_install"
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}

# 下载驱动文件
foreach ($file in $driverUrls.Keys) {
    $url = $driverUrls[$file]
    $filePath = "$tempDir\$file"
    
    Write-Host "下载 $file..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing
        Write-Host "✅ $file 下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ $file 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        
        # 如果下载失败，创建占位文件提示用户手动下载
        "# 请手动下载此文件" | Out-File $filePath -Encoding UTF8
    }
}

# 安装到所有Qt5版本
foreach ($qt5 in $qt5Versions) {
    $pluginDir = "$($qt5.Path)\plugins\sqldrivers"
    
    Write-Host "📦 安装到: $($qt5.Version) - $($qt5.Compiler)" -ForegroundColor Cyan
    
    # 创建插件目录
    if (!(Test-Path $pluginDir)) {
        New-Item -ItemType Directory -Path $pluginDir -Force | Out-Null
    }
    
    # 复制驱动文件
    foreach ($file in $driverUrls.Keys) {
        $srcFile = "$tempDir\$file"
        $dstFile = "$pluginDir\$file"
        
        if (Test-Path $srcFile) {
            Copy-Item $srcFile $dstFile -Force
            Write-Host "  ✅ 已安装 $file" -ForegroundColor Green
        } else {
            Write-Host "  ❌ 未找到 $file" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "🗄️ 检查MySQL客户端库..." -ForegroundColor Cyan

# 检查libmysql.dll
$libmysqlFound = $false
$systemPaths = $env:PATH -split ";"
foreach ($path in $systemPaths) {
    if (Test-Path "$path\libmysql.dll") {
        Write-Host "✅ 找到libmysql.dll: $path\libmysql.dll" -ForegroundColor Green
        $libmysqlFound = $true
        break
    }
}

if (!$libmysqlFound) {
    Write-Host "❌ 未找到libmysql.dll" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 解决方案:" -ForegroundColor Yellow
    Write-Host "1. 下载MySQL Connector/C: https://dev.mysql.com/downloads/connector/c/"
    Write-Host "2. 安装后将libmysql.dll添加到系统PATH"
    Write-Host "3. 或将libmysql.dll复制到程序目录"
}

# 清理临时文件
Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 Qt5 MySQL插件安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步:" -ForegroundColor Yellow
Write-Host "1. 重启您的Qt应用程序"
Write-Host "2. 确保libmysql.dll可用"
Write-Host "3. 测试QMYSQL驱动是否工作"
Write-Host ""

Read-Host "按任意键继续..."
