@echo off
setlocal enabledelayedexpansion

REM LiteAPPStar Visual Studio 2022 专用构建脚本

set "SCRIPT_NAME=%~nx0"
set "RELEASE_BUILD=0"
set "CLEAN_ONLY=0"
set "REBUILD=0"
set "OPEN_VS=0"

REM 显示帮助信息
:show_help
echo LiteAPPStar Visual Studio 2022 构建脚本
echo.
echo 用法: %SCRIPT_NAME% [选项]
echo.
echo 选项:
echo   -h, --help          显示此帮助信息
echo   -c, --clean         清理构建文件
echo   -r, --release       发布版本构建 (默认为调试版本)
echo   --rebuild           重新构建 (清理后构建)
echo   --open              构建后打开Visual Studio
echo.
echo 示例:
echo   %SCRIPT_NAME%                  # 调试构建
echo   %SCRIPT_NAME% -r               # 发布构建
echo   %SCRIPT_NAME% -c               # 清理构建文件
echo   %SCRIPT_NAME% --rebuild -r     # 重新进行发布构建
echo   %SCRIPT_NAME% --open           # 构建后打开VS
goto :eof

REM 打印信息
:print_info
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM 检查Visual Studio 2022
:check_vs2022
call :print_info "检查Visual Studio 2022环境..."

REM 查找VS2022安装路径
set "VS_PATH="
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -version [17.0,18.0) -property installationPath 2^>nul`) do (
    set "VS_PATH=%%i"
)

if "%VS_PATH%" == "" (
    call :print_error "未找到Visual Studio 2022，请确保已安装"
    exit /b 1
)

call :print_info "找到Visual Studio 2022: %VS_PATH%"

REM 设置VS环境变量
call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
if errorlevel 1 (
    call :print_error "无法初始化Visual Studio 2022环境"
    exit /b 1
)

goto :eof

REM 检查依赖
:check_dependencies
call :print_info "检查构建依赖..."

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    call :print_error "未找到CMake，请安装CMake并添加到PATH"
    exit /b 1
)

REM 检查Qt5
qmake -v >nul 2>&1
if errorlevel 1 (
    call :print_error "未找到Qt5，请安装Qt5并添加到PATH"
    exit /b 1
)

call :print_success "依赖检查完成"
goto :eof

REM 清理构建文件
:clean_build
call :print_info "清理构建文件..."

if exist build rmdir /s /q build
if exist bin rmdir /s /q bin

call :print_success "清理完成"
goto :eof

REM 配置CMake项目
:configure_cmake
call :print_info "配置CMake项目..."

cmake -B build -S . -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    call :print_error "CMake配置失败"
    exit /b 1
)

call :print_success "CMake配置完成"
goto :eof

REM 构建项目
:build_project
set "BUILD_CONFIG=Debug"
if "%RELEASE_BUILD%" == "1" set "BUILD_CONFIG=Release"

call :print_info "构建项目 (%BUILD_CONFIG%)..."

cmake --build build --config %BUILD_CONFIG%
if errorlevel 1 (
    call :print_error "构建失败"
    exit /b 1
)

call :print_success "构建完成"
goto :eof

REM 打开Visual Studio
:open_visual_studio
call :print_info "打开Visual Studio..."

start "" "%VS_PATH%\Common7\IDE\devenv.exe" build\LiteAPPStar.sln
goto :eof

REM 解析命令行参数
:parse_args
if "%~1" == "" goto :args_done
if "%~1" == "-h" goto :show_help
if "%~1" == "--help" goto :show_help
if "%~1" == "-c" (
    set "CLEAN_ONLY=1"
    shift
    goto :parse_args
)
if "%~1" == "--clean" (
    set "CLEAN_ONLY=1"
    shift
    goto :parse_args
)
if "%~1" == "-r" (
    set "RELEASE_BUILD=1"
    shift
    goto :parse_args
)
if "%~1" == "--release" (
    set "RELEASE_BUILD=1"
    shift
    goto :parse_args
)
if "%~1" == "--rebuild" (
    set "REBUILD=1"
    shift
    goto :parse_args
)
if "%~1" == "--open" (
    set "OPEN_VS=1"
    shift
    goto :parse_args
)

call :print_error "未知选项: %~1"
goto :show_help

:args_done

REM 主程序
:main
call :print_info "LiteAPPStar Visual Studio 2022 构建脚本启动"

REM 解析命令行参数
call :parse_args %*

REM 检查VS2022环境
call :check_vs2022
if errorlevel 1 exit /b 1

REM 检查依赖
call :check_dependencies
if errorlevel 1 exit /b 1

REM 如果只是清理，执行清理后退出
if "%CLEAN_ONLY%" == "1" (
    call :clean_build
    exit /b 0
)

REM 如果是重新构建，先清理
if "%REBUILD%" == "1" (
    call :clean_build
)

REM 配置项目
call :configure_cmake
if errorlevel 1 exit /b 1

REM 构建项目
call :build_project
if errorlevel 1 exit /b 1

call :print_success "构建完成！"

REM 显示可执行文件位置
set "BUILD_CONFIG=Debug"
if "%RELEASE_BUILD%" == "1" set "BUILD_CONFIG=Release"
call :print_info "可执行文件位置: .\build\%BUILD_CONFIG%\LiteAPPStar.exe"

REM 如果需要，打开Visual Studio
if "%OPEN_VS%" == "1" (
    call :open_visual_studio
)

goto :eof

REM 运行主程序
call :main %*
