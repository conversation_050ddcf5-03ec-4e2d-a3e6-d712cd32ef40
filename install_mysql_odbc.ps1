# MySQL ODBC驱动自动下载安装脚本
# 适用于Windows系统

Write-Host "正在下载并安装MySQL ODBC驱动..." -ForegroundColor Green

# 创建临时目录
$tempDir = "$env:TEMP\mysql_odbc_install"
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force
}

# MySQL ODBC驱动下载URL (8.0.35版本)
$odbcUrl = "https://dev.mysql.com/get/Downloads/Connector-ODBC/mysql-connector-odbc-8.0.35-winx64.msi"
$odbcFile = "$tempDir\mysql-connector-odbc-8.0.35-winx64.msi"

Write-Host "下载地址: $odbcUrl" -ForegroundColor Yellow

try {
    # 下载MySQL ODBC驱动
    Write-Host "正在下载MySQL ODBC驱动..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $odbcUrl -OutFile $odbcFile -UseBasicParsing
    
    if (Test-Path $odbcFile) {
        Write-Host "下载完成: $odbcFile" -ForegroundColor Green
        
        # 静默安装
        Write-Host "正在安装MySQL ODBC驱动..." -ForegroundColor Cyan
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$odbcFile`" /quiet /norestart" -Wait
        
        Write-Host "MySQL ODBC驱动安装完成!" -ForegroundColor Green
        
        # 验证安装
        $odbcDrivers = Get-OdbcDriver | Where-Object { $_.Name -like "*MySQL*" }
        if ($odbcDrivers) {
            Write-Host "验证成功: 找到以下MySQL ODBC驱动:" -ForegroundColor Green
            $odbcDrivers | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Yellow }
        }
        else {
            Write-Host "警告: 未找到MySQL ODBC驱动，可能需要重启系统" -ForegroundColor Red
        }
        
    }
    else {
        Write-Host "错误: 下载失败" -ForegroundColor Red
        exit 1
    }
    
}
catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载安装: https://dev.mysql.com/downloads/connector/odbc/" -ForegroundColor Yellow
    exit 1
}

# 清理临时文件
Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "`n安装完成! 请重新启动应用程序测试数据库连接。" -ForegroundColor Green
Write-Host "如果仍有问题，请确保MySQL服务正在运行。" -ForegroundColor Yellow

# 检查MySQL服务状态
$mysqlService = Get-Service -Name "MySQL*" -ErrorAction SilentlyContinue
if ($mysqlService) {
    Write-Host "`nMySQL服务状态:" -ForegroundColor Cyan
    $mysqlService | ForEach-Object { 
        Write-Host "  $($_.Name): $($_.Status)" -ForegroundColor $(if ($_.Status -eq 'Running') { 'Green' }else { 'Red' })
    }
}
else {
    Write-Host "`n警告: 未找到MySQL服务，请确保MySQL已安装并运行" -ForegroundColor Red
}

Read-Host "Press any key to continue..."
