# LiteAPPStar 快速构建脚本
Write-Host "快速构建 LiteAPPStar..." -ForegroundColor Green

# 设置Qt环境变量
$QT_DIR = "D:\Qt\Qt5.14.2\5.14.2\msvc2017_64"
$env:PATH = "$QT_DIR\bin;$env:PATH"

# 检查Qt是否存在
if (-not (Test-Path "$QT_DIR\bin\qmake.exe")) {
    Write-Host "Qt5 not found at $QT_DIR" -ForegroundColor Red
    Write-Host "Please check Qt installation path in CMakeLists.txt" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 创建构建目录
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
}

# 配置CMake项目
Write-Host "配置CMake项目..." -ForegroundColor Yellow
$configResult = & cmake -B build -G "Visual Studio 17 2022" -A x64
if ($LASTEXITCODE -ne 0) {
    Write-Host "CMake配置失败!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 编译项目
Write-Host "编译项目..." -ForegroundColor Yellow
$buildResult = & cmake --build build --config Debug
if ($LASTEXITCODE -ne 0) {
    Write-Host "编译失败!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "编译成功完成!" -ForegroundColor Green
Write-Host "可执行文件位置: build\bin\Debug\LiteAPPStar.exe" -ForegroundColor Cyan

# 检查可执行文件是否存在
if (Test-Path "build\bin\Debug\LiteAPPStar.exe") {
    Write-Host "构建验证: 成功" -ForegroundColor Green
    Write-Host "现在可以在VSCode中使用F5进行调试了。" -ForegroundColor Green
} else {
    Write-Host "构建验证: 失败 - 找不到可执行文件" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "Press Enter to exit"
