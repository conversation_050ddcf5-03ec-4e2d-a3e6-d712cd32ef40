#include "qtgeoinfowidget.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QApplication>

// 常量定义
const double QtGeoInfoWidget::DEFAULT_LATITUDE = 39.9042; // 北京天安门
const double QtGeoInfoWidget::DEFAULT_LONGITUDE = 116.4074;
const double QtGeoInfoWidget::DEFAULT_ZOOM = 10.0;
const int QtGeoInfoWidget::CONTROL_PANEL_WIDTH = 350;

QtGeoInfoWidget::QtGeoInfoWidget(QWidget *parent)
    : QWidget(parent), m_mainLayout(nullptr), m_mainSplitter(nullptr), m_mapView(nullptr), m_controlTabs(nullptr), m_mapControlTab(nullptr), m_mapTypeCombo(nullptr), m_zoomSlider(nullptr), m_zoomSpinBox(nullptr), m_zoomInBtn(nullptr), m_zoomOutBtn(nullptr), m_resetViewBtn(nullptr), m_fitMarkersBtn(nullptr), m_locationTab(nullptr), m_latitudeEdit(nullptr), m_longitudeEdit(nullptr), m_jumpBtn(nullptr), m_locationBtn(nullptr), m_currentCoordLabel(nullptr), m_mouseCoordLabel(nullptr), m_markersTab(nullptr), m_addMarkerBtn(nullptr), m_clearMarkersBtn(nullptr), m_markersListText(nullptr), m_markerTitleEdit(nullptr), m_markerDescEdit(nullptr), m_dataTab(nullptr), m_exportBtn(nullptr), m_importBtn(nullptr), m_dataInfoText(nullptr), m_progressBar(nullptr), m_statusBar(nullptr), m_coordStatusLabel(nullptr), m_zoomStatusLabel(nullptr), m_mapTypeStatusLabel(nullptr), m_markersCountLabel(nullptr), m_currentLatitude(DEFAULT_LATITUDE), m_currentLongitude(DEFAULT_LONGITUDE), m_currentZoom(DEFAULT_ZOOM), m_currentMapType("OpenStreetMap"), m_markersCount(0)
{
    qDebug() << "QtGeoInfoWidget: 初始化开始";

    setupUI();
    connectSignals();

    // 设置初始状态
    updateCoordinateDisplay(m_currentLatitude, m_currentLongitude);
    updateZoomDisplay(m_currentZoom);
    updateMapTypeDisplay(m_currentMapType);
    updateStatus("地图组件初始化完成");

    qDebug() << "QtGeoInfoWidget: 初始化完成";
}

QtGeoInfoWidget::~QtGeoInfoWidget()
{
    qDebug() << "QtGeoInfoWidget: 析构";
}

void QtGeoInfoWidget::setupUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 创建主分割器
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_mainSplitter);

    // 创建地图区域
    createMapArea();

    // 创建控制面板
    createControlTabs();

    // 创建状态栏
    createStatusBar();

    // 设置分割器比例
    m_mainSplitter->setSizes({1000, CONTROL_PANEL_WIDTH});
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 0);

    qDebug() << "QtGeoInfoWidget: UI设置完成";
}

void QtGeoInfoWidget::createMapArea()
{
    // 创建Qt Location地图视图
    m_mapView = new QtLocationMapView(this);
    m_mainSplitter->addWidget(m_mapView);

    qDebug() << "QtGeoInfoWidget: 地图区域创建完成";
}

void QtGeoInfoWidget::createControlTabs()
{
    // 创建控制标签页
    m_controlTabs = new QTabWidget(this);
    m_controlTabs->setFixedWidth(CONTROL_PANEL_WIDTH);
    m_controlTabs->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);

    // 创建各个标签页
    createMapControlTab();
    createLocationTab();
    createMarkersTab();
    createDataTab();

    m_mainSplitter->addWidget(m_controlTabs);

    qDebug() << "QtGeoInfoWidget: 控制面板创建完成";
}

void QtGeoInfoWidget::createMapControlTab()
{
    m_mapControlTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_mapControlTab);

    // 地图类型控制
    QGroupBox *mapTypeGroup = new QGroupBox("地图类型");
    QVBoxLayout *mapTypeLayout = new QVBoxLayout(mapTypeGroup);

    m_mapTypeCombo = new QComboBox();
    m_mapTypeCombo->addItems({"OpenStreetMap", "卫星地图", "混合地图"});
    mapTypeLayout->addWidget(m_mapTypeCombo);

    layout->addWidget(mapTypeGroup);

    // 缩放控制
    QGroupBox *zoomGroup = new QGroupBox("缩放控制");
    QVBoxLayout *zoomLayout = new QVBoxLayout(zoomGroup);

    // 缩放滑块和数值框
    QHBoxLayout *zoomSliderLayout = new QHBoxLayout();
    m_zoomSlider = new QSlider(Qt::Horizontal);
    m_zoomSlider->setRange(10, 200); // 1.0 到 20.0
    m_zoomSlider->setValue(DEFAULT_ZOOM * 10);
    m_zoomSpinBox = new QSpinBox();
    m_zoomSpinBox->setRange(1, 20);
    m_zoomSpinBox->setValue(DEFAULT_ZOOM);
    m_zoomSpinBox->setSuffix("x");

    zoomSliderLayout->addWidget(m_zoomSlider);
    zoomSliderLayout->addWidget(m_zoomSpinBox);
    zoomLayout->addLayout(zoomSliderLayout);

    // 缩放按钮
    QHBoxLayout *zoomBtnLayout = new QHBoxLayout();
    m_zoomInBtn = new QPushButton("放大");
    m_zoomOutBtn = new QPushButton("缩小");
    zoomBtnLayout->addWidget(m_zoomInBtn);
    zoomBtnLayout->addWidget(m_zoomOutBtn);
    zoomLayout->addLayout(zoomBtnLayout);

    // 视图控制按钮
    m_resetViewBtn = new QPushButton("重置视图");
    m_fitMarkersBtn = new QPushButton("适应标记");
    zoomLayout->addWidget(m_resetViewBtn);
    zoomLayout->addWidget(m_fitMarkersBtn);

    layout->addWidget(zoomGroup);

    // 添加弹性空间
    layout->addStretch();

    m_controlTabs->addTab(m_mapControlTab, "地图控制");
}

void QtGeoInfoWidget::createLocationTab()
{
    m_locationTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_locationTab);

    // 坐标输入组
    QGroupBox *coordGroup = new QGroupBox("坐标输入");
    QGridLayout *coordLayout = new QGridLayout(coordGroup);

    coordLayout->addWidget(new QLabel("纬度:"), 0, 0);
    m_latitudeEdit = new QLineEdit(QString::number(DEFAULT_LATITUDE, 'f', 6));
    coordLayout->addWidget(m_latitudeEdit, 0, 1);

    coordLayout->addWidget(new QLabel("经度:"), 1, 0);
    m_longitudeEdit = new QLineEdit(QString::number(DEFAULT_LONGITUDE, 'f', 6));
    coordLayout->addWidget(m_longitudeEdit, 1, 1);

    m_jumpBtn = new QPushButton("跳转到坐标");
    coordLayout->addWidget(m_jumpBtn, 2, 0, 1, 2);

    layout->addWidget(coordGroup);

    // 定位组
    QGroupBox *locationGroup = new QGroupBox("定位功能");
    QVBoxLayout *locationLayout = new QVBoxLayout(locationGroup);

    m_locationBtn = new QPushButton("获取当前位置");
    locationLayout->addWidget(m_locationBtn);

    layout->addWidget(locationGroup);

    // 坐标显示组
    QGroupBox *displayGroup = new QGroupBox("坐标显示");
    QVBoxLayout *displayLayout = new QVBoxLayout(displayGroup);

    m_currentCoordLabel = new QLabel("当前中心: 39.904200, 116.407400");
    m_currentCoordLabel->setWordWrap(true);
    displayLayout->addWidget(m_currentCoordLabel);

    m_mouseCoordLabel = new QLabel("鼠标位置: ---, ---");
    m_mouseCoordLabel->setWordWrap(true);
    displayLayout->addWidget(m_mouseCoordLabel);

    layout->addWidget(displayGroup);

    // 添加弹性空间
    layout->addStretch();

    m_controlTabs->addTab(m_locationTab, "位置控制");
}

void QtGeoInfoWidget::createMarkersTab()
{
    m_markersTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_markersTab);

    // 标记添加组
    QGroupBox *addGroup = new QGroupBox("添加标记");
    QVBoxLayout *addLayout = new QVBoxLayout(addGroup);

    addLayout->addWidget(new QLabel("标题:"));
    m_markerTitleEdit = new QLineEdit("新标记");
    addLayout->addWidget(m_markerTitleEdit);

    addLayout->addWidget(new QLabel("描述:"));
    m_markerDescEdit = new QLineEdit("用户添加的标记");
    addLayout->addWidget(m_markerDescEdit);

    m_addMarkerBtn = new QPushButton("在中心添加标记");
    addLayout->addWidget(m_addMarkerBtn);

    layout->addWidget(addGroup);

    // 标记管理组
    QGroupBox *manageGroup = new QGroupBox("标记管理");
    QVBoxLayout *manageLayout = new QVBoxLayout(manageGroup);

    m_clearMarkersBtn = new QPushButton("清除所有标记");
    manageLayout->addWidget(m_clearMarkersBtn);

    layout->addWidget(manageGroup);

    // 标记列表组
    QGroupBox *listGroup = new QGroupBox("标记列表");
    QVBoxLayout *listLayout = new QVBoxLayout(listGroup);

    m_markersListText = new QTextEdit();
    m_markersListText->setMaximumHeight(200);
    m_markersListText->setPlainText("标记列表:\n(右键点击地图添加标记)");
    listLayout->addWidget(m_markersListText);

    layout->addWidget(listGroup);

    // 添加弹性空间
    layout->addStretch();

    m_controlTabs->addTab(m_markersTab, "标记管理");
}

void QtGeoInfoWidget::createDataTab()
{
    m_dataTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_dataTab);

    // 数据导入导出组
    QGroupBox *dataGroup = new QGroupBox("数据管理");
    QVBoxLayout *dataLayout = new QVBoxLayout(dataGroup);

    QHBoxLayout *btnLayout = new QHBoxLayout();
    m_exportBtn = new QPushButton("导出数据");
    m_importBtn = new QPushButton("导入数据");
    btnLayout->addWidget(m_exportBtn);
    btnLayout->addWidget(m_importBtn);
    dataLayout->addLayout(btnLayout);

    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    dataLayout->addWidget(m_progressBar);

    layout->addWidget(dataGroup);

    // 数据信息组
    QGroupBox *infoGroup = new QGroupBox("数据信息");
    QVBoxLayout *infoLayout = new QVBoxLayout(infoGroup);

    m_dataInfoText = new QTextEdit();
    m_dataInfoText->setMaximumHeight(250);
    m_dataInfoText->setPlainText("数据统计:\n标记数量: 0\n地图类型: OpenStreetMap\n当前缩放: 10x");
    infoLayout->addWidget(m_dataInfoText);

    layout->addWidget(infoGroup);

    // 添加弹性空间
    layout->addStretch();

    m_controlTabs->addTab(m_dataTab, "数据管理");
}

void QtGeoInfoWidget::createStatusBar()
{
    m_statusBar = new QStatusBar(this);

    // 创建状态标签
    m_coordStatusLabel = new QLabel("坐标: 39.904200, 116.407400");
    m_zoomStatusLabel = new QLabel("缩放: 10x");
    m_mapTypeStatusLabel = new QLabel("类型: OpenStreetMap");
    m_markersCountLabel = new QLabel("标记: 0");

    // 添加到状态栏
    m_statusBar->addWidget(m_coordStatusLabel);
    m_statusBar->addPermanentWidget(m_zoomStatusLabel);
    m_statusBar->addPermanentWidget(m_mapTypeStatusLabel);
    m_statusBar->addPermanentWidget(m_markersCountLabel);

    m_mainLayout->addWidget(m_statusBar);

    qDebug() << "QtGeoInfoWidget: 状态栏创建完成";
}

void QtGeoInfoWidget::connectSignals()
{
    // 地图视图信号连接
    connect(m_mapView, &QtLocationMapView::centerChanged, this, &QtGeoInfoWidget::onMapCenterChanged);
    connect(m_mapView, &QtLocationMapView::zoomChanged, this, &QtGeoInfoWidget::onMapZoomChanged);
    connect(m_mapView, &QtLocationMapView::mapClicked, this, &QtGeoInfoWidget::onMapClicked);
    connect(m_mapView, &QtLocationMapView::mapDoubleClicked, this, &QtGeoInfoWidget::onMapDoubleClicked);
    connect(m_mapView, &QtLocationMapView::markerClicked, this, &QtGeoInfoWidget::onMarkerClicked);
    connect(m_mapView, &QtLocationMapView::mapTypeChanged, this, &QtGeoInfoWidget::onMapTypeChanged);

    // 地图控制信号连接
    connect(m_mapTypeCombo, &QComboBox::currentTextChanged, this, &QtGeoInfoWidget::switchMapType);
    connect(m_zoomSlider, QOverload<int>::of(&QSlider::valueChanged), [this](int value)
            {
        double zoom = value / 10.0;
        m_zoomSpinBox->setValue(zoom);
        setZoomLevel(zoom); });
    connect(m_zoomSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), [this](int value)
            {
        m_zoomSlider->setValue(value * 10);
        setZoomLevel(value); });
    connect(m_zoomInBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::zoomIn);
    connect(m_zoomOutBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::zoomOut);
    connect(m_resetViewBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::resetView);
    connect(m_fitMarkersBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::fitToMarkers);

    // 位置控制信号连接
    connect(m_latitudeEdit, &QLineEdit::textChanged, this, &QtGeoInfoWidget::onCoordinateInputChanged);
    connect(m_longitudeEdit, &QLineEdit::textChanged, this, &QtGeoInfoWidget::onCoordinateInputChanged);
    connect(m_jumpBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onJumpButtonClicked);
    connect(m_locationBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onLocationButtonClicked);

    // 标记管理信号连接
    connect(m_addMarkerBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onAddMarkerClicked);
    connect(m_clearMarkersBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onClearMarkersClicked);

    // 数据管理信号连接
    connect(m_exportBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onExportDataClicked);
    connect(m_importBtn, &QPushButton::clicked, this, &QtGeoInfoWidget::onImportDataClicked);

    qDebug() << "QtGeoInfoWidget: 信号连接完成";
}

// 公共接口实现
void QtGeoInfoWidget::setCenter(double latitude, double longitude)
{
    m_mapView->setCenter(latitude, longitude);
    updateCoordinateDisplay(latitude, longitude);
}

void QtGeoInfoWidget::setZoomLevel(double zoom)
{
    m_mapView->setZoomLevel(zoom);
    updateZoomDisplay(zoom);
}

void QtGeoInfoWidget::addMarker(double latitude, double longitude, const QString &title, const QString &description)
{
    m_mapView->addMarker(latitude, longitude, title, description);
    m_markersCount++;
    updateMarkersList();
}

void QtGeoInfoWidget::clearMarkers()
{
    m_mapView->clearMarkers();
    m_markersCount = 0;
    updateMarkersList();
}

// 公共槽函数实现
void QtGeoInfoWidget::jumpToCoordinate(double latitude, double longitude)
{
    setCenter(latitude, longitude);
    updateStatus(QString("跳转到坐标: %1, %2").arg(latitude, 0, 'f', 6).arg(longitude, 0, 'f', 6));
}

void QtGeoInfoWidget::getCurrentLocation()
{
    // 这里可以集成GPS定位功能
    updateStatus("GPS定位功能暂未实现");
    QMessageBox::information(this, "定位功能", "GPS定位功能正在开发中...");
}

void QtGeoInfoWidget::switchMapType(const QString &mapType)
{
    m_mapView->setMapType(mapType);
    updateMapTypeDisplay(mapType);
    updateStatus(QString("切换地图类型: %1").arg(mapType));
}

void QtGeoInfoWidget::zoomIn()
{
    m_mapView->zoomIn();
}

void QtGeoInfoWidget::zoomOut()
{
    m_mapView->zoomOut();
}

void QtGeoInfoWidget::resetView()
{
    m_mapView->resetView();
    updateStatus("重置地图视图");
}

void QtGeoInfoWidget::fitToMarkers()
{
    m_mapView->fitToMarkers();
    updateStatus("适应标记视图");
}

// 私有槽函数实现
void QtGeoInfoWidget::onCoordinateInputChanged()
{
    validateCoordinateInput();
}

void QtGeoInfoWidget::onJumpButtonClicked()
{
    bool latOk, lngOk;
    double lat = m_latitudeEdit->text().toDouble(&latOk);
    double lng = m_longitudeEdit->text().toDouble(&lngOk);

    if (latOk && lngOk && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180)
    {
        jumpToCoordinate(lat, lng);
    }
    else
    {
        QMessageBox::warning(this, "坐标错误", "请输入有效的坐标值！\n纬度范围: -90 到 90\n经度范围: -180 到 180");
        // 恢复原来的值
        m_latitudeEdit->setText(QString::number(m_currentLatitude, 'f', 6));
        m_longitudeEdit->setText(QString::number(m_currentLongitude, 'f', 6));
    }
}

void QtGeoInfoWidget::onLocationButtonClicked()
{
    getCurrentLocation();
}

void QtGeoInfoWidget::onMapTypeChanged(const QString &mapType)
{
    // 更新组合框选择（避免循环信号）
    if (m_mapTypeCombo->currentText() != mapType)
    {
        m_mapTypeCombo->setCurrentText(mapType);
    }
    updateMapTypeDisplay(mapType);
}

void QtGeoInfoWidget::onAddMarkerClicked()
{
    QString title = m_markerTitleEdit->text().trimmed();
    QString description = m_markerDescEdit->text().trimmed();

    if (title.isEmpty())
    {
        title = QString("标记 %1").arg(m_markersCount + 1);
    }

    addMarker(m_currentLatitude, m_currentLongitude, title, description);
    updateStatus(QString("添加标记: %1").arg(title));
}

void QtGeoInfoWidget::onClearMarkersClicked()
{
    if (m_markersCount > 0)
    {
        int ret = QMessageBox::question(this, "清除标记",
                                        QString("确定要清除所有 %1 个标记吗？").arg(m_markersCount),
                                        QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes)
        {
            clearMarkers();
            updateStatus("已清除所有标记");
        }
    }
    else
    {
        QMessageBox::information(this, "清除标记", "当前没有标记需要清除。");
    }
}

void QtGeoInfoWidget::onExportDataClicked()
{
    exportMarkers();
}

void QtGeoInfoWidget::onImportDataClicked()
{
    importMarkers();
}

// 地图事件槽函数实现
void QtGeoInfoWidget::onMapCenterChanged(double latitude, double longitude)
{
    m_currentLatitude = latitude;
    m_currentLongitude = longitude;

    updateCoordinateDisplay(latitude, longitude);
    emit coordinateChanged(latitude, longitude);
}

void QtGeoInfoWidget::onMapZoomChanged(double zoom)
{
    m_currentZoom = zoom;

    // 更新UI控件（避免循环信号）
    m_zoomSlider->blockSignals(true);
    m_zoomSpinBox->blockSignals(true);
    m_zoomSlider->setValue(zoom * 10);
    m_zoomSpinBox->setValue(zoom);
    m_zoomSlider->blockSignals(false);
    m_zoomSpinBox->blockSignals(false);

    updateZoomDisplay(zoom);
    emit zoomLevelChanged(zoom);
}

void QtGeoInfoWidget::onMapClicked(double latitude, double longitude)
{
    updateStatus(QString("地图点击: %1, %2").arg(latitude, 0, 'f', 6).arg(longitude, 0, 'f', 6));
    emit mapClicked(latitude, longitude);
}

void QtGeoInfoWidget::onMapDoubleClicked(double latitude, double longitude)
{
    // 双击添加标记
    QString title = QString("双击标记 %1").arg(m_markersCount + 1);
    addMarker(latitude, longitude, title, "双击地图添加的标记");
    updateStatus(QString("双击添加标记: %1").arg(title));
}

void QtGeoInfoWidget::onMarkerClicked(int markerId, double latitude, double longitude)
{
    updateStatus(QString("标记点击: ID=%1, 坐标=%2,%3").arg(markerId).arg(latitude, 0, 'f', 6).arg(longitude, 0, 'f', 6));
    emit markerClicked(markerId, latitude, longitude);
}

// 辅助方法实现
void QtGeoInfoWidget::updateCoordinateDisplay(double latitude, double longitude)
{
    m_currentLatitude = latitude;
    m_currentLongitude = longitude;

    // 更新输入框
    m_latitudeEdit->setText(QString::number(latitude, 'f', 6));
    m_longitudeEdit->setText(QString::number(longitude, 'f', 6));

    // 更新显示标签
    m_currentCoordLabel->setText(QString("当前中心: %1, %2").arg(latitude, 0, 'f', 6).arg(longitude, 0, 'f', 6));

    // 更新状态栏
    m_coordStatusLabel->setText(QString("坐标: %1, %2").arg(latitude, 0, 'f', 6).arg(longitude, 0, 'f', 6));
}

void QtGeoInfoWidget::updateZoomDisplay(double zoom)
{
    m_currentZoom = zoom;

    // 更新状态栏
    m_zoomStatusLabel->setText(QString("缩放: %1x").arg(zoom, 0, 'f', 1));
}

void QtGeoInfoWidget::updateMapTypeDisplay(const QString &mapType)
{
    m_currentMapType = mapType;

    // 更新状态栏
    m_mapTypeStatusLabel->setText(QString("类型: %1").arg(mapType));
}

void QtGeoInfoWidget::updateMarkersList()
{
    // 更新标记列表显示
    QString markersInfo = QString("标记列表: (共 %1 个)\n").arg(m_markersCount);
    if (m_markersCount == 0)
    {
        markersInfo += "(右键点击地图添加标记)";
    }
    else
    {
        markersInfo += "右键点击地图或使用控制面板添加更多标记";
    }

    m_markersListText->setPlainText(markersInfo);

    // 更新状态栏
    m_markersCountLabel->setText(QString("标记: %1").arg(m_markersCount));

    // 更新数据信息
    QString dataInfo = QString("数据统计:\n标记数量: %1\n地图类型: %2\n当前缩放: %3x\n当前中心: %4, %5")
                           .arg(m_markersCount)
                           .arg(m_currentMapType)
                           .arg(m_currentZoom, 0, 'f', 1)
                           .arg(m_currentLatitude, 0, 'f', 6)
                           .arg(m_currentLongitude, 0, 'f', 6);
    m_dataInfoText->setPlainText(dataInfo);
}

void QtGeoInfoWidget::updateStatus(const QString &status)
{
    m_statusBar->showMessage(status, 3000); // 显示3秒
    emit statusChanged(status);
    qDebug() << "QtGeoInfoWidget:" << status;
}

void QtGeoInfoWidget::exportMarkers()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出标记数据",
                                                    "markers.json",
                                                    "JSON文件 (*.json)");
    if (fileName.isEmpty())
    {
        return;
    }

    // 这里应该从地图视图获取标记数据并导出
    // 由于我们的简化实现，暂时创建示例数据
    QJsonObject rootObj;
    rootObj["version"] = "1.0";
    rootObj["mapType"] = m_currentMapType;
    rootObj["center"] = QJsonObject{
        {"latitude", m_currentLatitude},
        {"longitude", m_currentLongitude}};
    rootObj["zoom"] = m_currentZoom;
    rootObj["markersCount"] = m_markersCount;

    QJsonArray markersArray;
    // 这里应该添加实际的标记数据
    rootObj["markers"] = markersArray;

    QJsonDocument doc(rootObj);
    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly))
    {
        file.write(doc.toJson());
        file.close();
        updateStatus(QString("标记数据已导出到: %1").arg(fileName));
        QMessageBox::information(this, "导出成功", QString("标记数据已成功导出到:\n%1").arg(fileName));
    }
    else
    {
        updateStatus("导出失败: 无法写入文件");
        QMessageBox::warning(this, "导出失败", "无法写入文件，请检查文件权限。");
    }
}

void QtGeoInfoWidget::importMarkers()
{
    QString fileName = QFileDialog::getOpenFileName(this, "导入标记数据",
                                                    "",
                                                    "JSON文件 (*.json)");
    if (fileName.isEmpty())
    {
        return;
    }

    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly))
    {
        updateStatus("导入失败: 无法读取文件");
        QMessageBox::warning(this, "导入失败", "无法读取文件，请检查文件是否存在。");
        return;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    file.close();

    if (error.error != QJsonParseError::NoError)
    {
        updateStatus("导入失败: JSON格式错误");
        QMessageBox::warning(this, "导入失败", QString("JSON格式错误:\n%1").arg(error.errorString()));
        return;
    }

    QJsonObject rootObj = doc.object();

    // 导入地图设置
    if (rootObj.contains("mapType"))
    {
        switchMapType(rootObj["mapType"].toString());
    }

    if (rootObj.contains("center") && rootObj.contains("zoom"))
    {
        QJsonObject center = rootObj["center"].toObject();
        double lat = center["latitude"].toDouble();
        double lng = center["longitude"].toDouble();
        double zoom = rootObj["zoom"].toDouble();

        setCenter(lat, lng);
        setZoomLevel(zoom);
    }

    // 导入标记数据
    if (rootObj.contains("markers"))
    {
        QJsonArray markersArray = rootObj["markers"].toArray();
        for (const QJsonValue &value : markersArray)
        {
            QJsonObject marker = value.toObject();
            double lat = marker["latitude"].toDouble();
            double lng = marker["longitude"].toDouble();
            QString title = marker["title"].toString();
            QString desc = marker["description"].toString();

            addMarker(lat, lng, title, desc);
        }
    }

    updateStatus(QString("标记数据已从文件导入: %1").arg(fileName));
    QMessageBox::information(this, "导入成功", QString("标记数据已成功从文件导入:\n%1").arg(fileName));
}

void QtGeoInfoWidget::validateCoordinateInput()
{
    // 验证坐标输入的有效性
    bool latOk, lngOk;
    double lat = m_latitudeEdit->text().toDouble(&latOk);
    double lng = m_longitudeEdit->text().toDouble(&lngOk);

    // 设置输入框样式来指示有效性
    QString validStyle = "";
    QString invalidStyle = "background-color: #ffcccc;";

    if (latOk && lat >= -90 && lat <= 90)
    {
        m_latitudeEdit->setStyleSheet(validStyle);
    }
    else
    {
        m_latitudeEdit->setStyleSheet(invalidStyle);
    }

    if (lngOk && lng >= -180 && lng <= 180)
    {
        m_longitudeEdit->setStyleSheet(validStyle);
    }
    else
    {
        m_longitudeEdit->setStyleSheet(invalidStyle);
    }

    // 启用/禁用跳转按钮
    bool valid = latOk && lngOk && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
    m_jumpBtn->setEnabled(valid);
}
