^E:\CODE\LITEAPPSTAR\BUILD\CMAKEFILES\D1B5CB7DB7CDB338927E0098E3731FEF\GENERATE.STAMP.RULE
setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/code/LiteAPPStar/build/LiteAPPStar.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
