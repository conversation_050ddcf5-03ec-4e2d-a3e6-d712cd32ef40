^E:\CODE\LITEAPPSTAR2\BUILD\CMAKEFILES\737DDB11416EA4EE317F3569EBCD36EA\GENERATE.STAMP.RULE
setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/code/LiteAPPStar2/build/LiteAPPStar.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
