# 自动下载MySQL驱动文件
Write-Host "🔧 自动下载MySQL驱动文件" -ForegroundColor Green
Write-Host ""

# 设置TLS协议
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# 创建程序目录
$programDir = "build\bin\Debug"
if (!(Test-Path $programDir)) {
    New-Item -ItemType Directory -Path $programDir -Force | Out-Null
    Write-Host "✅ 创建程序目录: $programDir" -ForegroundColor Green
}

# 定义下载文件列表
$downloads = @(
    @{
        Name = "libmysql.dll"
        Urls = @(
            "https://github.com/mysql/mysql-server/raw/8.0/bin/libmysql.dll",
            "https://raw.githubusercontent.com/mysql/mysql-connector-cpp/master/bin/libmysql.dll"
        )
        Size = "2-5MB"
    },
    @{
        Name = "qsqlmysql.dll"
        Urls = @(
            "https://github.com/thecodemonkey86/qt_mysql_driver/releases/download/v1.0/qsqlmysql.dll"
        )
        Size = "50-200KB"
    },
    @{
        Name = "qsqlmysqld.dll"
        Urls = @(
            "https://github.com/thecodemonkey86/qt_mysql_driver/releases/download/v1.0/qsqlmysqld.dll"
        )
        Size = "50-200KB"
    }
)

# 下载函数
function Download-File {
    param($Urls, $OutputPath, $FileName)
    
    foreach ($url in $Urls) {
        Write-Host "尝试从: $url" -ForegroundColor Yellow
        try {
            Invoke-WebRequest -Uri $url -OutFile $OutputPath -UseBasicParsing -TimeoutSec 30
            if (Test-Path $OutputPath) {
                $size = (Get-Item $OutputPath).Length
                Write-Host "✅ $FileName 下载成功 (${size} bytes)" -ForegroundColor Green
                return $true
            }
        }
        catch {
            Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    return $false
}

# 创建备用文件函数
function Create-PlaceholderFile {
    param($FilePath, $FileName)

    $content = "// $FileName placeholder file - please download manually"
    $content | Out-File -FilePath $FilePath -Encoding UTF8
    Write-Host "⚠️ 创建 $FileName 占位文件" -ForegroundColor Yellow
}

# 开始下载
Write-Host "📥 开始下载MySQL驱动文件..." -ForegroundColor Cyan
Write-Host ""

$successCount = 0
foreach ($download in $downloads) {
    $fileName = $download.Name
    $filePath = Join-Path $programDir $fileName
    
    Write-Host "📦 下载 $fileName (预期大小: $($download.Size))" -ForegroundColor White
    
    if (Download-File -Urls $download.Urls -OutputPath $filePath -FileName $fileName) {
        $successCount++
    }
    else {
        Write-Host "❌ $fileName 下载失败，创建占位文件" -ForegroundColor Red
        Create-PlaceholderFile -FilePath $filePath -FileName $fileName
    }
    Write-Host ""
}

# 结果报告
Write-Host "📊 下载结果:" -ForegroundColor Cyan
Write-Host "成功下载: $successCount / $($downloads.Count) 个文件" -ForegroundColor $(if ($successCount -eq $downloads.Count) { 'Green' }else { 'Yellow' })
Write-Host ""

# 检查文件
Write-Host "📋 文件检查:" -ForegroundColor Cyan
Get-ChildItem $programDir -Filter "*.dll" | ForEach-Object {
    $size = $_.Length
    $status = if ($size -gt 1000) { "✅" } else { "⚠️" }
    Write-Host "$status $($_.Name) - ${size} bytes"
}

Write-Host ""
if ($successCount -gt 0) {
    Write-Host "🎉 部分或全部文件下载成功！" -ForegroundColor Green
    Write-Host "现在可以运行程序测试MySQL连接。" -ForegroundColor Green
}
else {
    Write-Host "❌ 所有文件下载失败" -ForegroundColor Red
    Write-Host "程序将使用模拟数据模式运行。" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Yellow
Write-Host "- 如果下载失败，程序会自动使用模拟数据模式"
Write-Host "- 模拟数据模式可以演示程序的所有功能"
Write-Host "- 要连接真实MySQL，需要手动下载驱动文件"
Write-Host ""

Read-Host "按任意键继续..."
