{"version": 3, "configurePresets": [{"name": "vs2022-debug", "displayName": "Visual Studio 2022 Debug", "description": "使用Visual Studio 2022进行调试构建", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_INSTALL_PREFIX": "${sourceDir}/install"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "vs2022-release", "displayName": "Visual Studio 2022 Release", "description": "使用Visual Studio 2022进行发布构建", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "CMAKE_INSTALL_PREFIX": "${sourceDir}/install"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "mingw-debug", "displayName": "MinGW Debug", "description": "使用MinGW进行调试构建", "generator": "MinGW Makefiles", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_INSTALL_PREFIX": "${sourceDir}/install"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "linux-debug", "displayName": "Linux Debug", "description": "Linux调试构建", "generator": "Unix Makefiles", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_INSTALL_PREFIX": "${sourceDir}/install"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}}, {"name": "macos-debug", "displayName": "macOS Debug", "description": "macOS调试构建", "generator": "Unix Makefiles", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_INSTALL_PREFIX": "${sourceDir}/install"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "<PERSON>"}}], "buildPresets": [{"name": "vs2022-debug", "displayName": "Visual Studio 2022 Debug Build", "configurePreset": "vs2022-debug", "configuration": "Debug"}, {"name": "vs2022-release", "displayName": "Visual Studio 2022 Release Build", "configurePreset": "vs2022-release", "configuration": "Release"}, {"name": "mingw-debug", "displayName": "MinGW Debug Build", "configurePreset": "mingw-debug"}, {"name": "linux-debug", "displayName": "Linux Debug Build", "configurePreset": "linux-debug"}, {"name": "macos-debug", "displayName": "macOS Debug Build", "configurePreset": "macos-debug"}], "testPresets": [{"name": "vs2022-debug", "displayName": "Visual Studio 2022 Debug Tests", "configurePreset": "vs2022-debug", "configuration": "Debug"}, {"name": "vs2022-release", "displayName": "Visual Studio 2022 Release Tests", "configurePreset": "vs2022-release", "configuration": "Release"}]}