# 图标文件说明

此目录包含应用程序所需的图标文件。

## 需要的图标文件：

### 应用程序图标
- `app_icon.png` - 主应用程序图标 (32x32, 48x48, 64x64)
- `app.ico` - Windows图标文件
- `app.icns` - macOS图标文件
- `app.png` - Linux图标文件

### 工具栏图标 (16x16 或 24x24)
- `new.png` - 新建文件图标
- `open.png` - 打开文件图标
- `save.png` - 保存文件图标
- `cut.png` - 剪切图标
- `copy.png` - 复制图标
- `paste.png` - 粘贴图标
- `exit.png` - 退出图标

## 图标规格建议：

### Windows (.ico)
- 包含多个尺寸：16x16, 32x32, 48x48, 64x64, 128x128, 256x256
- 支持透明背景
- 使用PNG压缩

### macOS (.icns)
- 包含多个尺寸：16x16, 32x32, 128x128, 256x256, 512x512, 1024x1024
- 支持Retina显示屏的@2x版本
- 使用PNG格式

### Linux (.png)
- 推荐尺寸：48x48, 64x64, 128x128
- 支持透明背景
- 放置在 /usr/share/pixmaps/ 或 ~/.local/share/icons/

## 创建图标的工具：
- GIMP (免费)
- Inkscape (免费，矢量图形)
- IconWorkshop (Windows)
- Icon Composer (macOS)
- 在线图标生成器

## 注意事项：
- 所有图标应保持一致的设计风格
- 使用适当的颜色对比度
- 确保在不同背景下都清晰可见
- 考虑深色和浅色主题的兼容性
