^E:\CODE\LITEAPPSTAR2\BUILD\CMAKEFILES\9F4DF36830BF5B6C9232AB610F73C87C\AUTOUIC_(CONFIG).STAMP.RULE
setlocal
cd E:\code\LiteAPPStar2\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\CODE\LITEAPPSTAR2\BUILD\CMAKEFILES\EAD14719A397A823260D11BA753B83FC\QRC_RESOURCES.CPP.RULE
setlocal
cd E:\code\LiteAPPStar2\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar2/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar2/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar2/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar2/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\CODE\LITEAPPSTAR2\CMAKELISTS.TXT
setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar2 -BE:/code/LiteAPPStar2/build --check-stamp-file E:/code/LiteAPPStar2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
