^E:\CODE\LITEAPPSTAR\BUILD\CMAKEFILES\6CED8156163E1D06B8BC9DABF4EA54B5\AUTOUIC_(CONFIG).STAMP.RULE
setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\CODE\LITEAPPSTAR\BUILD\CMAKEFILES\A949E9403C7FC38777131CA2BCB1BE2D\QRC_RESOURCES.CPP.RULE
setlocal
cd E:\code\LiteAPPStar\build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -E cmake_autorcc E:/code/LiteAPPStar/build/CMakeFiles/LiteAPPStar_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\CODE\LITEAPPSTAR\CMAKELISTS.TXT
setlocal
"D:\Program Files\cmake-3.28.1-windows-x86_64\bin\cmake.exe" -SE:/code/LiteAPPStar -BE:/code/LiteAPPStar/build --check-stamp-file E:/code/LiteAPPStar/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
