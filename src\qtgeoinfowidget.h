#ifndef QTGEOINFOWIDGET_H
#define QTGEOINFOWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QGroupBox>
#include <QSplitter>
#include <QTimer>
#include <QTextEdit>
#include <QTabWidget>
#include <QSlider>
#include <QSpinBox>
#include <QCheckBox>
#include <QProgressBar>
#include <QStatusBar>
#include "qtlocationmapview.h"

/**
 * @brief Qt Location 地理信息显示组件
 * 
 * 使用Qt Location框架提供专业的地图显示功能，集成：
 * - 高质量地图显示
 * - 多种地图提供商支持
 * - 标记和几何图形管理
 * - GPS定位功能
 * - 地理编码和反地理编码
 */
class QtGeoInfoWidget : public QWidget
{
    Q_OBJECT

public:
    explicit QtGeoInfoWidget(QWidget *parent = nullptr);
    ~QtGeoInfoWidget();

    // 地图控制接口
    void setCenter(double latitude, double longitude);
    void setZoomLevel(double zoom);
    void addMarker(double latitude, double longitude, const QString &title = "", const QString &description = "");
    void clearMarkers();

signals:
    // 地图事件信号
    void coordinateChanged(double latitude, double longitude);
    void zoomLevelChanged(double zoom);
    void mapClicked(double latitude, double longitude);
    void markerClicked(int markerId, double latitude, double longitude);
    void statusChanged(const QString &status);

public slots:
    // 地图操作槽函数
    void jumpToCoordinate(double latitude, double longitude);
    void getCurrentLocation();
    void switchMapType(const QString &mapType);
    void zoomIn();
    void zoomOut();
    void resetView();
    void fitToMarkers();

private slots:
    // UI事件槽函数
    void onCoordinateInputChanged();
    void onJumpButtonClicked();
    void onLocationButtonClicked();
    void onMapTypeChanged(const QString &mapType);
    void onAddMarkerClicked();
    void onClearMarkersClicked();
    void onExportDataClicked();
    void onImportDataClicked();
    
    // 地图事件槽函数
    void onMapCenterChanged(double latitude, double longitude);
    void onMapZoomChanged(double zoom);
    void onMapClicked(double latitude, double longitude);
    void onMapDoubleClicked(double latitude, double longitude);
    void onMarkerClicked(int markerId, double latitude, double longitude);

private:
    // UI初始化
    void setupUI();
    void createMapArea();
    void createControlTabs();
    void createMapControlTab();
    void createLocationTab();
    void createMarkersTab();
    void createDataTab();
    void createStatusBar();
    void connectSignals();

    // 功能方法
    void updateCoordinateDisplay(double latitude, double longitude);
    void updateZoomDisplay(double zoom);
    void updateMapTypeDisplay(const QString &mapType);
    void updateMarkersList();
    void updateStatus(const QString &status);
    void exportMarkers();
    void importMarkers();
    void validateCoordinateInput();

    // UI组件
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    
    // 地图区域
    QtLocationMapView *m_mapView;
    
    // 控制面板
    QTabWidget *m_controlTabs;
    
    // 地图控制标签页
    QWidget *m_mapControlTab;
    QComboBox *m_mapTypeCombo;
    QSlider *m_zoomSlider;
    QSpinBox *m_zoomSpinBox;
    QPushButton *m_zoomInBtn;
    QPushButton *m_zoomOutBtn;
    QPushButton *m_resetViewBtn;
    QPushButton *m_fitMarkersBtn;
    
    // 位置控制标签页
    QWidget *m_locationTab;
    QLineEdit *m_latitudeEdit;
    QLineEdit *m_longitudeEdit;
    QPushButton *m_jumpBtn;
    QPushButton *m_locationBtn;
    QLabel *m_currentCoordLabel;
    QLabel *m_mouseCoordLabel;
    
    // 标记管理标签页
    QWidget *m_markersTab;
    QPushButton *m_addMarkerBtn;
    QPushButton *m_clearMarkersBtn;
    QTextEdit *m_markersListText;
    QLineEdit *m_markerTitleEdit;
    QLineEdit *m_markerDescEdit;
    
    // 数据管理标签页
    QWidget *m_dataTab;
    QPushButton *m_exportBtn;
    QPushButton *m_importBtn;
    QTextEdit *m_dataInfoText;
    QProgressBar *m_progressBar;
    
    // 状态栏
    QStatusBar *m_statusBar;
    QLabel *m_coordStatusLabel;
    QLabel *m_zoomStatusLabel;
    QLabel *m_mapTypeStatusLabel;
    QLabel *m_markersCountLabel;
    
    // 数据成员
    double m_currentLatitude;
    double m_currentLongitude;
    double m_currentZoom;
    QString m_currentMapType;
    int m_markersCount;
    
    // 常量
    static const double DEFAULT_LATITUDE;
    static const double DEFAULT_LONGITUDE;
    static const double DEFAULT_ZOOM;
    static const int CONTROL_PANEL_WIDTH;
};

#endif // QTGEOINFOWIDGET_H
