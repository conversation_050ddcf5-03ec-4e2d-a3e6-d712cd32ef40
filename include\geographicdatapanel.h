#ifndef GEOGRAPHICDATAPANEL_H
#define GEOGRAPHICDATAPANEL_H

#include <QWidget>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QLabel>
#include <QProgressBar>
#include <QTabWidget>
#include <QPushButton>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QMouseEvent>
#include <QPoint>
#include <QMap>
#include <QPair>
#include <QDateTime>

QT_BEGIN_NAMESPACE
namespace Ui
{
    class GeographicDataPanel;
}
QT_END_NAMESPACE

/**
 * @brief 地理数据展示面板
 *
 * 这是一个浮动在地图上的数据展示面板，包含用户信息表格和统计信息。
 * 面板具有半透明效果，可以拖拽移动，支持显示/隐藏动画。
 */
class GeographicDataPanel : public QWidget
{
    Q_OBJECT

public:
    explicit GeographicDataPanel(QWidget *parent = nullptr);
    ~GeographicDataPanel();

    /**
     * @brief 设置面板在父窗口中的位置
     * @param position 位置（Bottom, Right, Custom）
     */
    enum Position
    {
        Bottom, // 底部居中
        Right,  // 右侧居中
        Custom  // 自定义位置
    };

    void setPosition(Position position);
    void setCustomPosition(const QPoint &pos);

    /**
     * @brief 自适应窗口宽度
     */
    void adjustToParentWidth();

    /**
     * @brief 更新表格列宽以适应面板宽度
     */
    void updateTableLayout();

    /**
     * @brief 显示/隐藏面板（带动画效果）
     */
    void showPanel();
    void hidePanel();
    void togglePanel();

    /**
     * @brief 设置面板透明度
     * @param opacity 透明度 (0.0-1.0)
     */
    void setOpacity(qreal opacity);

    /**
     * @brief 更新数据
     */
    void refreshData();

    /**
     * @brief 设置表格数据
     * @param data 表格数据列表，每行是一个QStringList
     */
    void setTableData(const QList<QStringList> &data);

    /**
     * @brief 更新统计信息
     * @param totalRecords 总记录数
     * @param progress 加载进度 (0-100)
     * @param regionDistribution 地理区域分布
     * @param coordinateRange 坐标范围
     * @param timeDistribution 时间分布
     */
    void updateStatistics(int totalRecords,
                          int progress,
                          const QString &regionDistribution,
                          const QString &coordinateRange,
                          const QString &timeDistribution);

signals:
    /**
     * @brief 面板关闭信号
     */
    void panelClosed();

    /**
     * @brief 数据刷新请求信号
     */
    void refreshRequested();

    /**
     * @brief 表格行选中信号
     * @param row 选中的行号
     * @param data 行数据
     */
    void rowSelected(int row, const QStringList &data);

protected:
    /**
     * @brief 绘制事件（用于半透明效果）
     */
    void paintEvent(QPaintEvent *event) override;

    /**
     * @brief 大小调整事件
     */
    void resizeEvent(QResizeEvent *event) override;

private slots:
    /**
     * @brief UI事件槽函数
     */
    void onTableRowClicked(int row, int column);
    void onTabChanged(int index);

    /**
     * @brief 动画完成槽函数
     */
    void onShowAnimationFinished();
    void onHideAnimationFinished();

private:
    /**
     * @brief 初始化UI
     */
    void setupUI();
    void setupConnections();
    void setupAnimations();
    void setupTableColumns();

    /**
     * @brief 位置计算
     */
    void updatePosition();
    QPoint calculatePosition(Position position);

    /**
     * @brief 数据处理
     */
    void loadSampleData();
    void updateTableData();
    void calculateStatistics();
    QMap<QString, int> calculateRegionDistribution(const QList<QPair<double, double>> &coordinates);
    QString formatCoordinateRange(const QList<QPair<double, double>> &coordinates);

    /**
     * @brief 样式设置
     */
    void applyStyles();
    void updateTableStyles();

private:
    Ui::GeographicDataPanel *ui;

    // 位置和动画
    Position m_position;
    QPoint m_customPosition;
    QPoint m_dragStartPosition;
    bool m_dragging;
    bool m_isVisible;

    // 动画效果
    QPropertyAnimation *m_showAnimation;
    QPropertyAnimation *m_hideAnimation;
    QGraphicsOpacityEffect *m_opacityEffect;

    // 数据存储
    QList<QStringList> m_tableData;
    QList<QPair<double, double>> m_coordinates;

    // 统计信息
    int m_totalRecords;
    int m_loadProgress;
    QString m_regionDistribution;
    QString m_coordinateRange;
    QString m_timeDistribution;

    // 定时器
    QTimer *m_refreshTimer;

    // 常量
    static const int ANIMATION_DURATION = 300;
    static const int DEFAULT_WIDTH = 800;
    static const int DEFAULT_HEIGHT = 300;
    static const int MARGIN = 20;
};

#endif // GEOGRAPHICDATAPANEL_H
