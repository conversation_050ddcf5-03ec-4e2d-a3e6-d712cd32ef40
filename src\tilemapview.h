#ifndef TILEMAPVIEW_H
#define TILEMAPVIEW_H

#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsPixmapItem>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QTimer>
#include <QHash>
#include <QPoint>
#include <QPixmap>
#include "localtilemanager.h"
#include <QWheelEvent>
#include <QMouseEvent>
#include <cmath>

/**
 * @brief 瓦片地图视图组件
 *
 * 基于QGraphicsView实现的瓦片地图显示组件，支持：
 * - OpenStreetMap瓦片加载
 * - 缩放和平移交互
 * - 坐标转换
 * - 瓦片缓存
 */
class TileMapView : public QGraphicsView
{
    Q_OBJECT

public:
    explicit TileMapView(QWidget *parent = nullptr);
    ~TileMapView();

    // 地图操作
    void setCenter(double lat, double lng);
    void setZoomLevel(int zoom);
    int getZoomLevel() const { return m_zoomLevel; }

    // 坐标转换
    QPointF latLngToPixel(double lat, double lng) const;
    QPair<double, double> pixelToLatLng(const QPointF &pixel) const;
    QPointF latLngToViewPixel(double lat, double lng) const;
    bool isCoordinateVisible(double lat, double lng) const;

    // 获取当前中心坐标
    QPair<double, double> getCenter() const;

signals:
    void coordinateClicked(double lat, double lng);
    void centerChanged(double lat, double lng);
    void zoomChanged(int zoom);
    void mouseCoordinateChanged(double lat, double lng);

protected:
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onTileDownloaded();
    void updateVisibleTiles();
    void onLocalTileDownloaded(int x, int y, int z, bool success);
    void onDownloadProgress(int current, int total);

public:
    // 瓦片结构（需要公开以便哈希函数访问）
    struct TileKey
    {
        int x, y, z;
        bool operator==(const TileKey &other) const
        {
            return x == other.x && y == other.y && z == other.z;
        }
    };

private:
    // 瓦片项
    struct TileItem
    {
        QGraphicsPixmapItem *item;
        bool loading;
        qint64 lastAccessed;
        TileItem() : item(nullptr), loading(false), lastAccessed(0) {}
    };

    // 核心功能
    void initializeMap();
    void loadTile(int x, int y, int z);
    void loadVisibleTiles();
    void checkAndDownloadTiles();
    void preloadTiles(); // 预加载周围瓦片
    void createTestTiles();
    void testSingleTileDownload();
    QPixmap createTestTile(int x, int y, int z);
    QString getTileUrl(int x, int y, int z) const;
    QPoint latLngToTile(double lat, double lng, int zoom) const;
    QPair<double, double> tileToLatLng(int x, int y, int zoom) const;

    // 视图管理
    void updateView();
    void clearOldTiles();

    // 成员变量
    QGraphicsScene *m_scene;
    QNetworkAccessManager *m_networkManager;
    QTimer *m_updateTimer;
    LocalTileManager *m_tileManager;

    // 地图状态
    double m_centerLat;
    double m_centerLng;
    int m_zoomLevel;

    // 瓦片管理
    QHash<QString, TileItem> m_tiles;
    QHash<QNetworkReply *, TileKey> m_pendingTiles;

    // 交互状态
    bool m_dragging;
    QPoint m_lastPanPoint;
    QPoint m_clickStartPoint; // 记录点击起始位置，用于区分点击和拖拽

    // 常量
    static const int TILE_SIZE = 256;
    static const int MAX_CACHED_TILES = 200; // 最大缓存瓦片数量
    static const int MIN_ZOOM = 4;           // 最小缩放级别（中国全境）
    static const int MAX_ZOOM = 18;          // 最大缩放级别
};

// 为TileKey提供哈希函数
inline uint qHash(const TileMapView::TileKey &key, uint seed = 0)
{
    return qHash(QString("%1_%2_%3").arg(key.x).arg(key.y).arg(key.z), seed);
}

#endif // TILEMAPVIEW_H
