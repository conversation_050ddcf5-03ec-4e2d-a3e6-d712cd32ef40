#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QFile>
#include <QDir>
#include <QDebug>
#include <QStandardPaths>
#include <QCoreApplication>

/**
 * @brief 数据库配置结构体
 */
struct DatabaseConfig
{
    QString type;         // 数据库类型 (QMYSQL, QPSQL, etc.)
    QString host;         // 主机地址
    int port;             // 端口号
    QString databaseName; // 数据库名
    QString username;     // 用户名
    QString password;     // 密码
    QString tableName;    // 表名
    QString charset;      // 字符集
    QString timezone;     // 时区

    // 默认构造函数
    DatabaseConfig() : port(3306) {}

    // 验证配置有效性
    bool isValid() const
    {
        return !type.isEmpty() && !host.isEmpty() &&
               port > 0 && port <= 65535 &&
               !databaseName.isEmpty() && !username.isEmpty() &&
               !tableName.isEmpty();
    }

    // 转换为字符串（用于调试）
    QString toString() const
    {
        return QString("DatabaseConfig{type=%1, host=%2:%3, db=%4, user=%5, table=%6}")
            .arg(type)
            .arg(host)
            .arg(port)
            .arg(databaseName)
            .arg(username)
            .arg(tableName);
    }
};

/**
 * @brief 连接池配置结构体
 */
struct ConnectionPoolConfig
{
    int maxConnections;    // 最大连接数
    int minConnections;    // 最小连接数
    int connectionTimeout; // 连接超时时间(秒)
    int idleTimeout;       // 空闲超时时间(秒)
    int retryAttempts;     // 重试次数
    int retryDelay;        // 重试延迟(毫秒)

    // 默认构造函数
    ConnectionPoolConfig()
        : maxConnections(10), minConnections(2), connectionTimeout(30),
          idleTimeout(300), retryAttempts(3), retryDelay(1000) {}
};

/**
 * @brief 数据库选项配置结构体
 */
struct DatabaseOptions
{
    bool autoReconnect; // 自动重连
    QString sslMode;    // SSL模式
    int connectTimeout; // 连接超时
    int readTimeout;    // 读取超时
    int writeTimeout;   // 写入超时
    bool enableLogging; // 启用日志
    QString logLevel;   // 日志级别

    // 默认构造函数
    DatabaseOptions()
        : autoReconnect(true), sslMode("disabled"), connectTimeout(10),
          readTimeout(30), writeTimeout(30), enableLogging(true), logLevel("info") {}
};

/**
 * @brief 完整的环境配置结构体
 */
struct EnvironmentConfig
{
    QString name;                        // 环境名称
    DatabaseConfig database;             // 数据库配置
    ConnectionPoolConfig connectionPool; // 连接池配置
    DatabaseOptions options;             // 数据库选项

    // 验证配置有效性
    bool isValid() const
    {
        return !name.isEmpty() && database.isValid();
    }
};

/**
 * @brief 配置管理器类
 *
 * 负责读取、解析、验证和管理数据库配置文件
 * 支持多环境配置、配置验证、错误处理和默认值设置
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 配置文件操作
    bool loadConfig(const QString &configPath = "");
    bool reloadConfig();
    bool saveConfig(const QString &configPath = "");

    // 环境管理
    bool setCurrentEnvironment(const QString &environment);
    QString getCurrentEnvironment() const;
    QStringList getAvailableEnvironments() const;

    // 配置获取
    EnvironmentConfig getCurrentConfig() const;
    EnvironmentConfig getEnvironmentConfig(const QString &environment) const;
    EnvironmentConfig getFallbackConfig() const;

    // 配置验证
    bool validateConfig(const EnvironmentConfig &config, QString *errorMessage = nullptr) const;
    bool validateConfigFile(const QString &configPath, QString *errorMessage = nullptr) const;

    // 配置信息
    QString getConfigVersion() const;
    QString getConfigPath() const;
    bool isConfigLoaded() const;

    // 静态工具方法
    static QString getDefaultConfigPath();
    static EnvironmentConfig createDefaultConfig();

signals:
    void configLoaded(const QString &environment);
    void configChanged(const QString &environment);
    void configError(const QString &error);
    void environmentChanged(const QString &oldEnv, const QString &newEnv);

private slots:
    void onConfigFileChanged();

private:
    // 内部方法
    bool parseConfigFile(const QString &filePath);
    EnvironmentConfig parseEnvironmentConfig(const QJsonObject &envObject) const;
    DatabaseConfig parseDatabaseConfig(const QJsonObject &dbObject) const;
    ConnectionPoolConfig parseConnectionPoolConfig(const QJsonObject &poolObject) const;
    DatabaseOptions parseDatabaseOptions(const QJsonObject &optionsObject) const;

    bool validateEnvironmentConfig(const EnvironmentConfig &config, QString *errorMessage) const;
    bool validateDatabaseConfig(const DatabaseConfig &config, QString *errorMessage) const;
    bool validateConnectionPoolConfig(const ConnectionPoolConfig &config, QString *errorMessage) const;
    bool validateDatabaseOptions(const DatabaseOptions &options, QString *errorMessage) const;

    QString findConfigFile() const;
    void initializeDefaults();
    void logConfigInfo() const;

    // 内部方法（不使用锁）
    bool setCurrentEnvironmentInternal(const QString &environment);

private:
    QString m_configPath;               // 配置文件路径
    QString m_currentEnvironment;       // 当前环境
    QJsonObject m_configData;           // 配置数据
    EnvironmentConfig m_currentConfig;  // 当前配置
    EnvironmentConfig m_fallbackConfig; // 默认配置
    bool m_configLoaded;                // 配置是否已加载
    QString m_configVersion;            // 配置版本

    // 支持的配置值
    QStringList m_supportedDatabaseTypes;
    QStringList m_supportedSslModes;
    QStringList m_supportedLogLevels;
    QStringList m_requiredFields;
};

#endif // CONFIGMANAGER_H
