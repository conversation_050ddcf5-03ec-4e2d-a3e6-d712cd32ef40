<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>LiteAPPStar - 轻量级信号分析工具</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/app.ico</normaloff>:/icons/app.ico</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1400</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件(&amp;F)</string>
    </property>
    <addaction name="actionNew"/>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>


   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>视图(&amp;V)</string>
    </property>
    <addaction name="actionShowSpectrum"/>
    <addaction name="actionShowWaterfall"/>
    <addaction name="actionShowDataTable"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助(&amp;H)</string>
    </property>
    <addaction name="actionAbout"/>
    <addaction name="actionAboutQt"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="actionShowSignalAnalysis"/>
   <addaction name="actionShowQgisMap"/>
   <addaction name="actionShowComprehensiveView"/>
   <addaction name="menuView"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>工具栏</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>

  </widget>
  <action name="actionNew">
   <property name="text">
    <string>新建(&amp;N)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionOpen">
   <property name="text">
    <string>打开(&amp;O)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="text">
    <string>保存(&amp;S)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="text">
    <string>另存为(&amp;A)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>退出(&amp;X)</string>
   </property>
   <property name="shortcut">
    <string>Alt+F4</string>
   </property>
  </action>



  <action name="actionShowSpectrum">
   <property name="text">
    <string>显示频谱图</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionShowWaterfall">
   <property name="text">
    <string>显示瀑布图</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionShowDataTable">
   <property name="text">
    <string>显示数据表格</string>
   </property>
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </action>

  <action name="actionShowQgisMap">
   <property name="text">
    <string>地理信息(&amp;G)</string>
   </property>
   <property name="toolTip">
    <string>显示QGIS专业GIS地图组件</string>
   </property>
   <property name="shortcut">
    <string>F9</string>
   </property>
  </action>

  <action name="actionAbout">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
  <action name="actionAboutQt">
   <property name="text">
    <string>关于Qt</string>
   </property>
  </action>
  <action name="actionShowSignalAnalysis">
   <property name="text">
    <string>分析(&amp;A)</string>
   </property>
   <property name="toolTip">
    <string>显示信号分析界面</string>
   </property>
   <property name="shortcut">
    <string>F8</string>
   </property>
  </action>
  <action name="actionShowComprehensiveView">
   <property name="text">
    <string>综合阅览(&amp;C)</string>
   </property>
   <property name="toolTip">
    <string>显示综合阅览界面 - 数据查询与展示</string>
   </property>
   <property name="shortcut">
    <string>F10</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
